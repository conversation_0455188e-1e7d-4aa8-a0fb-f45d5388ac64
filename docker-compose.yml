services:
  redis:
    image: docker.1ms.run/library/redis:7-alpine
    container_name: yz-redis
    ports:
      - "6379:6379"
    restart: "no"

  ruoyi-admin:
    build:
      context: .
      dockerfile: Dockerfile
    image: ruoyi-admin:latest
    container_name: yz-ruoyi-admin
    ports:
      - "8090:8090"
    environment:
      - SPRING_REDIS_HOST=redis
    restart: "no"
    volumes:
      - ./logs:/app/logs:z
      - ./.m2:/root/.m2:z
    user: "0"
