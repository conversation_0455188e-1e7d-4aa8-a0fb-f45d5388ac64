<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.core.mapper.PatientMapper">

    <select id="getPatientList" resultType="java.util.Map">
        SELECT
        m.*,
        CONCAT_WS(',',
        c1.zdmc,
        c2.zdmc,
        c3.zdmc,
        c4.zdmc
        ) AS linczd_names,
        IFNULL(deb.leaf_id, '') as bingclscId,
        IFNULL(dej.leaf_id, '') as jiuyjlscId
        FROM data_ext_menzbl m
        LEFT JOIN data_ext_linczd c1 ON m.linczd1 = c1.leaf_id
        LEFT JOIN data_ext_linczd c2 ON m.linczd2 = c2.leaf_id
        LEFT JOIN data_ext_linczd c3 ON m.linczd3 = c3.leaf_id
        LEFT JOIN data_ext_linczd c4 ON m.linczd4 = c4.leaf_id
        LEFT JOIN data_ext_binclsc deb on m.leaf_id = deb.guanlmzbl
        LEFT JOIN data_ext_jiuyjlsc dej on m.leaf_id = dej.guanlmzbl
        <where>
            <if test="xingming != null and xingming != ''">
                AND m.xingming LIKE CONCAT('%', #{xingming}, '%')
            </if>
            <if test="chuangwNo != null and chuangwNo != ''">
                AND m.chuangw_no LIKE CONCAT('%', #{chuangwNo}, '%')
            </if>
            <if test="zhuyuanh != null and zhuyuanh != ''">
                AND m.zhuyuanh LIKE CONCAT('%', #{zhuyuanh}, '%')
            </if>
        </where>
        ORDER BY
            m.c_time DESC,m.leaf_id DESC
    </select>
</mapper>