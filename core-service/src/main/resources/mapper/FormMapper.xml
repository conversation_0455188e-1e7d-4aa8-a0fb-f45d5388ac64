<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.core.mapper.FormMapper">

    <select id="formList" resultType="java.util.Map">
        SELECT
        *
        FROM
        (
        SELECT
        *
        FROM
        (
        SELECT
        ta.*,
        tb.app_sname,
        tc.user_name AS x_creator,
        td.user_name AS x_upuser
        FROM
        (
        SELECT
        main_code,
        is_sys,
        company_id,
        share_auto_approve,
        is_share,
        data_sname,
        open_invent,
        app_id,
        data_code,
        data_status,
        data_name,
        data_type,
        creator,
        upuser,
        input_time,
        create_time,
        is_anony,
        0 AS app_share,
        data_state->>'$.status' AS data_state
        FROM
        node_ext_data
        WHERE
        ((
        data_type IN ( 0 )
        <if test="appId != null and appId != ''">
            AND app_id = #{appId}
        </if>
        )
        OR data_type IN ( 2, 8 ))
        <if test="state != null and state != ''">
            AND data_state ->> '$.status' = #{state}
        </if>
        UNION ALL
        SELECT
        main_code,
        is_sys,
        company_id,
        share_auto_approve,
        is_share,
        data_sname,
        open_invent,
        app_id,
        data_code,
        data_status,
        data_name,
        data_type,
        creator,
        upuser,
        input_time,
        create_time,
        is_anony,
        1 AS app_share,
        data_state->>'$.status' AS data_state
        FROM
        node_ext_data ta
        WHERE
        data_type IN ( 0 )
        <if test="appId != null and appId != ''">
            AND ta.app_id != #{appId}
        </if>
        AND data_code IN (
        SELECT
        data_id
        FROM
        pso_auth_share ta
        LEFT JOIN pso_app_share tb ON ta.share_id = tb.share_id
        WHERE
        share_type = 0
        <if test="appId != null and appId != ''">
            AND ta.auth_app = #{appId}}
        </if>
        )
        <if test="state != null and state != ''">
            AND data_state ->> '$.status' = #{state}
        </if>
        ) ta
        LEFT JOIN pso_up_app tb ON ta.app_id = tb.app_id
        LEFT JOIN sys_user tc ON ta.creator = tc.user_name
        LEFT JOIN sys_user td ON ta.upuser = td.user_name
        ) ta
        WHERE
        data_code IN (
        SELECT
        node_data_id
        FROM
        PSO_NODE_DATA
        WHERE
        node_id IN ( SELECT node_id FROM pso_node_net WHERE node_id = #{dataCode} OR node_path LIKE CONCAT('%',
        #{dataCode}, '%') ))
        <if test="appId != null and appId != ''">
            AND app_id = #{appId}
        </if>
        <if test="dataSName != null and dataSName != ''">
            AND data_sname LIKE CONCAT('%', #{dataSName}, '%')
        </if>
        ORDER BY
        create_time DESC
        ) ta
    </select>

    <select id="flowList" resultType="java.util.Map">
        SELECT *
        FROM (
        SELECT
        ta.*,
        app_sname,
        tc.user_name AS x_creator,
        td.user_name AS x_upuser
        FROM (
        SELECT
        wf.app_id,
        wf.wf_code,
        wf.wf_name,
        wf.wf_sname,
        wf.is_pub,
        wf.map_data_code,
        wf.wf_map_tp,
        wf.wf_auth_type,
        wf.empty_type,
        wf.wf_order,
        wf.wf_matter_able,
        wf.wf_urge_able,
        wf.wf_comment_able,
        wf.wf_src_able,
        wf.creator,
        wf.create_time,
        wf.upuser,
        wf.input_time,
        wf.is_share,
        wf.wf_tags,
        wf.open_preju,
        wf.open_start,
        wf.wf_ext,
        wf.share_auto_approve,
        wf.company_id,
        wf.wf_type,
        0 AS app_share,
        wf.data_state->>'$.status' AS data_state
        FROM
        node_ext_wf wf
        <where>
            <if test="appId != null and appId != ''">
                AND app_id = #{appId}
            </if>
            <if test="state != null and state != ''">
                AND data_state ->> '$.status' = #{state}
            </if>
        </where>
        UNION ALL
        SELECT
        wf.app_id,
        wf.wf_code,
        wf.wf_name,
        wf.wf_sname,
        wf.is_pub,
        wf.map_data_code,
        wf.wf_map_tp,
        wf.wf_auth_type,
        wf.empty_type,
        wf.wf_order,
        wf.wf_matter_able,
        wf.wf_urge_able,
        wf.wf_comment_able,
        wf.wf_src_able,
        wf.creator,
        wf.create_time,
        wf.upuser,
        wf.input_time,
        wf.is_share,
        wf.wf_tags,
        wf.open_preju,
        wf.open_start,
        wf.wf_ext,
        wf.share_auto_approve,
        wf.company_id,
        wf.wf_type,
        1 AS app_share,
        wf.data_state->>'$.status' AS data_state
        FROM
        node_ext_wf wf
        WHERE
        wf_code IN (
        SELECT
        data_id
        FROM
        pso_auth_share ta
        LEFT JOIN
        pso_app_share tb ON ta.share_id = tb.share_id
        WHERE
        share_type = 1
        <if test="appId != null and appId != ''">
            AND ta.auth_app = #{appId}
        </if>
        )
        <if test="appId != null and appId != ''">
            app_id != #{appId}
        </if>
        <if test="state != null and state != ''">
            AND data_state ->> '$.status' = #{state}
        </if>
        ) ta
        LEFT JOIN pso_up_app tb ON ta.app_id = tb.app_id
        LEFT JOIN sys_user tc ON ta.creator = tc.user_name
        LEFT JOIN sys_user td ON ta.upuser = td.user_name
        ) pa
        <where>
            wf_code IN (
            SELECT
            node_data_id
            FROM
            PSO_NODE_DATA
            WHERE
            node_id IN ( SELECT node_id FROM pso_node_net WHERE node_id = #{wfCode} OR node_path LIKE CONCAT('%',
            #{wfCode}, '%') ))
            <if test="wfSname != null and wfSname != ''">
                and wf_sname LIKE CONCAT('%', #{wfSname}, '%')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="viewList" resultType="java.util.Map">
        SELECT
        *
        FROM
        (
        SELECT
        ta.*,
        app_sname,
        tc.user_name AS x_creator,
        td.user_name AS x_upuser
        FROM
        (
        SELECT
        app_id, view_code, view_name, view_sname, view_status,
        view_design, pub_config, driver_id, creator, create_time,
        upuser, input_time, is_share, is_mult, is_entity,
        is_sys, company_id, data_state->>'$.status' AS data_state
        FROM
        node_ext_view
        <where>
            <if test="appId != null and appId != ''">
                is_sys = 1 OR app_id = #{appId}
            </if>
            <if test="state != null and state != ''">
                AND data_state ->> '$.status' = #{state}
            </if>
        </where>
        UNION ALL
        SELECT
        app_id, view_code, view_name, view_sname, view_status,
        view_design, pub_config, driver_id, creator, create_time,
        upuser, input_time, is_share, is_mult, is_entity,
        is_sys, company_id, data_state->>'$.status' AS data_state
        FROM
        node_ext_view
        WHERE
        view_code IN (
        SELECT
        data_id
        FROM
        pso_auth_share ta
        LEFT JOIN pso_app_share tb ON ta.share_id = tb.share_id
        WHERE
        share_type = 3
        <if test="appId != null and appId !=''">
            and ta.auth_app = #{appId}
        </if>
        )
        <if test="state != null and state != ''">
            AND data_state ->> '$.status' = #{state}
        </if>
        <if test="appId != null and appId !=''">
            and app_id != #{appId}
        </if>
        ) ta
        LEFT JOIN pso_up_app tb ON ta.app_id = tb.app_id
        LEFT JOIN sys_user tc ON ta.creator = tc.user_name
        LEFT JOIN sys_user td ON ta.upuser = td.user_name
        ) pa
        WHERE
        view_code IN ( SELECT node_data_id FROM PSO_NODE_DATA WHERE node_id IN ( SELECT node_id FROM pso_node_net WHERE
        node_id = #{viewCode} OR node_path LIKE concat('%', #{viewCode}, '%') ) )
        <if test="viewSName != null and viewSName != ''">
            and view_sname LIKE CONCAT('%', #{viewSName}, '%')
        </if>
        ORDER BY
        create_time DESC
    </select>
</mapper>