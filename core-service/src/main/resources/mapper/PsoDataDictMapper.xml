<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.core.mapper.PsoDataDictMapper">
    <resultMap type="com.ruoyi.core.entity.PsoDataDict" id="PsoDataDictResult">
    	<id     property="AppId"      column="app_id"      />
        <result property="AutoNo"    column="auto_no"    />
        <result property="Code"     column="data_code"     />
        <result property="Field"   column="field_name"   />
        <result property="FieldType"    column="field_type"    />
        <result property="FieldLen"      column="field_len"      />
        <result property="FieldFormat"    column="field_format"    />
        <result property="OutFormat"      column="output_format"      />
        <result property="ControlType"    column="control_type"    />
        <result property="IsSys"    column="is_sys"    />
        <result property="IsEncry"    column="is_encry"    />
        <result property="IsPkey"    column="is_pkey"    />
        <result property="IsTitle"    column="is_title"    />
        <result property="OpenFail"    column="open_fail"    />
        <result property="OpenSense"    column="open_sense"    />
        <result property="FieldConfig"    column="field_config"    />
        <result property="BindDimen"    column="bind_dimen"    />
        <result property="IsSecret"    column="is_secret"    />
        <result property="EncryType"    column="encry_type"    />
        <result property="FieldDisplay"    column="field_display"    />
    </resultMap>

    <select id="selectDictList" parameterType="String" resultMap="PsoDataDictResult">
        select * from pso_data_dict where is_sys!=1 and data_code= #{Code}
    </select>

    <update id="updateExistDict" parameterType="com.ruoyi.core.entity.PsoDataDict">
        update PSO_DATA_DICT
        <set>
            app_id=#{AppId},
            field_name = #{Field},field_type = #{FieldType},field_len = #{FieldLen},field_format = #{FieldFormat},
            output_format = #{OutFormat},control_type = #{ControlType},is_encry = #{IsEncry},field_display = #{FieldDisplay},
            is_pkey = #{IsPkey},is_title = #{IsTitle},open_fail = #{OpenFail},open_sense = #{OpenSense},
            field_config = #{FieldConfig},bind_dimen = #{BindDimen},is_secret = #{IsSecret},encry_type = #{EncryType}
        </set>
        where auto_no = #{AutoNo} and  data_code=#{Code}
    </update>
</mapper>