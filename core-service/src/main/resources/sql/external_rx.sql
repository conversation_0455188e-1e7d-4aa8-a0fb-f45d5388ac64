/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 80018 (8.0.18)
 Source Host           : ************:33068
 Source Schema         : yzlc

 Target Server Type    : MySQL
 Target Server Version : 80018 (8.0.18)
 File Encoding         : 65001

 Date: 28/07/2025 12:11:41
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for external_rx
-- ----------------------------
DROP TABLE IF EXISTS `external_rx`;
CREATE TABLE `external_rx`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID（雪花ID）',
  `medical_record_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门诊病历id',
  `prescription_number` bigint(20) NULL DEFAULT NULL COMMENT '处方号',
  `prescription_date` datetime NULL DEFAULT NULL COMMENT '处方日期',
  `drug_stock_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '外配药库存id',
  `drug_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品名称',
  `drug_spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品规格',
  `drug_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品单位',
  `status` int(11) NOT NULL COMMENT '状态',
  `part` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '用药部位',
  `administration_route` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '给药途径',
  `administration_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '给药方法',
  `prescription_quantity` int(11) NULL DEFAULT NULL COMMENT '药品数量',
  `week_day` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '星期',
  `morning_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '早（数量）',
  `morning_time` time NULL DEFAULT NULL COMMENT '早（时间）',
  `noon_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '中（数量）',
  `noon_time` time NULL DEFAULT NULL COMMENT '中（时间）',
  `evening_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '晚（数量）',
  `evening_time` time NULL DEFAULT NULL COMMENT '晚（时间）',
  `night_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '夜（数量）',
  `night_time` time NULL DEFAULT NULL COMMENT '夜（时间）',
  `prompt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '提示',
  `drug_source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '药品来源',
  `single_dose` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '次用量',
  `single_dose_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '次用量单位',
  `order_date` date NULL DEFAULT NULL COMMENT '医嘱日期',
  `attending_doctor_id` bigint(20) NULL DEFAULT NULL COMMENT '带教医生ID',
  `order_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '医嘱类型',
  `order_time` time NULL DEFAULT NULL COMMENT '医嘱时间',
  `disease_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '长期医嘱的用药类型',
  `doctor_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '医生姓名',
  `nurse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '护士姓名',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除字段（0:未删除 1:已删除）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '外配药医嘱表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of external_rx
-- ----------------------------
INSERT INTO `external_rx` VALUES (1940646064981884928, '1939945677945696256', NULL, NULL, 'p1745392720019krlvd0ar06zfuguv6b', '阿昔洛韦滴眼液', '8ml:8mg/支,1支/盒', '支', 0, '腿部', '外用（自理）', 'Qd', 1, '', 8.00, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '慢滴', '家属', '早8ml', 'ml', '2025-07-01', 100006, '长期', '15:14:03', '慢性病用药', '100006', '100007', 1, '2025-07-03 13:37:35', 'admin', '2025-07-04 00:43:42', 'admin');
INSERT INTO `external_rx` VALUES (1940813796847251456, '1939945677945696256', NULL, NULL, 'p17453926421755qsji4mved2tjw7ajb', '阿普唑仑片', '0.4mg*24/盒,铝塑包装', '盒', 0, '腿部', '口服', 'Qd', NULL, '', 0.40, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '明起', '家属', '早0.4mg', 'mg', '2025-07-01', NULL, '长期', '15:14:03', '慢性病用药', '', '', 1, '2025-07-04 00:44:06', 'admin', '2025-07-04 00:47:44', 'admin');
INSERT INTO `external_rx` VALUES (1940814797218111488, '1939945677945696256', 902786, '2025-07-15 13:37:17', 'p17453926421755qsji4mved2tjw7ajb', '阿普唑仑片', '0.4mg*24/盒,铝塑包装', '盒', 1, '', '口服', 'Qd', NULL, '', 0.40, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '慢滴', '家属', '0.4mg', 'mg', '2025-07-01', NULL, '长期', '15:14:03', '慢性病用药', '', '', 0, '2025-07-04 00:48:05', 'admin', '2025-07-15 13:37:17', 'admin');
INSERT INTO `external_rx` VALUES (1940817291851722752, '1939945677945696256', 902786, '2025-07-15 13:37:17', 'p17453925183313lcj63g61f5631ecr0', '阿卡波糖咀嚼片', '50mg*60片', '盒', 1, '', '口服', 'Qd', NULL, '', 50.00, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '慢滴', '家属', '50mg', 'mg', '2025-07-01', NULL, '长期', '15:14:03', '慢性病用药', '', '', 0, '2025-07-04 00:58:00', 'admin', '2025-07-15 13:37:17', 'admin');
INSERT INTO `external_rx` VALUES (1947537429288202240, '1933444931121078272', NULL, NULL, 'p17453926897893ymf7tbq7f1vm2z5t4', '阿司匹林肠溶片', '0.1g*30片/盒/盒,铝塑包装', '盒', 0, '', '口服', '', NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '明起', '家属', '0.1', 'g', '2025-06-13', NULL, '长期', '16:42:35', '', '', '', 0, '2025-07-22 14:01:21', 'admin', '2025-07-22 14:01:21', '');
INSERT INTO `external_rx` VALUES (1947538065891274752, '1939616071042895872', NULL, NULL, 'p1745392720019krlvd0ar06zfuguv6b', '阿昔洛韦滴眼液', '8ml:8mg/支,1支/盒', '支', 0, '', '外用（自理）', '', 1, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '家属', '8ml', 'ml', '2025-06-30', NULL, '长期', '17:23:46', '', '', '', 0, '2025-07-22 14:03:53', 'admin', '2025-07-22 14:03:53', '');
INSERT INTO `external_rx` VALUES (1947538491155951616, '1939616071042895872', NULL, NULL, 'p1745392664205s2on7kwsprinppl86z', '阿奇霉素片', '250mg×4片/盒,铝塑水泡眼', '盒', 0, '', '口服', '', 1, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '家属', '250', 'mg', '2025-06-30', NULL, '长期', '17:23:46', '慢性病用药', '', '', 0, '2025-07-22 14:05:34', 'admin', '2025-07-22 14:05:34', '');
INSERT INTO `external_rx` VALUES (1949405530644549632, '1949393563313823744', NULL, NULL, 'p17453926421755qsji4mved2tjw7ajb', '阿普唑仑片', '0.4mg*24/盒,铝塑包装', '盒', 4, '', '口服', 'Qd', 1, '', 0.40, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '', '家属', '早0.4mg', 'mg', '2025-07-27', NULL, '长期', '16:56:39', '', '', '', 0, '2025-07-27 17:44:32', 'admin', '2025-07-28 11:28:47', 'admin');

SET FOREIGN_KEY_CHECKS = 1;
