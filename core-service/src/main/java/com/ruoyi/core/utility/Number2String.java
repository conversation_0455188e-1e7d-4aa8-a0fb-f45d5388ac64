package com.ruoyi.core.utility;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

public class Number2String {
    /**
     * Function实现，将Map中所有value为Integer或Long的值转换为String类型
     */
    public static final Function<Map<String, Object>, Map<String, Object>> number2String4Map = map -> {
        if (map == null) return null;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof Integer || value instanceof Long) {
                entry.setValue(String.valueOf(value));
            }
        }
        return map;
    };

    /**
     * Function实现，将List<Map<String, Object>>中所有value为Integer或Long的值转换为String类型
     */
    public static final Function<List<Map<String, Object>>, List<Map<String, Object>>> number2String4ListMap = list -> {
        if (list == null) return null;
        for (Map<String, Object> map : list) {
            number2String4Map.apply(map);
        }
        return list;
    };
}
