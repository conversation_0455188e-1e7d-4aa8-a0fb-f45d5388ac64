package com.ruoyi.core.entity;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RoleAuthStruct {
    /**
     * 主键ID
     */
    private String autoNo;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 权限类型
     */
    private String authType;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 授权类型（数据库有默认值0）
     */
    private Integer grantType;
}
