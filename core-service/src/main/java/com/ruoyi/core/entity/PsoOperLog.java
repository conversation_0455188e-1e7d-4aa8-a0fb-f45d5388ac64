package com.ruoyi.core.entity;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PsoOperLog {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String autoNo;

    /**
     * 日志类型
     */
    private String logType;

    /**
     * 操作用户
     */
    private String logUser;

    /**
     * 操作时间
     */
    private String logTime;

    /**
     * 日志内容
     */
    private String logContent;

    /**
     * 数据编码
     */
    private String dataCode;

    /**
     * 节点ID
     */
    private String leafId;

    /**
     * 日志数据
     */
    private String logData;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 历史数据
     */
    private String hisData;
}
