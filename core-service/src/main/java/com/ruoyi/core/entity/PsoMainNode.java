package com.ruoyi.core.entity;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PsoMainNode {
    // 原项目中对某些值进行了默认值处理
    private static final long serialVersionUID = 1L;
    private String appId;        // 应用ID
    private String nodeId;       // 节点ID（主键）
    private String nodeName;     // 节点名称
    private String nodeSname;    // 节点简称
    private String nodeType;     // 节点类型
    private Integer nodeStatus = 1;  // 节点状态
    private String nodeNote = "";     // 节点备注
    private String nodeIcon = "";     // 节点图标
    private Integer nodeTag = 0;     // 节点标签
    private String createTime = DateUtil.now();  // 创建时间
    private Integer nodePos = 0;     // 节点位置
    private Integer nodeAuth = 0;    // 节点权限
    private String nodeRenter = "";  // 节点租户
    private String nodeCate = "0";    // 节点分类
    private String companyId = "";   // 公司ID
    private String creator;     // 创建人
}
