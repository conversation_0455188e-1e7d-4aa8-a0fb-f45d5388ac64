package com.ruoyi.core.entity;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PsoDataDict {
    public String AppId;

    public String AutoNo;

    public String Code;

    public String Field;

    public String FieldDisplay;

    public String FieldType;

    public int FieldLen;

    public String FieldFormat;

    public String OutFormat;

    public String ControlType="";

    public int IsSys;

    public String IsEncry;

    public String IsPkey;

    public int IsTitle;

    public int OpenFail;

    public int OpenSense;

    public String FieldConfig;

    public String BindDimen;

    public int IsSecret;

    public String EncryType;

    public PsoDataDict(){
        this.setAppId("");
        this.setControlType("");
        this.setOutFormat("");
        this.setIsSys(1);
        this.setIsEncry("1");
        this.setIsPkey("0");
        this.setIsTitle(0);
        this.setOpenFail(0);
        this.setOpenSense(0);
        this.setFieldConfig("{}");
        this.setBindDimen("0");
        this.setIsSecret(0);
        this.setEncryType("");
    }

    public PsoDataDict(int sys){
        this.setAppId("");
        this.setControlType("");
        this.setOutFormat("");
        this.setIsSys(sys);
        this.setIsEncry("1");
        this.setIsPkey("0");
        this.setIsTitle(0);
        this.setOpenFail(0);
        this.setOpenSense(0);
        this.setFieldConfig("{}");
        this.setBindDimen("0");
        this.setIsSecret(0);
        this.setEncryType("");
        this.setControlType("common");
    }

    public PsoDataDict(JSONObject field) {
        this.AppId = "";
        this.AutoNo = field.getStr("fid");
        this.Field = field.getStr("dbId");
        this.FieldType = field.getStr("dbType");
        this.FieldLen =Integer.parseInt(field.getOrDefault("dbLen","0").toString()) ;
        if (field.get("dbFormat") == null ){
            this.FieldFormat = "common";
        }else{
            this.FieldFormat = field.getStr("dbFormat");
        }
        if (field.get("dbFormat") != null ){
            this.FieldFormat = field.getStr("wid");
        }
        this.OutFormat = field.getOrDefault("output_format","").toString();
        if ("subform".equals(this.ControlType) || this.ControlType.equals("asstable")) {
            this.OutFormat = field.getOrDefault("assForm","").toString();
        }else if ("time".equals(this.ControlType)) {
            this.OutFormat = field.getOrDefault("fmode","").toString();
        }else if(this.ControlType.equals("user") || this.ControlType.equals("attachment")|| this.ControlType.equals("seal") || this.ControlType.equals("tag")){
            this.OutFormat =field.getOrDefault("virConfig","").toString();
            this.FieldFormat = "common";
        }else if ("select".equals(this.ControlType) || "checkbox".equals(this.ControlType)) {
            if (field.get("srcType").toString().equals("1")){  //固定值
                this.OutFormat = field.getOrDefault("opts","[]").toString();
            }else{ //复选框
                this.OutFormat = field.getOrDefault("srcCode","").toString();
            }
        }
        //subform 子表录入
        //asstable 子表选择
        //attachment 附件
        this.IsEncry= field.getOrDefault("dbEncry","0").toString();
        this.IsPkey= field.getOrDefault("dbPkey","0").toString();
        this.IsTitle= Integer.parseInt(field.getOrDefault("isTitle","0").toString()) ;
        this.OpenFail= Integer.parseInt(field.getOrDefault("isFallible","0").toString()) ;
        this.OpenSense= Integer.parseInt(field.getOrDefault("isSensitive","0").toString());
        this.FieldDisplay= field.getOrDefault("fname","").toString();
        this.FieldConfig=field.toString();
        String dimen= field.getOrDefault("indexTag","[]").toString();
        JSONArray dimenArr= JSONUtil.parseArray(dimen);
        this.BindDimen= dimenArr.isEmpty() ?"":dimenArr.getStr(dimenArr.size()-1);
        this.IsSecret= Integer.parseInt(field.getOrDefault("IsSecret","0").toString());
        this.EncryType= field.getOrDefault("EncryType","").toString();
    }

    @Override
    public boolean equals(Object obj){
        PsoDataDict dict= (PsoDataDict) obj;
        return dict.AutoNo.equals(AutoNo) &&
                dict.Field.equals(Field) &&
                dict.FieldDisplay.equals(FieldDisplay) &&
                dict.FieldType.equals(FieldType) &&
                dict.FieldLen==FieldLen &&
                dict.FieldFormat.equals(FieldFormat) &&
                dict.OutFormat.equals(OutFormat) &&
                dict.ControlType.equals(ControlType) &&
                dict.IsPkey.equals(IsPkey) &&
                dict.IsTitle== IsTitle &&
                dict.OpenFail== OpenFail &&
                dict.OpenSense== OpenSense &&
                dict.IsSecret== IsSecret &&
                dict.EncryType.equals(EncryType)  &&
                dict.IsEncry.equals(IsEncry)  ;
    }
}
