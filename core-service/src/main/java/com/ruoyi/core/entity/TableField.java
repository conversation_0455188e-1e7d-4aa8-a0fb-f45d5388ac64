package com.ruoyi.core.entity;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TableField {
    public String Field;
    public String NewField;
    public String Type;
    public String DefaultValue;
    public int Len = 0;
    public boolean Pk = false;
    public boolean Null = true;
    public String FieldDisplay;

    public TableField() {
    }

    public TableField(String _field) {
        this.Field = _field;
    }


    public TableField(String _field, String _type, int _len) {
        this.Field = _field;
        this.Len = _len;
        this.Type = _type;
    }

    public TableField(String _field, String _type, int _len,String _display) {
        this.Field = _field;
        this.Len = _len;
        this.Type = _type;
        this.FieldDisplay=_display;
    }

    public void KvField(PsoDataDict field) {
        this.Field = String.format("x_%s", field.Field);
        this.Type = "string";
        this.Len = field.FieldLen;
        this.Null = true;
        this.FieldDisplay = field.FieldDisplay;
        this.DefaultValue = "";
    }

    public void PathVField(PsoDataDict field) {
        this.Field = String.format("v_%s", field.Field);
        this.Type = "string";
        this.Len = 50;
        this.Null = true;
        this.FieldDisplay = field.FieldDisplay;
    }

    public void PathNameField(PsoDataDict field) {
        this.Field = String.format("path_%s", field.Field);
        this.Type = "string";
        this.Len = 200;
        this.Null = true;
        this.FieldDisplay = field.FieldDisplay + "路径";
        this.DefaultValue = "";
    }

    public void CalUnitField(PsoDataDict field) {
        this.Field = String.format("u_%s", field.Field);
        this.Type = "string";
        this.Len = 20;
        this.Null = true;
        this.FieldDisplay = field.FieldDisplay + "单位";
        this.DefaultValue = "";
    }

    public void CalBasicField(PsoDataDict field) {
        this.Field = String.format("bs_%s", field.Field);
        this.Type = "string";
        this.Len = 20;
        this.Null = true;
        this.FieldDisplay = field.FieldDisplay + "基本单位";
        this.DefaultValue = "";
    }

    public void CalTransField(PsoDataDict field) {
        this.Field = String.format("bv_%s", field.Field);
        this.Type = "string";
        this.Len = 20;
        this.Null = true;
        this.FieldDisplay = field.FieldDisplay + "转换值";
        this.DefaultValue = "";
    }

    public void CalMultField(PsoDataDict field) {
        this.Field = String.format("br_%s", field.Field);
        this.Type = "string";
        this.Len = 20;
        this.Null = true;
        this.FieldDisplay = field.FieldDisplay + "倍数";
        this.DefaultValue = "";
    }
    //endregion
}
