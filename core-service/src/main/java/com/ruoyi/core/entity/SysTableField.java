package com.ruoyi.core.entity;

import cn.hutool.core.date.DateUtil;

import java.util.ArrayList;
import java.util.List;

public class SysTableField {

    public SysTableField() {
    }

    //region  系统字段

    public static TableField Pk() {
        TableField _field=new TableField();
        _field.Field = "leaf_id";
        _field.Type = "string";
        _field.Len = 50;
        _field.Pk = true;
        _field.Null = false;
        _field.FieldDisplay="数据ID";
        _field.DefaultValue="";
        return _field;
    }

    public static TableField AppId() {
        TableField _field=new TableField();
        _field.Field = "app_id";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = false;
        _field.FieldDisplay="应用ID";
        _field.DefaultValue="";
        return _field;
    }

    public static TableField CityId() {
        TableField _field=new TableField();
        _field.Field = "city_id";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = true;
        _field.FieldDisplay = "城市ID";
        _field.DefaultValue="";
        return _field;
    }

    public static TableField GroupId() {
        TableField _field=new TableField();
        _field.Field = "renter_id";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = true;
        _field.FieldDisplay = "群组ID";
        _field.DefaultValue="";
        return _field;
    }

    public static TableField MenuId() {
        TableField _field=new TableField();
        _field.Field = "menu_id";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = true;
        _field.FieldDisplay="菜单ID";
        _field.DefaultValue="";
        return _field;
    }

    public static TableField DataCode() {
        TableField _field=new TableField();
        _field.Field = "data_code";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = true;
        _field.FieldDisplay="表单ID";
        _field.DefaultValue="";
        return _field;
    }

    public static TableField MatterId() {
        TableField _field=new TableField();
        _field.Field = "matter_id";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = true;
        _field.FieldDisplay="事项ID";
        _field.DefaultValue="";
        return _field;
    }

    public static TableField TpId() {
        TableField _field=new TableField();
        _field.Field = "tp_id";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = true;
        _field.FieldDisplay="插件ID";
        _field.DefaultValue="";
        return _field;
    }

    public static TableField Status() {
        TableField _field=new TableField();
        _field.Field = "d_status";
        _field.Type = "int";
        _field.Len = 4;
        _field.Null = false;
        _field.FieldDisplay="状态";
        _field.DefaultValue="0";
        return _field;
    }
    public static TableField Stage() {
        TableField _field=new TableField();
        _field.Field = "d_stage";
        _field.Type = "int";
        _field.Len = 4;
        _field.Null = false;
        _field.FieldDisplay="阶段";
        _field.DefaultValue="0";
        return _field;
    }

    public static TableField Audit() {
        TableField _field=new TableField();
        _field.Field = "d_audit";
        _field.Type = "int";
        _field.Len = 4;
        _field.Null = false;
        _field.FieldDisplay="审核";
        _field.DefaultValue="0";
        return _field;
    }
    public static TableField Creator() {
        TableField _field=new TableField();
        _field.Field = "creator";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = false;
        _field.FieldDisplay="创建人ID";
        _field.DefaultValue="";
        return _field;
    }
    public static TableField CreatorX() {
        TableField _field=new TableField();
        _field.Field = "x_creator";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = true;
        _field.FieldDisplay="创建人姓名";
        _field.DefaultValue="";
        return _field;
    }
    public static TableField SyncStatus() {
        TableField _field=new TableField();
        _field.Field = "sync_status";
        _field.Type = "int";
        _field.Len = 4;
        _field.Null = false;
        _field.FieldDisplay="同步";
        _field.DefaultValue="0";
        return _field;
    }
    public static TableField PubStatus() {
        TableField _field=new TableField();
        _field.Field = "pub_status";
        _field.Type = "int";
        _field.Len = 4;
        _field.Null = true;
        _field.FieldDisplay="发布";
        _field.DefaultValue="0";
        return _field;
    }
    public static TableField ShareStatus() {
        TableField _field=new TableField();
        _field.Field = "share_status";
        _field.Type = "int";
        _field.Len = 4;
        _field.Null = true;
        _field.FieldDisplay="共享";
        _field.DefaultValue="0";
        return _field;
    }
    public static TableField IsChain() {
        TableField _field=new TableField();
        _field.Field = "is_chain";
        _field.Type = "int";
        _field.Len = 4;
        _field.Null = true;
        _field.FieldDisplay="是否上链";
        _field.DefaultValue="0";
        return _field;
    }
    public static TableField CTime() {
        TableField _field=new TableField();
        _field.Field = "c_time";
        _field.Type = "string";
        _field.Len = 20;
        _field.Null = false;
        _field.FieldDisplay="创建时间";
        _field.DefaultValue= DateUtil.now();
        return _field;
    }
    public static TableField Upuser() {
        TableField _field=new TableField();
        _field.Field = "upuser";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = true;
        _field.FieldDisplay="修改人ID";
        _field.DefaultValue="";
        return _field;
    }
    public static TableField UpuserX() {
        TableField _field=new TableField();
        _field.Field = "x_upuser";
        _field.Type = "string";
        _field.Len = 50;
        _field.Null = true;
        _field.FieldDisplay="修改人姓名";
        _field.DefaultValue="";
        return _field;
    }
    public static TableField InputTime() {
        TableField _field=new TableField();
        _field.Field = "input_time";
        _field.Type = "string";
        _field.Len = 20;
        _field.Null = true;
        _field.FieldDisplay="修改时间";
        _field.DefaultValue="";
        return _field;
    }
    public static TableField PubTime() {
        TableField _field=new TableField();
        _field.Field = "pub_time";
        _field.Type = "string";
        _field.Len = 20;
        _field.Null = true;
        _field.FieldDisplay="发布时间";
        return _field;
    }

    //endregion

    public static List<TableField> GetDataSysFields(){
        List<TableField> _list=new ArrayList<>();
        _list.add(AppId());
        _list.add(DataCode());
        _list.add(CityId());
        _list.add(GroupId());
        _list.add(MatterId());
        _list.add(MenuId());
        _list.add(TpId());
        _list.add(Pk());
        _list.add(Status());
        _list.add(Stage());
        _list.add(Audit());
        _list.add(SyncStatus());
        _list.add(IsChain());
        _list.add(PubStatus());
        _list.add(ShareStatus());
        _list.add(Creator());
        _list.add(CreatorX());
        _list.add(Upuser());
        _list.add(UpuserX());
        _list.add(CTime());
        _list.add(InputTime());
        _list.add(PubTime());
        return _list;
    }

}
