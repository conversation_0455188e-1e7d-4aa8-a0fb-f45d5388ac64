package com.ruoyi.core.sqltemplate.actuator;

import com.ruoyi.core.entity.TableField;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.List;

public class MySqlTableActuator extends BaseTableActuator {

    public MySqlTableActuator(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(namedParameterJdbcTemplate);
    }
    public MySqlTableActuator() {
        super();
    }
    @Override
    String ChangeFieldType(String _type, int _len) {
        if (_len == 0) _len = 20;
        switch (_type.toLowerCase()) {
            case "int":
            case "bool":
                return "int";
            case "text":
                return "text";
            case "ntext":
                return "mediumtext";
                //return "tinyint";
            case "nstring":
                return String.format("nvarchar(%d)", _len);
            default:
                return String.format("varchar(%d)", _len);
        }
    }

    @Override
    public void AddTableField(List<TableField> fields, String code)  throws Exception {
        MapSqlParameterSource sqlParams = new MapSqlParameterSource();
        for (TableField f : fields) {
            if (f.Field.isEmpty()) continue;
            namedParameterJdbcTemplate.update(String.format("alter table data_ext_%s add column %s %s;",code,
                    f.Field, ChangeFieldType(f.Type, f.Len)), sqlParams);
        }
    }

    @Override
    public void ModifyTableField(List<TableField> fields,String code) throws Exception {
        MapSqlParameterSource sqlParams = new MapSqlParameterSource();
        for (TableField f : fields) {
            if (f.Field.isEmpty()) continue;
            String _default=" default 0 ";
            if (f.DefaultValue==null) _default="";
            if (f.DefaultValue!=null && f.DefaultValue.isEmpty())  _default=" default '' ";
            if (f.NewField ==null ||  f.NewField.isEmpty() || f.NewField.equals(f.Field) ) {
                namedParameterJdbcTemplate.update(
                        String.format("alter table data_ext_%s modify %s %s %s;", code,
                                f.Field, ChangeFieldType(f.Type, f.Len),_default),sqlParams
                );
            } else {
                namedParameterJdbcTemplate.update(
                        String.format("alter table data_ext_%s change column %s %s %s %s;", code,
                                f.Field, f.NewField, ChangeFieldType(f.Type, f.Len),_default),sqlParams
                );
            }
        }
    }

    @Override
    public void DeleteTableField(List<TableField> fields, String code) throws Exception {
        MapSqlParameterSource sqlParams = new MapSqlParameterSource();
        for (TableField f : fields) {
            if (f.Field.isEmpty()) continue;
            namedParameterJdbcTemplate.update(String.format("alter table data_ext_%s drop column %s", code, f.Field),sqlParams);
        }
    }

}
