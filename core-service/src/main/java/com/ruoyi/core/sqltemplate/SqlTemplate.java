package com.ruoyi.core.sqltemplate;

public class SqlTemplate {
    public static String treeInfoSqlTemplate(StringBuilder condition, boolean isAppIdEmpty) {
        String appCondition = isAppIdEmpty ?
                "AND node_tag in (0,9)" :
                "AND (app_id = :app_id OR node_tag = 9)";
        return """
                SELECT
                    ub.*,
                    ua.node_pid,
                    ua.node_path,
                    '0' leaf,
                    ua.node_order,
                    'tree' struct_type
                FROM
                    PSO_NODE_NET ua
                LEFT JOIN
                    PSO_MAIN_NODE ub ON ua.node_id = ub.node_id
                WHERE
                    node_type = :node_type
                    AND node_status = 1
                    %s
                    %s
                ORDER BY
                    node_pos, node_order
                """.formatted(appCondition, condition.toString());
    }

    public static String initMedicalRecordSql(){
        return """
                SELECT
                    m.zhuyuanh,
                    m.xing<PERSON>,
                    m.quy as 'quyu',
                    m.chuangw_no as 'chuangwh',
                    m.ni<PERSON><PERSON>,
                    m.x<PERSON><PERSON>,
                    m.yibaolx as 'yiblx',
                    m.z<PERSON>rq as 'riqi',
                    m.zhendsj as 'shij',
                    m.daij<PERSON>,
                    m.chulyj,
                    m.tijqk,
                    m.fuzjc,
                    CONCAT_WS('，', m.zhus, m.xianbs) AS 'xianbs',
                    CONCAT_WS('，', l1.zdmc, l2.zdmc, l3.zdmc, l4.zdmc) AS 'jibzd'
                FROM
                    data_ext_menzbl m
                LEFT JOIN
                    data_ext_linczd l1 ON m.linczd1 = l1.leaf_id
                LEFT JOIN
                    data_ext_linczd l2 ON m.linczd2 = l2.leaf_id
                LEFT JOIN
                    data_ext_linczd l3 ON m.linczd3 = l3.leaf_id
                LEFT JOIN
                    data_ext_linczd l4 ON m.linczd4 = l4.leaf_id
                WHERE
                    m.leaf_id = :id
                """;
    }
}
