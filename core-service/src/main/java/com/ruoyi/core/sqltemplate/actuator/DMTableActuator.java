package com.ruoyi.core.sqltemplate.actuator;


import com.ruoyi.core.entity.TableField;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class DMTableActuator extends BaseTableActuator {

    public DMTableActuator(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(namedParameterJdbcTemplate);
    }

    public DMTableActuator() {
        super();
    }
    @Override
    String ChangeFieldType(String _type, int _len) {
        if (_len == 0) _len = 20;
        switch (_type.toLowerCase()) {
            case "int":
                return "bigint";
            case "text":
            case "ntext":
                return "longvarchar";
            case "bool":
                return "byte";
            case "nstring":
                return String.format("varchar2(%d)", _len);
            default:
                return String.format("varchar(%d)", _len);
        }
    }

    @Override
    public void AddTableField(List<TableField> fields, String code) throws Exception {
        MapSqlParameterSource sqlParams = new MapSqlParameterSource();
        for (TableField f : fields) {
            if (f.Field.isEmpty()) continue;
            namedParameterJdbcTemplate.update(
                    String.format("alter table data_ext_%s add %s %s;", code,
                            f.Field, ChangeFieldType(f.Type, f.Len)),sqlParams
            );
        }
    }

    @Override
    public void ModifyTableField(List<TableField> fields,String code) throws Exception {
        MapSqlParameterSource sqlParams = new MapSqlParameterSource();
        for (TableField f : fields) {
            if (f.Field.isEmpty()) continue;
            if (f.NewField!=null && !f.NewField.isEmpty() && !f.NewField.equals(f.Field)) {
                namedParameterJdbcTemplate.update(String.format("alter table data_ext_%s rename column %s TO %s;",
                       code, f.Field, f.NewField),sqlParams);
            }
        }
    }

    @Override
    public void DeleteTableField(List<TableField> fields, String code) throws Exception {
        MapSqlParameterSource sqlParams = new MapSqlParameterSource();
        for (TableField f : fields) {
            if (f.Field.isEmpty()) continue;
            namedParameterJdbcTemplate.update(String.format("ALTER TABLE data_ext_%s DROP COLUMN %s;", code, f.Field),sqlParams);
        }
    }

    @Override
    public String Create(List<TableField> fields,String code) throws Exception {
        List<String> cList = new ArrayList<>();
        for (TableField f : fields) {
            if (f.Field.isEmpty()) continue;
            cList.add(String.format("%s %s%s%s", f.Field,
                    ChangeFieldType(f.Type, f.Len),
                    "",
                    f.Null ? " null" : " not null"
            ));
        }
        List<TableField> pkField=fields.stream().filter(item->item.Pk).toList();
        String keyField="leaf_id";
        if(pkField.size()>0){
            keyField=pkField.get(0).Field;
        }
        return String.format("create table data_ext_%s(", code) +
                String.join(",", cList) +
                ", NOT CLUSTER PRIMARY KEY(\""+keyField+"\") "+
                ");";
    }

}
