package com.ruoyi.core.sqltemplate.actuator;


import com.ruoyi.core.entity.TableField;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.ArrayList;
import java.util.List;

public abstract class BaseTableActuator {
    NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    public BaseTableActuator() {

    }

    public BaseTableActuator(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
    }

    String ChangeFieldType(String _type, int _len)
    {
        if (_len == 0) _len = 20;
        switch (_type.toLowerCase()) {
            case "int":
            case "bool":
                return "int";
            case "text":
                return "text";
            case "ntext":
                return "mediumtext";
            case "nstring":
                return String.format("nvarchar(%d)", _len);
            default:
                return String.format("varchar(%d)", _len);
        }
    }

    public String Create(List<TableField> fields,String code) throws Exception {
        List<String> cList = new ArrayList<>();
        for (TableField f : fields) {
            if (f.Field.isEmpty()) continue;
            cList.add(String.format("%s %s%s%s", f.Field,
                    ChangeFieldType(f.Type, f.Len),
                    f.Pk ? " primary key" : "",
                    f.Null ? " null" : " not null"
            ));
        }
        return String.format("create table data_ext_%s(", code) +
                String.join(",", cList) +
                ");";
    }

    public abstract void AddTableField(List<TableField> fields,String code) throws Exception;
    public abstract void ModifyTableField(List<TableField> fields,String code) throws Exception;
    public abstract void DeleteTableField(List<TableField> fields, String code) throws Exception;

}
