package com.ruoyi.core.sqltemplate.actuator;

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

public class TableActuatorFactory {

    public static BaseTableActuator CreateActuator(String jdbc, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        if (jdbc.contains("mysql")){
            return new MySqlTableActuator(namedParameterJdbcTemplate);
        }
        if (jdbc.contains("dm")){
            return new DMTableActuator(namedParameterJdbcTemplate);
        }
        return new MySqlTableActuator(namedParameterJdbcTemplate);
    }
}
