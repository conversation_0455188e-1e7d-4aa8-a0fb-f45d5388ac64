package com.ruoyi.core.export;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Map;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

public class Word {
    /**
     * 入住申请表
     *
     * @param data 数据
     * @return 导出文件
     */
    public File exportWord(Map<String, Object> data) {
        try {
            // 获取模板文件路径
            String templatePath = System.getProperty("user.dir") + File.separator + ExportConstants.TEMPLATE_DIR + File.separator + "入住申请表.docx";
            File templateFile = new File(templatePath);

            if (!templateFile.exists()) {
                throw new RuntimeException("模板文件不存在: " + templatePath);
            }

            // 创建输出文件
            String fileName = "入住申请表_" + System.currentTimeMillis() + ".docx";
            String outputPath = System.getProperty("user.dir") + File.separator + ExportConstants.TEMP_OUTPUT_DIR + File.separator + fileName;
            File outputFile = new File(outputPath);

            // 确保输出目录存在
            File outputDir = new File(outputFile.getParent());
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }

            // 预处理数据
            preprocessGenderData(data);
            preprocessEthnicGroupData(data);
            preprocessMaritalStatusData(data);

            try (FileInputStream fis = new FileInputStream(templateFile);
                 XWPFDocument document = new XWPFDocument(fis);
                 FileOutputStream fos = new FileOutputStream(outputFile)) {

                // 替换文档中的变量
                replaceInDocument(document, data);

                // 保存文档
                document.write(fos);
            }

            return outputFile;
        } catch (IOException e) {
            throw new RuntimeException("导出Word文档失败", e);
        }
    }

    /**
     * 预处理性别数据，为gender1添加对应的复选框格式
     *
     * @param data 数据映射
     */
    private void preprocessGenderData(Map<String, Object> data) {
        // 获取性别值
        Object genderObj = data.get("gender");
        if (genderObj != null) {
            String gender = genderObj.toString().trim();
            String checkboxFormat;

            // 根据性别设置选择框格式
            if ("男".equals(gender)) {
                checkboxFormat = ExportConstants.CHECKBOX_CHECKED + "男" + ExportConstants.CHECKBOX_UNCHECKED + "女";
            } else if ("女".equals(gender)) {
                checkboxFormat = ExportConstants.CHECKBOX_UNCHECKED + "男" + ExportConstants.CHECKBOX_CHECKED + "女";
            } else {
                checkboxFormat = ExportConstants.CHECKBOX_UNCHECKED + "男" + ExportConstants.CHECKBOX_UNCHECKED + "女";
            }

            data.put("gender1", checkboxFormat);  // 用于${gender1}
            data.put("gender", gender);         // 原始键，用于${gender}
        }
    }

    /**
     * 预处理民族数据，为ethnic_group添加对应的复选框格式
     *
     * @param data 数据映射
     */
    private void preprocessEthnicGroupData(Map<String, Object> data) {
        // 获取民族值
        Object ethnicGroupObj = data.get("ethnic_group");
        if (ethnicGroupObj != null) {
            String ethnicGroup = ethnicGroupObj.toString().trim();
            String formattedValue;

            // 根据民族设置选择框格式
            if ("汉族".equals(ethnicGroup)) {
                formattedValue = ExportConstants.CHECKBOX_CHECKED + "汉族" + ExportConstants.CHECKBOX_UNCHECKED + "其他：_______";
            } else {
                // 添加下划线后再显示民族名称
                formattedValue = ExportConstants.CHECKBOX_UNCHECKED + "汉族" + ExportConstants.CHECKBOX_CHECKED + "其他：_" + ethnicGroup + "_";
            }

            // 更新数据中的民族值
            data.put("ethnic_group", formattedValue);
        }
    }

    /**
     * 预处理婚姻状况数据，为marital_status添加对应的复选框格式
     *
     * @param data 数据映射
     */
    private void preprocessMaritalStatusData(Map<String, Object> data) {
        // 获取婚姻状况值
        Object maritalStatusObj = data.get("marital_status");
        if (maritalStatusObj != null) {
            String maritalStatus = maritalStatusObj.toString().trim();
            String formattedValue;

            // 根据婚姻状况设置选择框格式
            if ("未婚".equals(maritalStatus)) {
                formattedValue = ExportConstants.CHECKBOX_CHECKED + "未婚" + ExportConstants.CHECKBOX_UNCHECKED + "已婚" + ExportConstants.CHECKBOX_UNCHECKED + "丧偶" + ExportConstants.CHECKBOX_UNCHECKED + "离异" + ExportConstants.CHECKBOX_UNCHECKED + "未说明婚姻情况";
            } else if ("已婚".equals(maritalStatus)) {
                formattedValue = ExportConstants.CHECKBOX_UNCHECKED + "未婚" + ExportConstants.CHECKBOX_CHECKED + "已婚" + ExportConstants.CHECKBOX_UNCHECKED + "丧偶" + ExportConstants.CHECKBOX_UNCHECKED + "离异" + ExportConstants.CHECKBOX_UNCHECKED + "未说明婚姻情况";
            } else if ("丧偶".equals(maritalStatus)) {
                formattedValue = ExportConstants.CHECKBOX_UNCHECKED + "未婚" + ExportConstants.CHECKBOX_UNCHECKED + "已婚" + ExportConstants.CHECKBOX_CHECKED + "丧偶" + ExportConstants.CHECKBOX_UNCHECKED + "离异" + ExportConstants.CHECKBOX_UNCHECKED + "未说明婚姻情况";
            } else if ("离异".equals(maritalStatus)) {
                formattedValue = ExportConstants.CHECKBOX_UNCHECKED + "未婚" + ExportConstants.CHECKBOX_UNCHECKED + "已婚" + ExportConstants.CHECKBOX_UNCHECKED + "丧偶" + ExportConstants.CHECKBOX_CHECKED + "离异" + ExportConstants.CHECKBOX_UNCHECKED + "未说明婚姻情况";
            } else {
                formattedValue = ExportConstants.CHECKBOX_UNCHECKED + "未婚" + ExportConstants.CHECKBOX_UNCHECKED + "已婚" + ExportConstants.CHECKBOX_UNCHECKED + "丧偶" + ExportConstants.CHECKBOX_UNCHECKED + "离异" + ExportConstants.CHECKBOX_CHECKED + "未说明婚姻情况";
            }

            // 更新数据中的婚姻状况值
            data.put("marital_status", formattedValue);
        }
    }

    /**
     * 替换文档中的变量
     *
     * @param document Word文档
     * @param data 替换的数据
     */
    private void replaceInDocument(XWPFDocument document, Map<String, Object> data) {
        // 替换段落中的变量
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceParagraph(paragraph, data);
        }

        // 替换表格中的变量
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceParagraph(paragraph, data);
                    }
                }
            }
        }
    }

    /**
     * 替换段落中的变量
     *
     * @param paragraph 段落
     * @param data 替换的数据
     */
    private void replaceParagraph(XWPFParagraph paragraph, Map<String, Object> data) {
        String text = paragraph.getText();
        if (text != null && !text.isEmpty()) {
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = "${" + entry.getKey() + "}";
                if (text.contains(key)) {
                    String value = entry.getValue() != null ? entry.getValue().toString() : "";

                    // 由于Word文档中的变量可能被分割在多个Run中，我们需要采用特殊方法处理
                    // 首先收集所有的Run和它们的文本，找到需要替换的部分
                    String paragraphText = paragraph.getText();
                    int keyStartPos = paragraphText.indexOf(key);
                    if (keyStartPos >= 0) {
                        int currentPos = 0;
                        int keyEndPos = keyStartPos + key.length();

                        boolean replacementDone = false;

                        // 逐个检查每个Run
                        for (int i = 0; i < paragraph.getRuns().size(); i++) {
                            XWPFRun run = paragraph.getRuns().get(i);
                            String runText = run.getText(0);
                            if (runText == null) {
                                continue;
                            }

                            int runLength = runText.length();
                            int runStartPos = currentPos;
                            int runEndPos = currentPos + runLength;
                            currentPos = runEndPos;

                            // 判断这个Run是否包含了变量的一部分或全部
                            if (runEndPos > keyStartPos && runStartPos < keyEndPos) {
                                // 这个Run包含了变量的一部分
                                String beforeVar = "";
                                String afterVar = "";

                                if (runStartPos < keyStartPos) {
                                    beforeVar = runText.substring(0, keyStartPos - runStartPos);
                                }

                                if (runEndPos > keyEndPos) {
                                    afterVar = runText.substring(keyEndPos - runStartPos);
                                }

                                // 如果这是第一个包含变量的Run，放入替换值
                                if (!replacementDone) {
                                    run.setText(beforeVar + value + afterVar, 0);
                                    replacementDone = true;
                                } else {
                                    // 如果不是第一个，则移除这部分文本（因为已经在前一个Run中替换过了）
                                    run.setText(afterVar, 0);
                                }
                            }
                        }
                    } else {
                        // 回退到简单模式，尝试直接替换每个Run中的文本
                        for (XWPFRun run : paragraph.getRuns()) {
                            String runText = run.getText(0);
                            if (runText != null && runText.contains(key)) {
                                run.setText(runText.replace(key, value), 0);
                            }
                        }
                    }
                }
            }
        }
    }
}
