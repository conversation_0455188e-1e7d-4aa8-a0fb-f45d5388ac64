package com.ruoyi.core.export;

import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 导出功能的公共常量类
 */
public class ExportConstants {

    /**
     * 文件路径相关常量
     */
    public static final String TEMPLATE_DIR = "template";            // 模板文件目录
    public static final String TEMP_OUTPUT_DIR = "temp_dir";         // 临时输出文件目录

    /**
     * 复选框格式相关常量
     */
    public static final String CHECKBOX_UNCHECKED = "□";            // 未勾选的复选框
    public static final String CHECKBOX_CHECKED = "☑";              // 已勾选的复选框

    /**
     * 保存到临时文件
     *
     * @param workbook 工作簿
     * @param fileName 文件名
     * @return 生成的文件
     * @throws IOException 保存文件时可能抛出异常
     */
    public static File saveExcelToTempFile(XSSFWorkbook workbook, String fileName) throws IOException {
        // 创建临时目录（如果不存在）
        File tempDir = new File("temp_dir");
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }

        // 生成带时间戳的文件名
        String tempFileName = fileName + "_" + System.currentTimeMillis() + ".xlsx";
        File file = new File(tempDir, tempFileName);

        // 写入文件
        try (FileOutputStream outputStream = new FileOutputStream(file)) {
            workbook.write(outputStream);
        }

        return file;
    }

    /**
     * 设置A4纸张大小和打印设置
     *
     * @param sheet 工作表
     */
    public static void setupExcelA4PageFormat(XSSFSheet sheet) {
        // 设置打印参数为A4纸张
        PrintSetup printSetup = sheet.getPrintSetup();
        printSetup.setPaperSize(PrintSetup.A4_PAPERSIZE); // A4纸张
        printSetup.setLandscape(false); // 纵向打印
        printSetup.setFitWidth((short) 1); // 设置为1页宽度
        printSetup.setFitHeight((short) 0); // 自动高度

        // 设置页面边距(单位:英寸)
        sheet.setMargin(XSSFSheet.LeftMargin, 0.25);
        sheet.setMargin(XSSFSheet.RightMargin, 0.25);
        sheet.setMargin(XSSFSheet.TopMargin, 0.75);
        sheet.setMargin(XSSFSheet.BottomMargin, 0.75);

        // 设置页面打印居中
        sheet.setHorizontallyCenter(true);
    }
}
