package com.ruoyi.core.export;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class Excel {

    /**
     * 获取或创建单元格，用于编辑
     *
     * @param sheet     工作表
     * @param rowIndex  行索引
     * @param columnIndex 列索引
     * @return 单元格
     */
    private Cell getCellForEdit(Sheet sheet, int rowIndex, int columnIndex) {
        Row row = sheet.getRow(rowIndex);
        if (row == null) {
            row = sheet.createRow(rowIndex);
        }
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }
        return cell;
    }

    /**
     * 设置单元格的值
     *
     * @param sheet     工作表
     * @param rowIndex  行索引
     * @param columnIndex 列索引
     * @param value     要设置的值
     */
    private void setCellValue(Sheet sheet, int rowIndex, int columnIndex, String value) {
        Cell cell = getCellForEdit(sheet, rowIndex, columnIndex);
        cell.setCellValue(value);
    }

    /**
     * 创建并保存工作簿到临时文件
     *
     * @param workbook  工作簿
     * @param filePrefix 文件前缀
     * @return 保存的文件
     * @throws IOException 如果保存失败
     */
    private File saveWorkbookToTempFile(Workbook workbook, String filePrefix) throws IOException {
        String tempDir = System.getProperty("user.dir") + "/" + ExportConstants.TEMP_OUTPUT_DIR + "/";
        File dir = new File(tempDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        String fileName = filePrefix + "_" + System.currentTimeMillis() + ".xlsx";
        File file = new File(tempDir + fileName);

        try (FileOutputStream outputStream = new FileOutputStream(file)) {
            workbook.write(outputStream);
        }

        return file;
    }

    /**
     * 活动观察表
     *
     * @param data 数据
     * @return 导出的Excel文件
     */
    public File exportExcel1(Map<String, Object> data) {
        try {
            // 获取模板文件
            String templatePath = System.getProperty("user.dir") + "/" + ExportConstants.TEMPLATE_DIR + "/活动观察表.xlsx";
            File templateFile = new File(templatePath);
            if (!templateFile.exists()) {
                throw new RuntimeException("模板文件不存在：" + templatePath);
            }

            // 打开工作簿
            XSSFWorkbook workbook = new XSSFWorkbook(templateFile);
            XSSFSheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

            // 设置A3单元格的护理区信息
            String title = (String) data.get("title");
            Row row2 = sheet.getRow(2); // 第3行（索引从0开始）
            if (Objects.nonNull(row2)) {
                Cell cell = row2.getCell(0); // A列（索引从0开始）
                if (null != cell) {
                    cell.setCellValue("护理区：" + (title != null ? title : ""));
                }
                Cell cell2 = row2.getCell(2);
                if (Objects.nonNull(cell2) && Objects.nonNull(data.get("title1"))) {
                    cell2.setCellValue("护理区域：" + (String) data.get("title1"));
                }
            }

            Row row3 = sheet.getRow(3);
            if (Objects.nonNull(row3)) {
                if (Objects.nonNull(data.get("title2"))) {
                    setCellValueSafely(row3, 1, (String) data.get("title2"));
                }
                if (Objects.nonNull(data.get("title3"))) {
                    setCellValueSafely(row3, 3, (String) data.get("title3"));
                }
                if (Objects.nonNull(data.get("title4"))) {
                    setCellValueSafely(row3, 5, (String) data.get("title4"));
                }
                if (Objects.nonNull(data.get("title5"))) {
                    setCellValueSafely(row3, 7, (String) data.get("title5"));
                }
            }

            // 处理activity数据，设置C6单元格
            try {
                @SuppressWarnings("unchecked")
                List<String> activityValues = (List<String>) data.get("activity");
                if (Objects.nonNull(activityValues) && !activityValues.isEmpty()) {
                    String activityValue = activityValues.get(0);
                    if ("较长".equals(activityValue)) {
                        setCellValue(sheet, 5, 2, "☑较长");
                    } else if ("普通".equals(activityValue)) {
                        setCellValue(sheet, 6, 2, "☑普通");
                    } else if ("偏少".equals(activityValue)) {
                        setCellValue(sheet, 7, 2, "☑偏少");
                    } else if ("无".equals(activityValue)) {
                        setCellValue(sheet, 8, 2, "☑无");
                    }

                    // 处理第二个activity值 (索引1)
                    if (activityValues.size() > 1 && activityValues.get(1) != null) {
                        setCellValue(sheet, 5, 3, activityValues.get(1));
                    }

                    // 处理第三个activity值 (索引2)
                    if (activityValues.size() > 2 && activityValues.get(2) != null) {
                        String activityValue3 = activityValues.get(2);
                        if ("坐位".equals(activityValue3)) {
                            setCellValue(sheet, 5, 4, "☑坐位");
                        }
                    }

                    if (activityValues.size() > 3 && Objects.nonNull(activityValues.get(3)) && !activityValues.get(3).isBlank()) {
                        setCellValue(sheet, 5, 5, activityValues.get(3));
                    }

                    // 处理第五个activity值 (索引4)
                    if (activityValues.size() > 4 && activityValues.get(4) != null) {
                        String activityValue5 = activityValues.get(4);
                        if ("太阳".equals(activityValue5)) {
                            setCellValue(sheet, 5, 6, "☑太阳");
                        } else if ("多云".equals(activityValue5)) {
                            setCellValue(sheet, 6, 6, "☑多云");
                        } else if ("阴天".equals(activityValue5)) {
                            setCellValue(sheet, 7, 6, "☑阴天");
                        } else if ("闪电".equals(activityValue5)) {
                            setCellValue(sheet, 8, 6, "☑闪电");
                        }
                    }

                    // 处理第六个activity值 (索引5)
                    if (activityValues.size() > 5 && activityValues.get(5) != null) {
                        String activityValue6 = activityValues.get(5);
                        if ("清醒".equals(activityValue6)) {
                            setCellValue(sheet, 5, 7, "☑清醒");
                        } else if ("瞌睡".equals(activityValue6)) {
                            setCellValue(sheet, 6, 7, "☑瞌睡");
                        } else if ("嗜睡".equals(activityValue6)) {
                            setCellValue(sheet, 7, 7, "☑嗜睡");
                        }
                    }
                }
            } catch (Exception e) {
                System.out.println("处理activity数据异常: " + e.getMessage());
            }

            // 处理activity1数据
            try {
                @SuppressWarnings("unchecked")
                List<String> activity1Values = (List<String>) data.get("activity1");
                if (Objects.nonNull(activity1Values) && !activity1Values.isEmpty()) {
                    String activity1Value = activity1Values.get(0);
                    if ("较长".equals(activity1Value)) {
                        setCellValue(sheet, 10, 2, "☑较长");
                    } else if ("普通".equals(activity1Value)) {
                        setCellValue(sheet, 11, 2, "☑普通");
                    } else if ("偏少".equals(activity1Value)) {
                        setCellValue(sheet, 12, 2, "☑偏少");
                    } else if ("无".equals(activity1Value)) {
                        setCellValue(sheet, 13, 2, "☑无");
                    }

                    // 处理第二个activity1值 (索引1)
                    if (activity1Values.size() > 1 && activity1Values.get(1) != null) {
                        String activity1Value2 = activity1Values.get(1);
                        if (activity1Value2 != null && !activity1Value2.trim().isEmpty()) {
                            setCellValue(sheet, 10, 3, activity1Value2);
                        }
                    }

                    // 处理第三个activity1值 (索引2)
                    if (activity1Values.size() > 2 && activity1Values.get(2) != null) {
                        String activity1Value3 = activity1Values.get(2);
                        if ("坐位".equals(activity1Value3)) {
                            setCellValue(sheet, 10, 4, "☑坐位");
                        } else if ("卧床".equals(activity1Value3)) {
                            setCellValue(sheet, 11, 4, "☑卧床");
                        } else if ("自主行走".equals(activity1Value3)) {
                            setCellValue(sheet, 12, 4, "☑自主行走");
                        }
                    }

                    // 处理第四个activity1值 (索引3)
                    if (activity1Values.size() > 3 && activity1Values.get(3) != null) {
                        String activity1Value4 = activity1Values.get(3);
                        if (activity1Value4 != null && !activity1Value4.trim().isEmpty()) {
                            setCellValue(sheet, 10, 5, activity1Value4);
                        }
                    }

                    // 处理第五个activity1值 (索引4)
                    if (activity1Values.size() > 4 && activity1Values.get(4) != null) {
                        String activity1Value5 = activity1Values.get(4);
                        if ("太阳".equals(activity1Value5)) {
                            setCellValue(sheet, 10, 6, "☑太阳");
                        } else if ("多云".equals(activity1Value5)) {
                            setCellValue(sheet, 11, 6, "☑多云");
                        } else if ("阴天".equals(activity1Value5)) {
                            setCellValue(sheet, 12, 6, "☑阴天");
                        } else if ("闪电".equals(activity1Value5)) {
                            setCellValue(sheet, 13, 6, "☑闪电");
                        }
                    }

                    // 处理第六个activity1值 (索引5)
                    if (activity1Values.size() > 5 && activity1Values.get(5) != null) {
                        String activity1Value6 = activity1Values.get(5);
                        if ("清醒".equals(activity1Value6)) {
                            setCellValue(sheet, 10, 7, "☑清醒");
                        } else if ("瞌睡".equals(activity1Value6)) {
                            setCellValue(sheet, 11, 7, "☑瞌睡");
                        } else if ("嗜睡".equals(activity1Value6)) {
                            setCellValue(sheet, 12, 7, "☑嗜睡");
                        }
                    }
                }
            } catch (Exception e) {
                System.out.println("处理activity1数据异常: " + e.getMessage());
            }

            try {
                @SuppressWarnings("unchecked")
                List<String> activity2Values = (List<String>) data.get("activity2");
                if (Objects.nonNull(activity2Values) && !activity2Values.isEmpty()) {
                    String activity2Value = activity2Values.get(0);
                    if ("较长".equals(activity2Value)) {
                        setCellValue(sheet, 15, 2, "☑较长");
                    } else if ("普通".equals(activity2Value)) {
                        setCellValue(sheet, 16, 2, "☑普通");
                    } else if ("偏少".equals(activity2Value)) {
                        setCellValue(sheet, 17, 2, "☑偏少");
                    } else if ("无".equals(activity2Value)) {
                        setCellValue(sheet, 18, 2, "☑无");
                    }

                    // 处理第二个activity2值 (索引1)
                    if (activity2Values.size() > 1 && activity2Values.get(1) != null) {
                        String activity2Value2 = activity2Values.get(1);
                        if (activity2Value2 != null && !activity2Value2.trim().isEmpty()) {
                            setCellValue(sheet, 15, 3, activity2Value2);
                        }
                    }

                    // 处理第三个activity2值 (索引2)
                    if (activity2Values.size() > 2 && activity2Values.get(2) != null) {
                        String activity2Value3 = activity2Values.get(2);
                        if ("坐位".equals(activity2Value3)) {
                            setCellValue(sheet, 15, 4, "☑坐位");
                        } else if ("卧床".equals(activity2Value3)) {
                            setCellValue(sheet, 16, 4, "☑卧床");
                        } else if ("自主行走".equals(activity2Value3)) {
                            setCellValue(sheet, 17, 4, "☑自主行走");
                        }
                    }

                    // 处理第四个activity2值 (索引3)
                    if (activity2Values.size() > 3 && activity2Values.get(3) != null) {
                        String activity2Value4 = activity2Values.get(3);
                        if (activity2Value4 != null && !activity2Value4.trim().isEmpty()) {
                            setCellValue(sheet, 15, 5, activity2Value4);
                        }
                    }

                    // 处理第五个activity2值 (索引4)
                    if (activity2Values.size() > 4 && activity2Values.get(4) != null) {
                        String activity2Value5 = activity2Values.get(4);
                        if ("太阳".equals(activity2Value5)) {
                            setCellValue(sheet, 15, 6, "☑太阳");
                        } else if ("多云".equals(activity2Value5)) {
                            setCellValue(sheet, 16, 6, "☑多云");
                        } else if ("阴天".equals(activity2Value5)) {
                            setCellValue(sheet, 17, 6, "☑阴天");
                        } else if ("闪电".equals(activity2Value5)) {
                            setCellValue(sheet, 18, 6, "☑闪电");
                        }
                    }

                    // 处理第六个activity2值 (索引5)
                    if (activity2Values.size() > 5 && activity2Values.get(5) != null) {
                        String activity2Value6 = activity2Values.get(5);
                        if ("清醒".equals(activity2Value6)) {
                            setCellValue(sheet, 15, 7, "☑清醒");
                        } else if ("瞌睡".equals(activity2Value6)) {
                            setCellValue(sheet, 16, 7, "☑瞌睡");
                        } else if ("嗜睡".equals(activity2Value6)) {
                            setCellValue(sheet, 17, 7, "☑嗜睡");
                        }
                    }
                }
            } catch (Exception e) {
                System.out.println("处理activity2数据异常: " + e.getMessage());
            }

            // 处理评分数据，设置A20单元格
            try {
                // 获取评分值
                Object scoreObj = data.get("score");
                if (scoreObj != null) {
                    int score = 0;
                    // 将评分转换为整数
                    if (scoreObj instanceof Number) {
                        score = ((Number) scoreObj).intValue();
                    } else if (scoreObj instanceof String) {
                        try {
                            score = Integer.parseInt((String) scoreObj);
                        } catch (NumberFormatException e) {
                            System.out.println("评分格式不正确，无法转换为整数: " + e.getMessage());
                        }
                    }

                    // 限制评分范围，防止生成过多星号
                    if (score < 0) score = 0;
                    if (score > 5) score = 5;  // 设置一个合理的上限

                    // 生成星号字符串
                    StringBuilder stars = new StringBuilder("评分：");
                    for (int i = 0; i < score; i++) {
                        stars.append("☆");
                    }

                    // 设置A20单元格
                    setCellValue(sheet, 19, 0, stars.toString());
                }
            } catch (Exception e) {
                System.out.println("处理评分数据异常: " + e.getMessage());
            }

            // 处理remark数据，设置I6单元格
            try {
                // 获取remark值
                String remarkValue = (String) data.get("remark");
                if (remarkValue != null) {
                    // 获取I6单元格 (行索引5，列索引8)
                    Cell i6Cell = getCellForEdit(sheet, 5, 8);

                    // 根据remark值设置不同的内容
                    if ("新入院".equals(remarkValue)) {
                        i6Cell.setCellValue("☑新入院\n□回家返院\n□住院返院");
                    } else if ("回家返院".equals(remarkValue)) {
                        i6Cell.setCellValue("□新入院\n☑回家返院\n□住院返院");
                    } else if ("住院返院".equals(remarkValue)) {
                        i6Cell.setCellValue("□新入院\n□回家返院\n☑住院返院");
                    }

                    // 设置单元格样式允许换行
                    CellStyle style = workbook.createCellStyle();
                    style.setWrapText(true); // 允许文本换行
                    style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

                    // 添加边框设置，确保右侧边框显示
                    style.setBorderBottom(BorderStyle.THIN);
                    style.setBorderTop(BorderStyle.THIN);
                    style.setBorderLeft(BorderStyle.THIN);
                    style.setBorderRight(BorderStyle.MEDIUM);

                    // 设置边框颜色为黑色
                    style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                    style.setTopBorderColor(IndexedColors.BLACK.getIndex());
                    style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
                    style.setRightBorderColor(IndexedColors.BLACK.getIndex());

                    i6Cell.setCellStyle(style);
                }
            } catch (Exception e) {
                System.out.println("处理remark数据异常: " + e.getMessage());
            }

            // 使用工具方法保存工作簿到临时文件
            return saveWorkbookToTempFile(workbook, "活动观察表");
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 安全设置单元格值
     *
     * @param row      行
     * @param colIndex 列索引
     * @param value    值
     */
    private void setCellValueSafely(Row row, int colIndex, String value) {
        Cell cell = row.getCell(colIndex);
        if (cell == null) {
            cell = row.createCell(colIndex);
        }
        if (value != null) {
            cell.setCellValue(value);
        }
    }

    /**
     * 采购计划
     * 药品、医用材料采购单
     *
     * @return 导出的Excel文件
     */
    public File exportExcel(Map<String, Object> data) {
        // 验证必要的输入数据
        validatePurchaseData(data);

        String type = (String) data.get("type"); // 计划/紧急
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rows = (List<Map<String, Object>>) data.get("rows");

        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("采购单");

            // 设置页面格式
            setupPageFormat(sheet);

            // 创建标题
            createTitle(workbook, sheet);

            // 创建类型选择和采购单号行
            createTypeAndCodeRow(workbook, sheet, type);

            // 创建表头
            createHeaderRow(workbook, sheet);

            // 填充数据行
            int rowNum = fillDataRows(workbook, sheet, rows);

            // 创建签名栏
            createSignatureRows(workbook, sheet, rowNum);

            // 保存文件
            return saveToTempFile(workbook, "采购计划");
        } catch (IOException e) {
            throw new RuntimeException("创建Excel文件失败", e);
        }
    }

    /**
     * 验证采购单数据
     *
     * @param data 采购单数据
     */
    private void validatePurchaseData(Map<String, Object> data) {
        String type = (String) data.get("type");
        if (null == type || type.isBlank()) {
            throw new RuntimeException("采购单类型不能为空");
        }

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rows = (List<Map<String, Object>>) data.get("rows");
        if (null == rows || rows.isEmpty()) {
            throw new RuntimeException("采购单明细不能为空");
        }
    }

    /**
     * 设置页面格式为A4
     *
     * @param sheet Excel工作表
     */
    private void setupPageFormat(XSSFSheet sheet) {
        sheet.setFitToPage(true);
        PrintSetup printSetup = sheet.getPrintSetup();
        printSetup.setPaperSize(PrintSetup.A4_PAPERSIZE);
        printSetup.setFitHeight((short) 1);
        printSetup.setFitWidth((short) 1);
        printSetup.setLandscape(false); // 纵向打印

        // 设置页面边距
        sheet.setMargin(XSSFSheet.TopMargin, 0.75);
        sheet.setMargin(XSSFSheet.BottomMargin, 0.75);
        sheet.setMargin(XSSFSheet.LeftMargin, 0.7);
        sheet.setMargin(XSSFSheet.RightMargin, 0.7);

        // 设置24个基础列的宽度
        int baseWidth = 1000;
        for (int i = 0; i < 24; i++) {
            sheet.setColumnWidth(i, baseWidth);
        }
    }

    /**
     * 创建Excel标题
     *
     * @param workbook Excel工作簿
     * @param sheet Excel工作表
     */
    private void createTitle(Workbook workbook, Sheet sheet) {
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("药品、医用材料采购单");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 23));

        // 创建单元格样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        titleCell.setCellStyle(titleStyle);
    }

    /**
     * 创建类型和采购单号行
     *
     * @param workbook Excel工作簿
     * @param sheet Excel工作表
     * @param type 采购单类型
     */
    private void createTypeAndCodeRow(Workbook workbook, Sheet sheet, String type) {
        Row typeRow = sheet.createRow(1);
        typeRow.setHeightInPoints(25);
        Cell typeCell = typeRow.createCell(0);
        String planCheckBox = "计划".equals(type) ? "☑ 计划" : "□ 计划";
        String urgentCheckBox = "紧急".equals(type) ? "☑ 紧急" : "□ 紧急";
        typeCell.setCellValue(planCheckBox + "    " + urgentCheckBox);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 7));

        Cell codeCell = typeRow.createCell(12);
        codeCell.setCellValue("SSF/BD-WS-3-01");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 12, 23));

        CellStyle defaultStyle = workbook.createCellStyle();
        defaultStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        typeCell.setCellStyle(defaultStyle);

        CellStyle rightAlignStyle = workbook.createCellStyle();
        rightAlignStyle.setAlignment(HorizontalAlignment.RIGHT);
        rightAlignStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        codeCell.setCellStyle(rightAlignStyle);
    }

    /**
     * 创建表头行
     *
     * @param workbook Excel工作簿
     * @param sheet Excel工作表
     */
    private void createHeaderRow(Workbook workbook, Sheet sheet) {
        Row headerRow = sheet.createRow(3);
        headerRow.setHeightInPoints(22);
        String[] headers = {"序号", "药名", "规格", "厂商", "单位", "数量", "备注"};

        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 创建所有单元格并应用样式
        for (int i = 0; i < 24; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellStyle(headerStyle);
        }

        // 设置表头内容和合并单元格
        headerRow.getCell(0).setCellValue(headers[0]);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 1)); // 序号：2列

        headerRow.getCell(2).setCellValue(headers[1]);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 2, 7)); // 药名：6列

        headerRow.getCell(8).setCellValue(headers[2]);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 8, 10)); // 规格：3列

        headerRow.getCell(11).setCellValue(headers[3]);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 11, 16)); // 厂商：6列

        headerRow.getCell(17).setCellValue(headers[4]);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 17, 18)); // 单位：2列

        headerRow.getCell(19).setCellValue(headers[5]);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 19, 20)); // 数量：2列

        headerRow.getCell(21).setCellValue(headers[6]);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 21, 23)); // 备注：3列
    }

    /**
     * 填充数据行
     *
     * @param workbook Excel工作簿
     * @param sheet Excel工作表
     * @param rows 数据行
     * @return 下一行索引
     */
    private int fillDataRows(Workbook workbook, Sheet sheet, List<Map<String, Object>> rows) {
        // 创建单元格样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setWrapText(true);

        // 设置边框颜色为黑色
        dataStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        dataStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        dataStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        dataStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

        // 数字居右样式
        CellStyle numberStyle = workbook.createCellStyle();
        numberStyle.cloneStyleFrom(dataStyle);
        numberStyle.setAlignment(HorizontalAlignment.RIGHT);

        // 居中样式
        CellStyle centerStyle = workbook.createCellStyle();
        centerStyle.cloneStyleFrom(dataStyle);

        int rowNum = 4;
        for (int i = 0; i < rows.size(); i++) {
            Map<String, Object> rowData = rows.get(i);

            // 创建实际的数据行
            Row dataRow = sheet.createRow(rowNum++);
            dataRow.setHeightInPoints(30);

            // 创建单元格并应用样式
            for (int j = 0; j < 24; j++) {
                Cell cell = dataRow.createCell(j);
                cell.setCellStyle(dataStyle);
            }

            // 序号 - 合并第0-1列
            Cell indexCell = dataRow.getCell(0);
            indexCell.setCellValue(i + 1);
            indexCell.setCellStyle(centerStyle);
            sheet.addMergedRegion(new CellRangeAddress(dataRow.getRowNum(), dataRow.getRowNum(), 0, 1));

            // 药品名称 - 合并第2-7列
            Cell nameCell = dataRow.getCell(2);
            nameCell.setCellValue(String.valueOf(rowData.getOrDefault("name", "")));
            sheet.addMergedRegion(new CellRangeAddress(dataRow.getRowNum(), dataRow.getRowNum(), 2, 7));

            // 规格 - 合并第8-10列
            Cell specCell = dataRow.getCell(8);
            specCell.setCellValue(String.valueOf(rowData.getOrDefault("spec", "")));
            sheet.addMergedRegion(new CellRangeAddress(dataRow.getRowNum(), dataRow.getRowNum(), 8, 10));

            // 厂商 - 合并第11-16列
            Cell manufCell = dataRow.getCell(11);
            manufCell.setCellValue(String.valueOf(rowData.getOrDefault("manuf", "")));
            sheet.addMergedRegion(new CellRangeAddress(dataRow.getRowNum(), dataRow.getRowNum(), 11, 16));

            // 单位 - 合并第17-18列
            Cell unitCell = dataRow.getCell(17);
            unitCell.setCellValue(String.valueOf(rowData.getOrDefault("unit", "")));
            unitCell.setCellStyle(centerStyle);
            sheet.addMergedRegion(new CellRangeAddress(dataRow.getRowNum(), dataRow.getRowNum(), 17, 18));

            // 数量 - 合并第19-20列
            Cell qtyCell = dataRow.getCell(19);
            Object qtyObj = rowData.get("qty");
            if (qtyObj != null) {
                if (qtyObj instanceof Number) {
                    qtyCell.setCellValue(((Number) qtyObj).doubleValue());
                } else {
                    qtyCell.setCellValue(String.valueOf(qtyObj));
                }
            }
            qtyCell.setCellStyle(numberStyle);
            sheet.addMergedRegion(new CellRangeAddress(dataRow.getRowNum(), dataRow.getRowNum(), 19, 20));

            // 备注 - 合并第21-23列
            Cell commentCell = dataRow.getCell(21);
            commentCell.setCellValue(String.valueOf(rowData.getOrDefault("comment", "")));
            sheet.addMergedRegion(new CellRangeAddress(dataRow.getRowNum(), dataRow.getRowNum(), 21, 23));
        }

        return rowNum + 1; // 返回下一行索引，并增加一个空行
    }

    /**
     * 创建签名行
     *
     * @param workbook Excel工作簿
     * @param sheet Excel工作表
     * @param rowNum 开始行索引
     */
    private void createSignatureRows(Workbook workbook, Sheet sheet, int rowNum) {
        CellStyle signatureStyle = workbook.createCellStyle();
        signatureStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 添加第一行签名栏
        Row signatureRow1 = sheet.createRow(rowNum++);
        signatureRow1.setHeightInPoints(25);

        for (int i = 0; i < 24; i++) {
            Cell cell = signatureRow1.createCell(i);
            cell.setCellStyle(signatureStyle);
        }

        signatureRow1.getCell(0).setCellValue("单位负责人：");
        sheet.addMergedRegion(new CellRangeAddress(signatureRow1.getRowNum(), signatureRow1.getRowNum(), 0, 7));

        signatureRow1.getCell(8).setCellValue("分管领导：");
        sheet.addMergedRegion(new CellRangeAddress(signatureRow1.getRowNum(), signatureRow1.getRowNum(), 8, 15));

        signatureRow1.getCell(16).setCellValue("医保专员：");
        sheet.addMergedRegion(new CellRangeAddress(signatureRow1.getRowNum(), signatureRow1.getRowNum(), 16, 23));

        // 添加第二行签名栏
        Row signatureRow2 = sheet.createRow(rowNum++);
        signatureRow2.setHeightInPoints(25);

        for (int i = 0; i < 24; i++) {
            Cell cell = signatureRow2.createCell(i);
            cell.setCellStyle(signatureStyle);
        }

        signatureRow2.getCell(0).setCellValue("部门负责人：");
        sheet.addMergedRegion(new CellRangeAddress(signatureRow2.getRowNum(), signatureRow2.getRowNum(), 0, 7));

        signatureRow2.getCell(8).setCellValue("制    表：");
        sheet.addMergedRegion(new CellRangeAddress(signatureRow2.getRowNum(), signatureRow2.getRowNum(), 8, 15));

        signatureRow2.getCell(16).setCellValue("日    期：");
        sheet.addMergedRegion(new CellRangeAddress(signatureRow2.getRowNum(), signatureRow2.getRowNum(), 16, 23));
    }

    /**
     * 保存工作簿到临时文件
     *
     * @param workbook Excel工作簿
     * @param filePrefix 文件前缀
     * @return 保存的文件
     * @throws IOException 如果保存失败
     */
    private File saveToTempFile(Workbook workbook, String filePrefix) throws IOException {
        String tempDirPath = System.getProperty("user.dir") + "/temp_dir";
        File tempDir = new File(tempDirPath);
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }

        String fileName = filePrefix + ".xlsx";
        File resultFile = new File(tempDirPath, fileName);

        try (FileOutputStream fileOut = new FileOutputStream(resultFile)) {
            workbook.write(fileOut);
            System.out.println("已成功创建Excel文件: " + resultFile.getAbsolutePath());
        }

        return resultFile;
    }
}
