package com.ruoyi.core.export;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 药价调整表
 */
public class Excel2 {
    private final String TITLE = "药价调整表";

    /**
     * 导出Excel文件
     *
     * @param data 导出数据
     * @return 生成的Excel文件
     */
    public File exportExcel2(Map<String, Object> data) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 创建工作表
            XSSFSheet sheet = workbook.createSheet(TITLE);

            // 设置默认字体为宋体12号字
            setDefaultFont(workbook);

            // 设置A4纸张大小
            ExportConstants.setupExcelA4PageFormat(sheet);

            setupColumnWidths(sheet);

            // 创建标题行
            createTitle(workbook, sheet);

            // 创建表单编号行
            createFormNumberRow(workbook, sheet);

            // 创建执行日期行
            createDateRow(workbook, sheet, data);

            // 创建表头行
            createTableHeader(workbook, sheet);

            // 创建数据行
            int lastRowIndex = createDataRows(workbook, sheet, data);

            // 创建表格底部的审核和制单信息行
            createFooterRows(workbook, sheet, lastRowIndex + 2, data);

            // 保存文件
            return ExportConstants.saveExcelToTempFile(workbook, TITLE);
        } catch (Exception e) {
            throw new RuntimeException("创建Excel文件失败", e);
        }
    }

    /**
     * 导出Excel文件（无数据参数版本）
     *
     * @return 生成的Excel文件
     */
    public File exportExcel2() {
        // 创建默认数据
        Map<String, Object> defaultData = new HashMap<>();
        defaultData.put("date", "2025年06月23日");

        return exportExcel2(defaultData);
    }

    /**
     * 设置默认字体为微软雅黑12号字
     *
     * @param workbook 工作簿
     */
    private void setDefaultFont(XSSFWorkbook workbook) {
        try {
            // 使用项目中的微软雅黑字体文件
            String fontPath = System.getProperty("user.dir") + "/font/msyh.ttc";
            File fontFile = new File(fontPath);

            if (fontFile.exists()) {
                // 创建默认字体
                org.apache.poi.xssf.usermodel.XSSFFont defaultFont = workbook.createFont();
                defaultFont.setFontName("Microsoft YaHei"); // 微软雅黑的英文名称
                defaultFont.setFontHeightInPoints((short) 12);

                // 创建默认样式并应用默认字体
                org.apache.poi.xssf.usermodel.XSSFCellStyle defaultStyle = workbook.createCellStyle();
                defaultStyle.setFont(defaultFont);

                // 将默认样式应用于工作簿
                for (short i = 0; i < 64; i++) {
                    try {
                        org.apache.poi.xssf.usermodel.XSSFCellStyle style = workbook.getCellStyleAt(i);
                        if (style != null) {
                            style.setFont(defaultFont);
                        }
                    } catch (Exception e) {
                        break;
                    }
                }

                System.out.println("成功设置微软雅黑字体：" + fontPath);
            } else {
                System.err.println("找不到微软雅黑字体文件：" + fontPath);

                // 找不到字体文件时，使用系统默认字体
                org.apache.poi.xssf.usermodel.XSSFFont defaultFont = workbook.createFont();
                defaultFont.setFontName("Microsoft YaHei"); // 尝试使用系统安装的微软雅黑
                defaultFont.setFontHeightInPoints((short) 12);

                // 创建默认样式并应用默认字体
                org.apache.poi.xssf.usermodel.XSSFCellStyle defaultStyle = workbook.createCellStyle();
                defaultStyle.setFont(defaultFont);

                // 将默认样式应用于工作簿
                for (short i = 0; i < 64; i++) {
                    try {
                        org.apache.poi.xssf.usermodel.XSSFCellStyle style = workbook.getCellStyleAt(i);
                        if (style != null) {
                            style.setFont(defaultFont);
                        }
                    } catch (Exception e) {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("设置字体时发生错误：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 设置列宽
     *
     * @param sheet 工作表
     */
    private void setupColumnWidths(XSSFSheet sheet) {
        sheet.setColumnWidth(0, (int) (5.0 * 256));
        sheet.setColumnWidth(1, (int) (20.0 * 256));
        sheet.setColumnWidth(2, (int) (16.0 * 256));
        sheet.setColumnWidth(3, (int) (4.75 * 256));
        sheet.setColumnWidth(4, (int) (8.0 * 256));
        sheet.setColumnWidth(5, (int) (8.0 * 256));
        sheet.setColumnWidth(6, (int) (10.0 * 256));
        sheet.setColumnWidth(7, (int) (12.0 * 256));
    }

    /**
     * 创建标题行
     *
     * @param workbook 工作簿
     * @param sheet    工作表
     */
    private void createTitle(XSSFWorkbook workbook, XSSFSheet sheet) {
        // 创建标题行
        XSSFRow titleRow = sheet.createRow(0);
        sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(0, 0, 0, 7));

        // 设置标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);

        // 使用与默认字体相同的微软雅黑字体，但设置更大的字号
        org.apache.poi.xssf.usermodel.XSSFFont titleFont = workbook.createFont();
        titleFont.setFontName("Microsoft YaHei"); // 使用与默认字体相同的微软雅黑
        titleFont.setFontHeightInPoints((short) 14); // 标题字体大小设为14号
        titleStyle.setFont(titleFont);

        // 创建标题单元格
        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(TITLE);
        titleCell.setCellStyle(titleStyle);
    }

    /**
     * 创建表单编号行
     *
     * @param workbook 工作簿
     * @param sheet    工作表
     */
    private void createFormNumberRow(XSSFWorkbook workbook, XSSFSheet sheet) {
        // 创建第二行
        XSSFRow formNumberRow = sheet.createRow(1);

        // 合并8列单元格
        sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(1, 1, 0, 7));

        // 设置样式 - 右侧对齐
        CellStyle rightAlignStyle = workbook.createCellStyle();
        rightAlignStyle.setAlignment(HorizontalAlignment.RIGHT);

        // 使用与默认字体相同的微软雅黑字体
        org.apache.poi.xssf.usermodel.XSSFFont font = workbook.createFont();
        font.setFontName("Microsoft YaHei");
        font.setFontHeightInPoints((short) 10); // 稍小一点的字体
        rightAlignStyle.setFont(font);

        // 创建单元格并设置值
        XSSFCell formNumberCell = formNumberRow.createCell(0);
        formNumberCell.setCellValue("SSF/BD-WS-3-14");
        formNumberCell.setCellStyle(rightAlignStyle);
    }

    /**
     * 创建执行日期行
     *
     * @param workbook 工作簿
     * @param sheet    工作表
     * @param data     数据
     */
    private void createDateRow(XSSFWorkbook workbook, XSSFSheet sheet, Map<String, Object> data) {
        // 创建第三行
        XSSFRow dateRow = sheet.createRow(2);

        // 设置样式 - 左对齐
        CellStyle leftAlignStyle = workbook.createCellStyle();
        leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);

        // 使用与默认字体相同的微软雅黑字体
        org.apache.poi.xssf.usermodel.XSSFFont font = workbook.createFont();
        font.setFontName("Microsoft YaHei");
        font.setFontHeightInPoints((short) 10);
        leftAlignStyle.setFont(font);

        // 创建单元格并设置值
        XSSFCell dateCell = dateRow.createCell(0);

        // 从数据中获取日期，如果没有则使用默认值
        String dateValue = data.get("date") != null ? data.get("date").toString() : "";
        dateCell.setCellValue("执行日期：" + dateValue);
        dateCell.setCellStyle(leftAlignStyle);
    }

    /**
     * 创建表头行
     *
     * @param workbook 工作簿
     * @param sheet    工作表
     */
    private void createTableHeader(XSSFWorkbook workbook, XSSFSheet sheet) {
        // 创建表头行
        XSSFRow headerRow = sheet.createRow(3);

        // 设置表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        // 边框样式
        headerStyle.setBorderTop(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setBorderRight(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setBorderBottom(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        headerStyle.setBorderLeft(org.apache.poi.ss.usermodel.BorderStyle.THIN);

        // 使用与默认字体相同的微软雅黑字体，稍微加粗
        org.apache.poi.xssf.usermodel.XSSFFont font = workbook.createFont();
        font.setFontName("Microsoft YaHei");
        font.setFontHeightInPoints((short) 9);
        headerStyle.setFont(font);

        // 定义表头字段 - 修正为正确的表头名称
        String[] headers = {"序号", "药品名称", "规格", "单位", "当前价格", "最新价格", "零售价差额", "调整日期"};

        // 创建表头单元格
        for (int i = 0; i < headers.length; i++) {
            XSSFCell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 创建数据行
     *
     * @param workbook 工作簿
     * @param sheet    工作表
     * @param data     数据
     */
    @SuppressWarnings("unchecked")
    private int createDataRows(XSSFWorkbook workbook, XSSFSheet sheet, Map<String, Object> data) {
        // 从data中获取rows数据
        Object rowsObj = data.get("rows");
        if (rowsObj == null) {
            return 0;
        }

        List<String[]> rows;
        if (rowsObj instanceof List) {
            rows = (List<String[]>) rowsObj;
        } else {
            return 0;
        }

        // 创建单元格样式 - 所有单元格统一使用居中对齐
        CellStyle dataCellStyle = workbook.createCellStyle();
        dataCellStyle.setBorderTop(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        dataCellStyle.setBorderRight(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        dataCellStyle.setBorderBottom(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        dataCellStyle.setBorderLeft(org.apache.poi.ss.usermodel.BorderStyle.THIN);
        // 设置自动换行
        dataCellStyle.setWrapText(true);
        // 设置水平居中
        dataCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        dataCellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);

        // 设置字体
        org.apache.poi.xssf.usermodel.XSSFFont font = workbook.createFont();
        font.setFontName("Microsoft YaHei");
        font.setFontHeightInPoints((short) 10);
        dataCellStyle.setFont(font);

        // 添加数据行
        int rowIndex = 4; // 从第5行开始（索引为4）
        for (String[] rowData : rows) {
            XSSFRow row = sheet.createRow(rowIndex++);

            for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
                XSSFCell cell = row.createCell(colIndex);
                String value = rowData[colIndex];
                cell.setCellValue(value);

                // 所有单元格使用统一的居中样式
                cell.setCellStyle(dataCellStyle);
            }

            // 设置自动行高
            row.setHeight((short) -1);
        }

        return rowIndex; // 返回最后一行的索引
    }

    /**
     * 创建表格底部的审核和制单信息行
     *
     * @param workbook  工作簿
     * @param sheet     工作表
     * @param startRow  开始行
     * @param data      数据
     */
    private void createFooterRows(XSSFWorkbook workbook, XSSFSheet sheet, int startRow, Map<String, Object> data) {
        // 创建样式 - 左对齐
        CellStyle leftAlignStyle = workbook.createCellStyle();
        leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);

        // 使用与默认字体相同的微软雅黑字体
        org.apache.poi.xssf.usermodel.XSSFFont font = workbook.createFont();
        font.setFontName("Microsoft YaHei");
        font.setFontHeightInPoints((short) 10);
        leftAlignStyle.setFont(font);

        // 第一行：审核和制单
        XSSFRow row1 = sheet.createRow(startRow);

        // B列添加审核
        XSSFCell cellB1 = row1.createCell(1);
        cellB1.setCellValue("审核：");
        cellB1.setCellStyle(leftAlignStyle);

        XSSFCell cellG1 = row1.createCell(6);
        cellG1.setCellValue("制单：");
        cellG1.setCellStyle(leftAlignStyle);

        // 第二行：医保更改确认接收人和执行日期
        XSSFRow row2 = sheet.createRow(startRow + 1);

        // B列添加医保更改确认接收人
        XSSFCell cellB2 = row2.createCell(1);
        cellB2.setCellValue("医保更改确认接收人：");
        cellB2.setCellStyle(leftAlignStyle);

        // G列添加执行日期
        XSSFCell cellG2 = row2.createCell(6);
        String dateValue = data.get("date") != null ? data.get("date").toString() : "";
        cellG2.setCellValue("执行日期：" + dateValue);
        cellG2.setCellStyle(leftAlignStyle);
    }

    /**
     * main方法用于测试Excel2导出功能
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            List<String[]> rows = List.<String[]>of(new String[]{"1", "注射用头孢呋辛钠（信立欣）", "1.5g*10瓶", "盒", "158.95", "158.9", "-0.05", "2025-04-03"});
            Map<String, Object> data = new HashMap<>();
            data.put("date", "2025-06-23");
            data.put("rows", rows);

            Excel2 excel2 = new Excel2();
            File excelFile = excel2.exportExcel2(data);
            System.out.println("Excel文件生成成功，文件路径：" + excelFile.getAbsolutePath());
        } catch (Exception e) {
            System.err.println("Excel文件生成失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

}
