package com.ruoyi.core.export;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PDFTest {

    public static void main(String[] args) {
        try {
            System.out.println("开始PDF导出测试...");

            // 创建测试数据
            Map<String, Object> testData = new HashMap<>();
            testData.put("date_begin", "2024-10-01");
            testData.put("date_end", "2024-10-07");
            testData.put("ratio", "100%");

            // 创建一周七天的数据
            List<Map<String, Object>> rows = new ArrayList<>();

            // 为一周的每一天创建数据
            String[] dates = {"2024-10-06", "2024-10-07", "2024-10-08", "2024-10-09", "2024-10-10", "2024-10-11", "2024-10-12"};

            for (String date : dates) {
                Map<String, Object> row = new HashMap<>();
                row.put("date", date);
                rows.add(row);
            }

            rows.get(0).put("breakfast", "无糖豆浆,糙米粥");
            rows.get(0).put("breakfast1", "荠菜肉包,玉米刀切");
            rows.get(0).put("breakfast2", "酱蛋,酱菜");
            rows.get(0).put("lunch", "红烧鳊鱼");
            rows.get(0).put("lunch1", "糖醋肋排");
            rows.get(0).put("lunch2", "西葫芦肉片");
            rows.get(0).put("lunch3", "香煎臭豆腐");
            rows.get(0).put("lunch4", "鱼香肉丝清炒花菜海带玉米汤");
            rows.get(0).put("dinner", "黑胡椒牛肉粒");
            rows.get(0).put("dinner1", "椒盐排条");
            rows.get(0).put("dinner2", "酱汁鸭胸肉");
            rows.get(0).put("dinner3", "菠菜木耳");
            rows.get(0).put("dinner4", "红烧萝卜 罗宋汤");

            rows.get(1).put("breakfast", "赤豆粥,糖豆浆");
            rows.get(1).put("breakfast1", "蛋糕,全麦刀切");
            rows.get(1).put("breakfast2", "炒蛋");

            rows.get(2).put("breakfast", "青菜肉丝面");
            rows.get(2).put("breakfast1", "花卷");
            rows.get(2).put("breakfast2", "");

            testData.put("rows", rows);

            System.out.println("创建PDF导出器...");
            // 实例化PDF类
            PDF pdfExporter = new PDF();

            System.out.println("调用exportPDF方法...");
            // 调用exportPDF方法，现在返回File对象
            File pdfFile = pdfExporter.exportPDF(testData);

            // 获取当前工作目录，用于显示文件位置
            String currentDir = System.getProperty("user.dir");

            if (pdfFile.exists()) {
                System.out.println("PDF文件已成功生成: " + pdfFile.getAbsolutePath());
                System.out.println("文件大小: " + pdfFile.length() + " 字节");

                // 检查文件是否确实有内容
                if (pdfFile.length() > 0) {
                    System.out.println("PDF文件有内容，请打开检查标题\"上海市第四社会福利院\"是否正确显示");
                } else {
                    System.out.println("警告: 生成的PDF文件大小为0字节，请检查生成过程");

                    // 如果文件为空，检查字体是否存在
                    File fontFile = new File(currentDir + File.separator + "font" + File.separator + "simhei.ttf");
                    if (!fontFile.exists()) {
                        System.out.println("提示: 项目目录中没有找到simhei.ttf字体文件，请确保字体文件存在");
                    }
                }
            } else {
                System.out.println("文件未找到，请检查路径: " + pdfFile.getAbsolutePath());
            }

            System.out.println("PDF生成测试完成!");
        } catch (Exception e) {
            System.err.println("PDF生成测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}