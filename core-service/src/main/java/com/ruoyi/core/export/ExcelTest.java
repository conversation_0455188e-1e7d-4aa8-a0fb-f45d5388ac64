package com.ruoyi.core.export;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel导出测试类
 */
public class ExcelTest {

    public static void main(String[] args) {
        testExportExcel();
//        testExportExcel1();
    }

    /**
     * 测试活动观察表导出
     */
    public static void testExportExcel1() {
        // 创建测试数据
        Map<String, Object> data = new HashMap<>();

        // 设置表头信息
        data.put("title", "XX区");
        data.put("title1", "qjwe");
        data.put("title2", "2023年10月");
        data.put("title3", "活动观察表");
        data.put("title4", "2023年10月1日");
        data.put("title5", "2023年10月31日");

        data.put("activity", List.of("较长", "bz", "坐位", "bz", "太阳", "清醒"));
        data.put("activity1", List.of("普通", "1", "卧床", "2", "多云", "瞌睡"));
        data.put("activity2", List.of("偏少", "", "自主行走", "", "阴天", "嗜睡"));
        data.put("remark", "新入院");

        data.put("score", "2");

        // 创建Excel对象并调用exportExcel1方法
        Excel excel = new Excel();
        File exportedFile = excel.exportExcel1(data);

        // 提示文件生成结果
        if (exportedFile != null && exportedFile.exists()) {
            System.out.println("活动观察表已成功导出，文件位置: " + exportedFile.getAbsolutePath());

            // 尝试打开文件（仅Windows环境有效）
            try {
                if (System.getProperty("os.name").toLowerCase().contains("win")) {
                    Runtime.getRuntime().exec("cmd /c start " + exportedFile.getAbsolutePath());
                    System.out.println("已尝试打开Excel文件，请检查是否正常显示");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            System.out.println("活动观察表导出失败");
        }
    }

    /**
     * 测试药品、医用材料采购单导出
     */
    public static void testExportExcel() {
        // 创建测试数据
        Map<String, Object> data = new HashMap<>();

        // 设置采购单类型和采购单号
        data.put("type", "计划");

        // 创建明细行数据
        List<Map<String, Object>> rows = new ArrayList<>();

        // 添加测试数据行
        Map<String, Object> row1 = new HashMap<>();
        row1.put("name", "阿司匹林");
        row1.put("spec", "100mg*30片");
        row1.put("manuf", "北京XX制药有限公司"); // 添加厂商信息
        row1.put("unit", "盒");
        row1.put("qty", 100); // 修改字段名为qty
        row1.put("comment", "门诊使用"); // 添加备注信息
        rows.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("name", "布洛芬");
        row2.put("spec", "0.2g*20片");
        row2.put("manuf", "上海XX药业集团"); // 添加厂商信息
        row2.put("unit", "盒");
        row2.put("qty", 50); // 修改字段名为qty
        row2.put("comment", "住院部急需"); // 添加备注信息
        rows.add(row2);

        // 添加第三行数据以展示更多样式
        Map<String, Object> row3 = new HashMap<>();
        row3.put("name", "注射用青霉素钠");
        row3.put("spec", "80万单位/支");
        row3.put("manuf", "广州XX生物医药股份有限公司");
        row3.put("unit", "支");
        row3.put("qty", 200);
        row3.put("comment", "药库备货");
        rows.add(row3);

        // 将明细行添加到数据中
        data.put("rows", rows);

        // 创建Excel对象并调用Procurement方法
        Excel excel = new Excel();
        excel.exportExcel(data);

        // 尝试打开生成的Excel文件
        try {
            String filename = "采购计划_" + data.get("code") + ".xlsx";
            File file = new File(filename);
            if (file.exists()) {
                System.out.println("文件已创建成功，位于: " + file.getAbsolutePath());

                // 尝试打开文件（仅Windows环境有效）
                if (System.getProperty("os.name").toLowerCase().contains("win")) {
                    Runtime.getRuntime().exec("cmd /c start " + filename);
                    System.out.println("已尝试打开Excel文件，请检查是否正常显示");
                }
            } else {
                System.out.println("文件创建失败，未找到文件: " + filename);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
