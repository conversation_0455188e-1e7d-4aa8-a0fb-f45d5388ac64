package com.ruoyi.core.export;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class PDF {
    /**
     * 每周菜谱及兑现表
     *
     * @param data 数据
     * @return 生成的PDF文件对象
     */
    public File exportPDF(Map<String, Object> data) {
        // 验证rows数据
        Object rowsObj = data.get("rows");
        if (!(rowsObj instanceof List<?>)) {
            throw new IllegalArgumentException("数据格式错误：rows必须是List<Map<String, Object>>类型");
        }

        List<?> rowsList = (List<?>) rowsObj;
        if (rowsList.size() != 7) {
            throw new IllegalArgumentException("数据行数错误：rows的长度必须为7，当前长度为" + rowsList.size());
        }

        // 验证rows中的每个元素是否为Map<String, Object>类型
        for (Object row : rowsList) {
            if (!(row instanceof Map<?, ?>)) {
                throw new IllegalArgumentException("数据格式错误：rows中的每个元素必须是Map<String, Object>类型");
            }
        }

        // 类型安全的转换
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rows = (List<Map<String, Object>>) rowsObj;

        String fileName = "每周菜谱及兑现表" + data.get("date_begin") + ".pdf";
        File outputFile = new File(fileName);

        try (PDDocument document = new PDDocument()) {
            // 创建A4页面
            PDPage page = new PDPage(PDRectangle.A4);
            document.addPage(page);

            // 获取A4页面尺寸（单位：点，1英寸=72点）
            float pageWidth = page.getMediaBox().getWidth();
            float pageHeight = page.getMediaBox().getHeight();

            // 设置较窄的页面边距（单位：点）
            float margin = 36; // 默认通常是72点(1英寸)，这里设为36点(0.5英寸)

            // 计算内容区域的尺寸和位置
            float contentWidth = pageWidth - 2 * margin;
            float contentHeight = pageHeight - 2 * margin;
            float startX = margin;
            float startY = pageHeight - margin; // PDF坐标系原点在左下角

            // 创建内容流
            PDPageContentStream contentStream = new PDPageContentStream(document, page);

            try {
                // 加载中文字体
                PDFont font = loadChineseFont(document);

                // 设置字体和字号
                float titleFontSize = 16;
                float subtitleFontSize = 14; // 副标题字号稍小

                // 计算标题位置（居中）
                String title = "上海市第四社会福利院";
                contentStream.setFont(font, titleFontSize);
                float titleWidth = font.getStringWidth(title) / 1000 * titleFontSize;
                float titleX = (pageWidth - titleWidth) / 2;

                // 添加标题
                contentStream.beginText();
                contentStream.newLineAtOffset(titleX, startY - 30);
                contentStream.showText(title);
                contentStream.endText();

                // 添加副标题
                String subtitle = "膳食科每周菜谱、兑现表";
                contentStream.setFont(font, subtitleFontSize);
                float subtitleWidth = font.getStringWidth(subtitle) / 1000 * subtitleFontSize;
                float subtitleX = (pageWidth - subtitleWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(subtitleX, startY - 55); // 位于主标题下方
                contentStream.showText(subtitle);
                contentStream.endText();

                // 添加信息行
                float infoFontSize = 8; // 信息行字体稍小
                contentStream.setFont(font, infoFontSize);

                // 获取日期范围和兑现率数据，如果不存在则使用默认值
                String dateBegin = data.containsKey("date_begin") ? (String) data.get("date_begin") : "";
                String dateEnd = data.containsKey("date_end") ? (String) data.get("date_end") : "";
                String ratio = data.containsKey("ratio") ? (String) data.get("ratio") : "";

                // 组装左侧文字
                String leftText = "日期：" + dateBegin + " --- " + dateEnd;
                // 中间文字
                String middleText = "兑现率：" + ratio;
                // 右侧文字
                String rightText = "SSF/BD-SS-2-02";

                // 在副标题下方留出足够空间
                float infoY = startY - 85;

                // 绘制左侧文字
                contentStream.beginText();
                contentStream.newLineAtOffset(startX, infoY);
                contentStream.showText(leftText);
                contentStream.endText();

                // 绘制中间文字（计算居中位置）
                float middleWidth = font.getStringWidth(middleText) / 1000 * infoFontSize;
                float middleX = (pageWidth - middleWidth) / 2;
                contentStream.beginText();
                contentStream.newLineAtOffset(middleX, infoY);
                contentStream.showText(middleText);
                contentStream.endText();

                // 绘制右侧文字（计算使其右对齐）
                float rightWidth = font.getStringWidth(rightText) / 1000 * infoFontSize;
                float rightX = pageWidth - margin - rightWidth;
                contentStream.beginText();
                contentStream.newLineAtOffset(rightX, infoY);
                contentStream.showText(rightText);
                contentStream.endText();

                // 添加表格
                // 表格起始位置在信息行下方
                float tableTopY = infoY - 20;

                // 定义表格行高
                float headerRowHeight = 20; // 第一行表头行高
                float subHeaderRowHeight = 20; // 第二行表头行高
                float thirdRowHeight = 20; // 第三行表头行高
                float totalHeaderHeight = headerRowHeight + subHeaderRowHeight + thirdRowHeight; // 三行总高度

                float tableBottomY = tableTopY - totalHeaderHeight; // 表头高度，三行

                // 定义列宽（总宽度为contentWidth）
                // 将contentWidth分为两部分：菜单部分占65%，实际兑现部分占35%
                float menuWidth = contentWidth * 0.85f;
                float checkWidth = contentWidth * 0.15f; // 实际兑现部分

                // 日期列宽度在菜单部分
                float col1Width = menuWidth * 0.08f;

                // 菜单部分的早餐占24%
                float breakfastWidth = menuWidth * 0.24f;
                float col2Width = breakfastWidth / 3; // 稀饭列
                float col3Width = breakfastWidth / 3; // 点心列
                float col4Width = breakfastWidth / 3; // 其它列

                // 菜单部分的午餐占35%
                float lunchWidth = menuWidth * 0.35f;
                float col5Width = lunchWidth / 4; // 午餐A
                float col6Width = lunchWidth / 4; // 午餐B
                float col7Width = lunchWidth / 4; // 午餐C
                float col8Width = lunchWidth / 4; // 午餐D

                // 菜单部分的晚餐占35%
                float dinnerWidth = menuWidth * 0.35f;
                float col9Width = dinnerWidth / 4; // 晚餐A
                float col10Width = dinnerWidth / 4; // 晚餐B
                float col11Width = dinnerWidth / 4; // 晚餐C
                float col12Width = dinnerWidth / 4; // 晚餐D

                // 单个字符宽度的估算
                float oneCharWidth = 8f; // 估计单个字符的宽度

                // 实际兑现部分列宽计算 - 调整为简化布局
                float checkBreakfastWidth = oneCharWidth;
                float checkLunchWidth = oneCharWidth * 4;              // 午餐A、B、C、D四列宽度
                float checkDinnerWidth = oneCharWidth * 4;             // 晚餐A、B、C、D四列宽度

                // 重新计算实际兑现情况总宽度
                checkWidth = checkBreakfastWidth + checkLunchWidth + checkDinnerWidth;

                // 相应调整菜单宽度，使两者加起来等于contentWidth
                menuWidth = contentWidth - checkWidth;

                // 午餐和晚餐每列宽度
                float checkLunchColWidth = checkLunchWidth / 4;         // 每列宽度固定为总宽度四分之一
                float checkDinnerColWidth = checkDinnerWidth / 4;       // 每列宽度固定为总宽度四分之一

                // 计算表格坐标
                float tableLeftX = startX;
                float tableRightX = startX + contentWidth;

                // 菜单部分右侧边界
                float menuRightX = tableLeftX + menuWidth;

                // 日期列右边界
                float col1RightX = tableLeftX + col1Width;

                // 计算早餐部分右边界
                float breakfastRightX = col1RightX + breakfastWidth;

                // 计算午餐部分右边界
                float lunchRightX = breakfastRightX + lunchWidth;

                // 计算实际兑现情况部分的分隔线位置
                float checkBreakfastRightX = menuRightX + checkBreakfastWidth;
                float checkLunchRightX = checkBreakfastRightX + checkLunchWidth;

                // 绘制表格外框
                contentStream.setLineWidth(0.5f);
                // 外框
                contentStream.moveTo(tableLeftX, tableTopY);
                contentStream.lineTo(tableRightX, tableTopY);
                contentStream.stroke();

                contentStream.moveTo(tableLeftX, tableBottomY);
                contentStream.lineTo(tableRightX, tableBottomY);
                contentStream.stroke();

                contentStream.moveTo(tableLeftX, tableTopY);
                contentStream.lineTo(tableLeftX, tableBottomY);
                contentStream.stroke();

                contentStream.moveTo(tableRightX, tableTopY);
                contentStream.lineTo(tableRightX, tableBottomY);
                contentStream.stroke();

                // 绘制第一级横线（第一行表头下方）- 从日期列右侧开始，去掉日期上方的分割线
                float firstMidLineY = tableTopY - headerRowHeight;
                contentStream.moveTo(col1RightX, firstMidLineY);  // 从日期列右侧开始
                contentStream.lineTo(tableRightX, firstMidLineY);
                contentStream.stroke();

                // 绘制第二级横线（第二行表头下方）
                float secondMidLineY = firstMidLineY - subHeaderRowHeight;

                // 绘制第二行横线，但在日期列、早餐部分、午餐部分、晚餐部分以及实际兑现情况的早餐部分不绘制（实现合并效果）
                contentStream.moveTo(checkBreakfastRightX, secondMidLineY); // 从实际兑现情况午餐列左侧开始，跳过早餐列
                contentStream.lineTo(tableRightX, secondMidLineY);
                contentStream.stroke();

                // 绘制竖线 - 计算各列右侧X坐标
                // 日期列竖线
                contentStream.moveTo(col1RightX, tableTopY);
                contentStream.lineTo(col1RightX, tableBottomY);
                contentStream.stroke();

                // 早餐部分
                contentStream.moveTo(breakfastRightX, tableTopY);
                contentStream.lineTo(breakfastRightX, tableBottomY);
                contentStream.stroke();

                // 早餐下的子列分隔线（只画在第二行和第三行）
                float col2RightX = col1RightX + col2Width;
                contentStream.moveTo(col2RightX, firstMidLineY);
                contentStream.lineTo(col2RightX, tableBottomY);
                contentStream.stroke();

                float col3RightX = col2RightX + col3Width;
                contentStream.moveTo(col3RightX, firstMidLineY);
                contentStream.lineTo(col3RightX, tableBottomY);
                contentStream.stroke();

                // 午餐部分
                contentStream.moveTo(lunchRightX, tableTopY);
                contentStream.lineTo(lunchRightX, tableBottomY);
                contentStream.stroke();

                // 午餐下的子列分隔线（只画在第二行和第三行）
                float col5RightX = breakfastRightX + col5Width;
                contentStream.moveTo(col5RightX, firstMidLineY);
                contentStream.lineTo(col5RightX, tableBottomY);
                contentStream.stroke();

                float col6RightX = col5RightX + col6Width;
                contentStream.moveTo(col6RightX, firstMidLineY);
                contentStream.lineTo(col6RightX, tableBottomY);
                contentStream.stroke();

                float col7RightX = col6RightX + col7Width;
                contentStream.moveTo(col7RightX, firstMidLineY);
                contentStream.lineTo(col7RightX, tableBottomY);
                contentStream.stroke();

                // 晚餐下的子列分隔线（只画在第二行和第三行）
                float col9RightX = lunchRightX + col9Width;
                contentStream.moveTo(col9RightX, firstMidLineY);
                contentStream.lineTo(col9RightX, tableBottomY);
                contentStream.stroke();

                float col10RightX = col9RightX + col10Width;
                contentStream.moveTo(col10RightX, firstMidLineY);
                contentStream.lineTo(col10RightX, tableBottomY);
                contentStream.stroke();

                float col11RightX = col10RightX + col11Width;
                contentStream.moveTo(col11RightX, firstMidLineY);
                contentStream.lineTo(col11RightX, tableBottomY);
                contentStream.stroke();

                // 菜单与实际兑现部分的分隔线
                contentStream.moveTo(menuRightX, tableTopY);
                contentStream.lineTo(menuRightX, tableBottomY);
                contentStream.stroke();

                // 实际兑现情况部分的分隔线
                // 绘制早餐与午餐的分隔线
                contentStream.moveTo(checkBreakfastRightX, firstMidLineY);  // 从第一行底部开始
                contentStream.lineTo(checkBreakfastRightX, tableBottomY);
                contentStream.stroke();

                // 绘制午餐与晚餐的分隔线
                contentStream.moveTo(checkLunchRightX, firstMidLineY);  // 从第一行底部开始
                contentStream.lineTo(checkLunchRightX, tableBottomY);
                contentStream.stroke();

                // 添加午餐A、B、C、D的分隔线
                float checkLunchAX = checkBreakfastRightX + checkLunchColWidth;
                contentStream.moveTo(checkLunchAX, secondMidLineY);  // 从第二行底部开始
                contentStream.lineTo(checkLunchAX, tableBottomY);
                contentStream.stroke();

                float checkLunchBX = checkLunchAX + checkLunchColWidth;
                contentStream.moveTo(checkLunchBX, secondMidLineY);
                contentStream.lineTo(checkLunchBX, tableBottomY);
                contentStream.stroke();

                float checkLunchCX = checkLunchBX + checkLunchColWidth;
                contentStream.moveTo(checkLunchCX, secondMidLineY);
                contentStream.lineTo(checkLunchCX, tableBottomY);
                contentStream.stroke();

                // 添加晚餐A、B、C、D的分隔线
                float checkDinnerAX = checkLunchRightX + checkDinnerColWidth;
                contentStream.moveTo(checkDinnerAX, secondMidLineY);  // 从第二行底部开始
                contentStream.lineTo(checkDinnerAX, tableBottomY);
                contentStream.stroke();

                float checkDinnerBX = checkDinnerAX + checkDinnerColWidth;
                contentStream.moveTo(checkDinnerBX, secondMidLineY);
                contentStream.lineTo(checkDinnerBX, tableBottomY);
                contentStream.stroke();

                float checkDinnerCX = checkDinnerBX + checkDinnerColWidth;
                contentStream.moveTo(checkDinnerCX, secondMidLineY);
                contentStream.lineTo(checkDinnerCX, tableBottomY);
                contentStream.stroke();

                // 写入表头文字
                float smallFontSize = 8;
                contentStream.setFont(font, smallFontSize);

                // 修改日期文字位置，使其在合并的三行单元格中垂直居中
                String dateHeader = "日期";
                float dateTextWidth = font.getStringWidth(dateHeader) / 1000 * smallFontSize;
                float dateTextX = tableLeftX + (col1Width - dateTextWidth) / 2;
                // 计算合并单元格中心点的Y坐标
                float dateTextY = tableTopY - totalHeaderHeight / 2 - smallFontSize / 3;

                contentStream.beginText();
                contentStream.newLineAtOffset(dateTextX, dateTextY);
                contentStream.showText(dateHeader);
                contentStream.endText();

                // 第一行表头文字Y坐标
                float firstRowTextY = tableTopY - headerRowHeight / 2 - smallFontSize / 3;

                // 写入"早餐"(第一行)
                String breakfastHeader = "早餐";
                float breakfastTextWidth = font.getStringWidth(breakfastHeader) / 1000 * smallFontSize;
                float breakfastTextX = col1RightX + (breakfastWidth - breakfastTextWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(breakfastTextX, firstRowTextY);
                contentStream.showText(breakfastHeader);
                contentStream.endText();

                // 写入"午餐"(第一行)
                String lunchHeader = "午餐";
                float lunchTextWidth = font.getStringWidth(lunchHeader) / 1000 * smallFontSize;
                float lunchTextX = breakfastRightX + (lunchWidth - lunchTextWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(lunchTextX, firstRowTextY);
                contentStream.showText(lunchHeader);
                contentStream.endText();

                // 写入"晚餐"(第一行)
                String dinnerHeader = "晚餐";
                float dinnerTextWidth = font.getStringWidth(dinnerHeader) / 1000 * smallFontSize;
                float dinnerTextX = lunchRightX + (dinnerWidth - dinnerTextWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(dinnerTextX, firstRowTextY);
                contentStream.showText(dinnerHeader);
                contentStream.endText();

                // 写入实际兑现情况表头文字（第一行）
                String actualHeader = "实际兑现情况";
                float actualTextWidth = font.getStringWidth(actualHeader) / 1000 * smallFontSize;
                float actualTextX = menuRightX + (checkWidth - actualTextWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(actualTextX, firstRowTextY);
                contentStream.showText(actualHeader);
                contentStream.endText();

                // 写入早餐子表头（第二行）- 调整垂直位置至两行合并后的中心点
                float combinedHeight = subHeaderRowHeight + thirdRowHeight;
                float earlyBreakfastTextY = firstMidLineY - combinedHeight / 2 - smallFontSize / 3;

                // 写入"稀饭"
                String porridgeHeader = "稀饭";
                float porridgeTextWidth = font.getStringWidth(porridgeHeader) / 1000 * smallFontSize;
                float porridgeTextX = col1RightX + (col2Width - porridgeTextWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(porridgeTextX, earlyBreakfastTextY);
                contentStream.showText(porridgeHeader);
                contentStream.endText();

                // 写入"点心"
                String snackHeader = "点心";
                float snackTextWidth = font.getStringWidth(snackHeader) / 1000 * smallFontSize;
                float snackTextX = col2RightX + (col3Width - snackTextWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(snackTextX, earlyBreakfastTextY);
                contentStream.showText(snackHeader);
                contentStream.endText();

                // 写入"其它"
                String otherHeader = "其它";
                float otherTextWidth = font.getStringWidth(otherHeader) / 1000 * smallFontSize;
                float otherTextX = col3RightX + (col4Width - otherTextWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(otherTextX, earlyBreakfastTextY);
                contentStream.showText(otherHeader);
                contentStream.endText();

                // 写入午餐子表头（第二行） - 调整垂直位置至两行合并后的中心点
                // 写入"A"
                String lunchA = "A";
                float lunchAWidth = font.getStringWidth(lunchA) / 1000 * smallFontSize;
                float lunchAX = breakfastRightX + (col5Width - lunchAWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(lunchAX, earlyBreakfastTextY); // 使用与早餐列相同的垂直居中位置
                contentStream.showText(lunchA);
                contentStream.endText();

                // 写入"B"
                String lunchB = "B";
                float lunchBWidth = font.getStringWidth(lunchB) / 1000 * smallFontSize;
                float lunchBX = col5RightX + (col6Width - lunchBWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(lunchBX, earlyBreakfastTextY); // 使用与早餐列相同的垂直居中位置
                contentStream.showText(lunchB);
                contentStream.endText();

                // 写入"C"
                String lunchC = "C";
                float lunchCWidth = font.getStringWidth(lunchC) / 1000 * smallFontSize;
                float lunchCX = col6RightX + (col7Width - lunchCWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(lunchCX, earlyBreakfastTextY); // 使用与早餐列相同的垂直居中位置
                contentStream.showText(lunchC);
                contentStream.endText();

                // 写入"D"
                String lunchD = "D";
                float lunchDWidth = font.getStringWidth(lunchD) / 1000 * smallFontSize;
                float lunchDX = col7RightX + (col8Width - lunchDWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(lunchDX, earlyBreakfastTextY); // 使用与早餐列相同的垂直居中位置
                contentStream.showText(lunchD);
                contentStream.endText();

                // 写入晚餐子表头（第二行） - 使用与早餐和午餐相同的垂直居中位置
                // 写入"A"
                String dinnerA = "A";
                float dinnerAWidth = font.getStringWidth(dinnerA) / 1000 * smallFontSize;
                float dinnerAX = lunchRightX + (col9Width - dinnerAWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(dinnerAX, earlyBreakfastTextY); // 使用与早餐列相同的垂直居中位置
                contentStream.showText(dinnerA);
                contentStream.endText();

                // 写入"B"
                String dinnerB = "B";
                float dinnerBWidth = font.getStringWidth(dinnerB) / 1000 * smallFontSize;
                float dinnerBX = col9RightX + (col10Width - dinnerBWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(dinnerBX, earlyBreakfastTextY); // 使用与早餐列相同的垂直居中位置
                contentStream.showText(dinnerB);
                contentStream.endText();

                // 写入"C"
                String dinnerC = "C";
                float dinnerCWidth = font.getStringWidth(dinnerC) / 1000 * smallFontSize;
                float dinnerCX = col10RightX + (col11Width - dinnerCWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(dinnerCX, earlyBreakfastTextY); // 使用与早餐列相同的垂直居中位置
                contentStream.showText(dinnerC);
                contentStream.endText();

                // 写入"D"
                String dinnerD = "D";
                float dinnerDWidth = font.getStringWidth(dinnerD) / 1000 * smallFontSize;
                float dinnerDX = col11RightX + (col12Width - dinnerDWidth) / 2;

                contentStream.beginText();
                contentStream.newLineAtOffset(dinnerDX, earlyBreakfastTextY); // 使用与早餐列相同的垂直居中位置
                contentStream.showText(dinnerD);
                contentStream.endText();

                // 写入实际兑现情况子表头文字（第二行）
                // 写入"早餐" - 改为纵向显示，水平垂直居中
                String zaoChar = "早";
                String canChar = "餐";
                float zaoWidth = font.getStringWidth(zaoChar) / 1000 * smallFontSize;
                float canWidth = font.getStringWidth(canChar) / 1000 * smallFontSize;

                // 计算水平居中位置 - 取两个字符中宽度较大者进行居中
                float checkBreakfastCenterX = menuRightX + checkBreakfastWidth / 2;

                // 计算垂直方向上的总高度 (两个字符高度加上间距)
                float charSpacing = smallFontSize * 0.5f; // 字符间距为字体大小的一半
                float totalTextHeight = (smallFontSize * 2) + charSpacing;

                // 计算第一个字符的垂直位置 - 整体垂直居中，并向下移动一个字高度
                float verticalCenter = tableTopY - totalHeaderHeight / 2;
                float zaoY = verticalCenter + totalTextHeight / 2 - smallFontSize * 2; // 向下移动一个字高度
                float canY = zaoY - smallFontSize - charSpacing;

                // 绘制"早"字
                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkBreakfastCenterX - zaoWidth / 2, zaoY);
                contentStream.showText(zaoChar);
                contentStream.endText();

                // 绘制"餐"字
                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkBreakfastCenterX - canWidth / 2, canY);
                contentStream.showText(canChar);
                contentStream.endText();

                // 写入午餐标题 - 确保水平和垂直居中
                String checkLunchHeader = "午餐";
                float checkLunchHeaderWidth = font.getStringWidth(checkLunchHeader) / 1000 * smallFontSize;
                float checkLunchHeaderX = checkBreakfastRightX + (checkLunchWidth - checkLunchHeaderWidth) / 2;

                // 使用与日期相同的垂直居中计算方式
                float checkLunchHeaderY = tableTopY - totalHeaderHeight / 2 - smallFontSize / 3;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkLunchHeaderX, checkLunchHeaderY);
                contentStream.showText(checkLunchHeader);
                contentStream.endText();

                // 写入晚餐标题 - 确保水平和垂直居中
                String checkDinnerHeader = "晚餐";
                float checkDinnerHeaderWidth = font.getStringWidth(checkDinnerHeader) / 1000 * smallFontSize;
                float checkDinnerHeaderX = checkLunchRightX + (checkDinnerWidth - checkDinnerHeaderWidth) / 2;

                // 使用与日期和午餐相同的垂直居中计算方式
                float checkDinnerHeaderY = tableTopY - totalHeaderHeight / 2 - smallFontSize / 3;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkDinnerHeaderX, checkDinnerHeaderY);
                contentStream.showText(checkDinnerHeader);
                contentStream.endText();

                // 第三行表头文字Y坐标
                float thirdRowTextY = secondMidLineY - thirdRowHeight / 2 - smallFontSize / 3;

                // 添加实际兑现情况午餐的四个子列A、B、C、D标签
                // 写入午餐子列"A"
                String checkLunchA = "A";
                float checkLunchATextWidth = font.getStringWidth(checkLunchA) / 1000 * smallFontSize;
                float checkLunchATextX = checkBreakfastRightX + (checkLunchColWidth - checkLunchATextWidth) / 2;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkLunchATextX, thirdRowTextY);
                contentStream.showText(checkLunchA);
                contentStream.endText();

                // 写入午餐子列"B"
                String checkLunchB = "B";
                float checkLunchBTextWidth = font.getStringWidth(checkLunchB) / 1000 * smallFontSize;
                float checkLunchBTextX = checkLunchAX + (checkLunchColWidth - checkLunchBTextWidth) / 2;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkLunchBTextX, thirdRowTextY);
                contentStream.showText(checkLunchB);
                contentStream.endText();

                // 写入午餐子列"C"
                String checkLunchC = "C";
                float checkLunchCTextWidth = font.getStringWidth(checkLunchC) / 1000 * smallFontSize;
                float checkLunchCTextX = checkLunchBX + (checkLunchColWidth - checkLunchCTextWidth) / 2;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkLunchCTextX, thirdRowTextY);
                contentStream.showText(checkLunchC);
                contentStream.endText();

                // 写入午餐子列"D"
                String checkLunchD = "D";
                float checkLunchDTextWidth = font.getStringWidth(checkLunchD) / 1000 * smallFontSize;
                float checkLunchDTextX = checkLunchCX + (checkLunchColWidth - checkLunchDTextWidth) / 2;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkLunchDTextX, thirdRowTextY);
                contentStream.showText(checkLunchD);
                contentStream.endText();

                // 添加实际兑现情况晚餐的四个子列A、B、C、D标签
                // 写入晚餐子列"A"
                String checkDinnerA = "A";
                float checkDinnerATextWidth = font.getStringWidth(checkDinnerA) / 1000 * smallFontSize;
                float checkDinnerATextX = checkLunchRightX + (checkDinnerColWidth - checkDinnerATextWidth) / 2;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkDinnerATextX, thirdRowTextY);
                contentStream.showText(checkDinnerA);
                contentStream.endText();

                // 写入晚餐子列"B"
                String checkDinnerB = "B";
                float checkDinnerBTextWidth = font.getStringWidth(checkDinnerB) / 1000 * smallFontSize;
                float checkDinnerBTextX = checkDinnerAX + (checkDinnerColWidth - checkDinnerBTextWidth) / 2;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkDinnerBTextX, thirdRowTextY);
                contentStream.showText(checkDinnerB);
                contentStream.endText();

                // 写入晚餐子列"C"
                String checkDinnerC = "C";
                float checkDinnerCTextWidth = font.getStringWidth(checkDinnerC) / 1000 * smallFontSize;
                float checkDinnerCTextX = checkDinnerBX + (checkDinnerColWidth - checkDinnerCTextWidth) / 2;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkDinnerCTextX, thirdRowTextY);
                contentStream.showText(checkDinnerC);
                contentStream.endText();

                // 写入晚餐子列"D"
                String checkDinnerD = "D";
                float checkDinnerDTextWidth = font.getStringWidth(checkDinnerD) / 1000 * smallFontSize;
                float checkDinnerDTextX = checkDinnerCX + (checkDinnerColWidth - checkDinnerDTextWidth) / 2;

                contentStream.beginText();
                contentStream.setFont(font, smallFontSize);
                contentStream.newLineAtOffset(checkDinnerDTextX, thirdRowTextY);
                contentStream.showText(checkDinnerD);
                contentStream.endText();

                // 开始渲染数据行
                // 计算数据行起始位置（从表头下方开始）
                float dataRowY = tableBottomY;
                float minRowHeight = 30f; // 最小行高
                float lineHeightMultiplier = 1.2f; // 行高是字体大小的倍数

                // 设置数据行的字体大小
                float dataFontSize = 7;
                contentStream.setFont(font, dataFontSize);

                // 循环处理每一天的数据
                for (Map<String, Object> row : rows) {
                    // 计算当前行需要的高度 - 先计算所有单元格的最大行数
                    int maxLines = 1; // 默认至少1行

                    // 检查早餐数据的行数
                    if (row.containsKey("breakfast")) {
                        String breakfastValue = String.valueOf(row.get("breakfast"));
                        int lines = calculateLines(breakfastValue, font, dataFontSize, col2Width * 0.9f);
                        maxLines = Math.max(maxLines, lines);
                    }

                    if (row.containsKey("breakfast1")) {
                        String breakfast1Value = String.valueOf(row.get("breakfast1"));
                        int lines = calculateLines(breakfast1Value, font, dataFontSize, col3Width * 0.9f);
                        maxLines = Math.max(maxLines, lines);
                    }

                    if (row.containsKey("breakfast2")) {
                        String breakfast2Value = String.valueOf(row.get("breakfast2"));
                        int lines = calculateLines(breakfast2Value, font, dataFontSize, col4Width * 0.9f);
                        maxLines = Math.max(maxLines, lines);
                    }

                    // 检查午餐数据的行数
                    if (row.containsKey("lunch")) {
                        String lunchValue = String.valueOf(row.get("lunch"));
                        int lines = calculateLines(lunchValue, font, dataFontSize, col5Width * 0.9f);
                        maxLines = Math.max(maxLines, lines);
                    }

                    if (row.containsKey("lunch1")) {
                        String lunch1Value = String.valueOf(row.get("lunch1"));
                        int lines = calculateLines(lunch1Value, font, dataFontSize, col6Width * 0.9f);
                        maxLines = Math.max(maxLines, lines);
                    }

                    if (row.containsKey("lunch2")) {
                        String lunch2Value = String.valueOf(row.get("lunch2"));
                        int lines = calculateLines(lunch2Value, font, dataFontSize, col7Width * 0.9f);
                        maxLines = Math.max(maxLines, lines);
                    }

                    if (row.containsKey("lunch3")) {
                        String lunch3Value = String.valueOf(row.get("lunch3"));
                        int lines = calculateLines(lunch3Value, font, dataFontSize, col8Width * 0.9f);
                        maxLines = Math.max(maxLines, lines);
                    }

                    // 检查是否需要增加午餐子行
                    boolean hasLunch4 = row.containsKey("lunch4") && row.get("lunch4") != null &&
                            !String.valueOf(row.get("lunch4")).trim().isEmpty();

                    // 计算lunch4所需的行数
                    int lunch4Lines = 0;
                    if (hasLunch4) {
                        String lunch4Value = String.valueOf(row.get("lunch4"));
                        lunch4Lines = calculateLines(lunch4Value, font, dataFontSize, lunchWidth * 0.9f);
                    }

                    // 计算实际所需行高（行数 * 行高）
                    float actualLineHeight = dataFontSize * lineHeightMultiplier;

                    // 计算基本行高 - 基于所有常规单元格的最大行数
                    float mainRowHeight = Math.max(minRowHeight, maxLines * actualLineHeight + 10); // 主行高度

                    // 计算午餐子行所需的最小高度，为子行保留足够空间
                    float minimumLunch4Height = 0;
                    if (hasLunch4) {
                        // 子行高度至少为minRowHeight的60%，或者基于行数计算的高度
                        minimumLunch4Height = Math.max(minRowHeight * 0.6f, lunch4Lines * actualLineHeight + 5);
                    }

                    // 计算总行高
                    float requiredRowHeight = mainRowHeight;

                    // 子行高度
                    float lunch4Height = 0;

                    if (hasLunch4) {
                        // 检查当前行高是否已包含足够空间
                        float availableSpaceForLunch4 = mainRowHeight * 0.4f; // 预留现有行高的40%给子行

                        if (minimumLunch4Height <= availableSpaceForLunch4) {
                            // 现有空间足够，使用现有空间
                            lunch4Height = availableSpaceForLunch4;
                        } else {
                            // 现有空间不足，需要额外增加空间
                            lunch4Height = minimumLunch4Height;
                            requiredRowHeight = mainRowHeight * 0.75f + lunch4Height; // 主行保留75%原高度，再加上子行高度
                        }
                    }

                    // 计算当前行的顶部Y坐标和底部Y坐标
                    float currentRowTopY = dataRowY;
                    float currentRowBottomY = dataRowY - requiredRowHeight;

                    // 计算子行顶部位置
                    float subRowTopY = hasLunch4 ? (currentRowTopY - (requiredRowHeight - lunch4Height)) : currentRowBottomY;

                    // 绘制行的底部横线
                    contentStream.moveTo(tableLeftX, currentRowBottomY);
                    contentStream.lineTo(tableRightX, currentRowBottomY);
                    contentStream.stroke();

                    // 绘制每一行的竖线边框
                    // 表格左侧边框
                    contentStream.moveTo(tableLeftX, currentRowTopY);
                    contentStream.lineTo(tableLeftX, currentRowBottomY);
                    contentStream.stroke();

                    // 日期列右侧边框
                    contentStream.moveTo(col1RightX, currentRowTopY);
                    contentStream.lineTo(col1RightX, currentRowBottomY);
                    contentStream.stroke();

                    // 早餐部分右侧边框
                    contentStream.moveTo(breakfastRightX, currentRowTopY);
                    contentStream.lineTo(breakfastRightX, currentRowBottomY);
                    contentStream.stroke();

                    // 早餐内部子列分隔线
                    contentStream.moveTo(col2RightX, currentRowTopY);
                    contentStream.lineTo(col2RightX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(col3RightX, currentRowTopY);
                    contentStream.lineTo(col3RightX, currentRowBottomY);
                    contentStream.stroke();

                    // 午餐部分右侧边框
                    contentStream.moveTo(lunchRightX, currentRowTopY);
                    contentStream.lineTo(lunchRightX, currentRowBottomY);
                    contentStream.stroke();

                    // 午餐内部子列分隔线
                    contentStream.moveTo(col5RightX, currentRowTopY);
                    contentStream.lineTo(col5RightX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(col6RightX, currentRowTopY);
                    contentStream.lineTo(col6RightX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(col7RightX, currentRowTopY);
                    contentStream.lineTo(col7RightX, currentRowBottomY);
                    contentStream.stroke();

                    // 晚餐部分内部子列分隔线
                    contentStream.moveTo(col9RightX, currentRowTopY);
                    contentStream.lineTo(col9RightX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(col10RightX, currentRowTopY);
                    contentStream.lineTo(col10RightX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(col11RightX, currentRowTopY);
                    contentStream.lineTo(col11RightX, currentRowBottomY);
                    contentStream.stroke();

                    // 菜单与实际兑现部分的分隔线
                    contentStream.moveTo(menuRightX, currentRowTopY);
                    contentStream.lineTo(menuRightX, currentRowBottomY);
                    contentStream.stroke();

                    // 实际兑现情况部分的分隔线
                    contentStream.moveTo(checkBreakfastRightX, currentRowTopY);
                    contentStream.lineTo(checkBreakfastRightX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(checkLunchRightX, currentRowTopY);
                    contentStream.lineTo(checkLunchRightX, currentRowBottomY);
                    contentStream.stroke();

                    // 实际兑现情况午餐内部子列分隔线
                    contentStream.moveTo(checkLunchAX, currentRowTopY);
                    contentStream.lineTo(checkLunchAX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(checkLunchBX, currentRowTopY);
                    contentStream.lineTo(checkLunchBX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(checkLunchCX, currentRowTopY);
                    contentStream.lineTo(checkLunchCX, currentRowBottomY);
                    contentStream.stroke();

                    // 实际兑现情况晚餐内部子列分隔线
                    contentStream.moveTo(checkDinnerAX, currentRowTopY);
                    contentStream.lineTo(checkDinnerAX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(checkDinnerBX, currentRowTopY);
                    contentStream.lineTo(checkDinnerBX, currentRowBottomY);
                    contentStream.stroke();

                    contentStream.moveTo(checkDinnerCX, currentRowTopY);
                    contentStream.lineTo(checkDinnerCX, currentRowBottomY);
                    contentStream.stroke();

                    // 表格右侧边框
                    contentStream.moveTo(tableRightX, currentRowTopY);
                    contentStream.lineTo(tableRightX, currentRowBottomY);
                    contentStream.stroke();

                    // 渲染日期数据（两行显示）
                    if (row.containsKey("date")) {
                        String dateValue = String.valueOf(row.get("date"));
                        String weekdayValue = getWeekDay(dateValue); // 根据日期获取星期几

                        // 计算日期文本位置（水平居中）
                        float dateValueWidth = font.getStringWidth(dateValue) / 1000 * (dataFontSize);
                        float dateValueX = tableLeftX + (col1Width - dateValueWidth) / 2;
                        float dateValueY = currentRowTopY - 10; // 日期在上面

                        // 计算星期几文本位置（水平居中）
                        float weekdayValueWidth = font.getStringWidth(weekdayValue) / 1000 * dataFontSize;
                        float weekdayValueX = tableLeftX + (col1Width - weekdayValueWidth) / 2;
                        float weekdayValueY = currentRowTopY - 20; // 星期几在下面

                        // 渲染日期
                        contentStream.beginText();
                        contentStream.newLineAtOffset(dateValueX, dateValueY);
                        contentStream.showText(dateValue);
                        contentStream.endText();

                        // 渲染星期几
                        contentStream.beginText();
                        contentStream.newLineAtOffset(weekdayValueX, weekdayValueY);
                        contentStream.showText(weekdayValue);
                        contentStream.endText();
                    }

                    // 渲染早餐数据
                    // 渲染稀饭列数据
                    if (row.containsKey("breakfast")) {
                        String breakfastValue = String.valueOf(row.get("breakfast"));

                        // 计算可用文本区域
                        float availableWidth = col2Width * 0.9f;
                        float cellCenterY = currentRowTopY - requiredRowHeight / 2;

                        // 使用自动换行方法
                        drawMultiLineText(contentStream, breakfastValue, font, dataFontSize,
                                col1RightX + col2Width / 2, cellCenterY, availableWidth);
                    }

                    // 渲染点心列数据
                    if (row.containsKey("breakfast1")) {
                        String breakfast1Value = String.valueOf(row.get("breakfast1"));

                        // 计算可用文本区域
                        float availableWidth = col3Width * 0.9f;
                        float cellCenterY = currentRowTopY - requiredRowHeight / 2;

                        // 使用自动换行方法
                        drawMultiLineText(contentStream, breakfast1Value, font, dataFontSize,
                                col2RightX + col3Width / 2, cellCenterY, availableWidth);
                    }

                    // 渲染其它列数据
                    if (row.containsKey("breakfast2")) {
                        String breakfast2Value = String.valueOf(row.get("breakfast2"));

                        // 计算可用文本区域
                        float availableWidth = col4Width * 0.9f;
                        float cellCenterY = currentRowTopY - requiredRowHeight / 2;

                        // 使用自动换行方法
                        drawMultiLineText(contentStream, breakfast2Value, font, dataFontSize,
                                col3RightX + col4Width / 2, cellCenterY, availableWidth);
                    }

                    // 渲染午餐数据
                    // 渲染午餐A列数据
                    if (row.containsKey("lunch")) {
                        String lunchValue = String.valueOf(row.get("lunch"));

                        // 计算可用文本区域
                        float availableWidth = col5Width * 0.9f;

                        // 计算主行文本的垂直位置
                        float mainRowCenterY;
                        if (hasLunch4) {
                            // 如果有子行，主行文本居中显示在主行区域
                            float mainRowArea = requiredRowHeight - lunch4Height;
                            mainRowCenterY = currentRowTopY - mainRowArea * 0.5f;
                        } else {
                            // 没有子行，文本垂直居中
                            mainRowCenterY = currentRowTopY - requiredRowHeight / 2;
                        }

                        // 使用自动换行方法
                        drawMultiLineText(contentStream, lunchValue, font, dataFontSize,
                                breakfastRightX + col5Width / 2, mainRowCenterY, availableWidth);
                    }

                    // 渲染午餐B列数据
                    if (row.containsKey("lunch1")) {
                        String lunch1Value = String.valueOf(row.get("lunch1"));

                        // 计算可用文本区域
                        float availableWidth = col6Width * 0.9f;

                        // 计算主行文本的垂直位置
                        float mainRowCenterY;
                        if (hasLunch4) {
                            // 如果有子行，主行文本居中显示在主行区域
                            float mainRowArea = requiredRowHeight - lunch4Height;
                            mainRowCenterY = currentRowTopY - mainRowArea * 0.5f;
                        } else {
                            // 没有子行，文本垂直居中
                            mainRowCenterY = currentRowTopY - requiredRowHeight / 2;
                        }

                        // 使用自动换行方法
                        drawMultiLineText(contentStream, lunch1Value, font, dataFontSize,
                                col5RightX + col6Width / 2, mainRowCenterY, availableWidth);
                    }

                    // 渲染午餐C列数据
                    if (row.containsKey("lunch2")) {
                        String lunch2Value = String.valueOf(row.get("lunch2"));

                        // 计算可用文本区域
                        float availableWidth = col7Width * 0.9f;

                        // 计算主行文本的垂直位置
                        float mainRowCenterY;
                        if (hasLunch4) {
                            // 如果有子行，主行文本居中显示在主行区域
                            float mainRowArea = requiredRowHeight - lunch4Height;
                            mainRowCenterY = currentRowTopY - mainRowArea * 0.5f;
                        } else {
                            // 没有子行，文本垂直居中
                            mainRowCenterY = currentRowTopY - requiredRowHeight / 2;
                        }

                        // 使用自动换行方法
                        drawMultiLineText(contentStream, lunch2Value, font, dataFontSize,
                                col6RightX + col7Width / 2, mainRowCenterY, availableWidth);
                    }

                    // 渲染午餐D列数据
                    if (row.containsKey("lunch3")) {
                        String lunch3Value = String.valueOf(row.get("lunch3"));

                        // 计算可用文本区域
                        float availableWidth = col8Width * 0.9f;

                        // 计算主行文本的垂直位置
                        float mainRowCenterY;
                        if (hasLunch4) {
                            // 如果有子行，主行文本居中显示在主行区域
                            float mainRowArea = requiredRowHeight - lunch4Height;
                            mainRowCenterY = currentRowTopY - mainRowArea * 0.5f;
                        } else {
                            // 没有子行，文本垂直居中
                            mainRowCenterY = currentRowTopY - requiredRowHeight / 2;
                        }

                        // 使用自动换行方法
                        drawMultiLineText(contentStream, lunch3Value, font, dataFontSize,
                                col7RightX + col8Width / 2, mainRowCenterY, availableWidth);
                    }

                    // 如果存在lunch4，添加子行并合并A B C D四列显示内容
                    if (hasLunch4) {
                        // 绘制子行的横线分隔线（与主行数据间的分隔线）
                        contentStream.moveTo(breakfastRightX, subRowTopY);
                        contentStream.lineTo(lunchRightX, subRowTopY);
                        contentStream.stroke();

                        // 获取lunch4的内容
                        String lunch4Value = String.valueOf(row.get("lunch4"));

                        // 计算合并单元格的宽度和中心点
                        float mergedWidth = lunchWidth;
                        float mergedCenterX = breakfastRightX + mergedWidth / 2;

                        // 计算子行文本的垂直位置 - 居中在子行区域
                        float mergedCenterY = subRowTopY - lunch4Height / 2;

                        // 在合并单元格中绘制文本
                        drawMultiLineText(contentStream, lunch4Value, font, dataFontSize,
                                mergedCenterX, mergedCenterY, mergedWidth * 0.9f);
                    }

                    // 更新下一行的Y坐标
                    dataRowY = currentRowBottomY;
                }

                // TODO: 根据data参数添加更多内容
            } finally {
                // 关闭内容流
                contentStream.close();
            }

            // 先保存文档到文件
            document.save(outputFile);
            System.out.println("PDF文件已生成: " + fileName);
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("PDF导出失败: " + e.getMessage());
        }

        // 返回文件对象而不是创建新的文件输出流
        return outputFile;
    }

    /**
     * 根据日期获取星期几
     *
     * @param dateStr 日期字符串，格式为yyyy-MM-dd
     * @return 星期几
     */
    private String getWeekDay(String dateStr) {
        try {
            java.time.LocalDate date = java.time.LocalDate.parse(dateStr);
            java.time.DayOfWeek dayOfWeek = date.getDayOfWeek();

            switch (dayOfWeek) {
                case MONDAY:
                    return "星期一";
                case TUESDAY:
                    return "星期二";
                case WEDNESDAY:
                    return "星期三";
                case THURSDAY:
                    return "星期四";
                case FRIDAY:
                    return "星期五";
                case SATURDAY:
                    return "星期六";
                case SUNDAY:
                    return "星期日";
                default:
                    return "";
            }
        } catch (Exception e) {
            // 日期格式解析失败时，返回空字符串
            return "";
        }
    }

    /**
     * 加载支持中文的字体
     *
     * @param document PDF文档对象
     * @return 支持中文的字体
     * @throws IOException 如果字体加载失败
     */
    private PDFont loadChineseFont(PDDocument document) throws IOException {
        // 首先尝试从项目根目录加载字体
        String projectRoot = System.getProperty("user.dir");

        // 尝试不同的目录路径
        String[] fontPaths = {
                projectRoot + File.separator + "font" + File.separator + "simhei.ttf",
                projectRoot + File.separator + "fonts" + File.separator + "simhei.ttf",
                "C:/Windows/Fonts/simhei.ttf",
                "C:/Windows/Fonts/msyh.ttf"
        };

        // 尝试加载字体
        for (String fontPath : fontPaths) {
            File fontFile = new File(fontPath);
            if (fontFile.exists()) {
                try {
                    System.out.println("正在从项目根目录加载字体: " + fontPath);
                    return PDType0Font.load(document, fontFile);
                } catch (Exception e) {
                    System.err.println("加载字体失败: " + fontPath + ", 错误: " + e.getMessage());
                }
            }
        }

        // 如果找不到外部字体文件，创建fonts目录并复制Windows字体
        try {
            String targetDir = projectRoot + File.separator + "font";
            File fontDir = new File(targetDir);
            if (!fontDir.exists()) {
                fontDir.mkdirs();
            }

            File windowsSimhei = new File("C:/Windows/Fonts/simhei.ttf");
            if (windowsSimhei.exists()) {
                File targetFont = new File(targetDir + File.separator + "simhei.ttf");
                Files.copy(windowsSimhei.toPath(), targetFont.toPath(), StandardCopyOption.REPLACE_EXISTING);
                System.out.println("已复制字体文件到: " + targetFont.getAbsolutePath());
                return PDType0Font.load(document, targetFont);
            }
        } catch (Exception e) {
            System.err.println("复制字体文件失败: " + e.getMessage());
        }

        throw new IOException("无法加载中文字体，请确保系统中安装了SimHei或其他中文字体");
    }

    /**
     * 计算文本在指定宽度下需要的行数
     *
     * @param text     需要计算的文本
     * @param font     字体
     * @param fontSize 字体大小
     * @param maxWidth 最大宽度
     * @return 需要的行数
     * @throws IOException 如果发生I/O错误
     */
    private int calculateLines(String text, PDFont font, float fontSize, float maxWidth) throws IOException {
        if (text == null || text.trim().isEmpty()) {
            return 1; // 空文本仍然占用1行
        }

        // 按逗号分割文本
        String[] sections = text.split(",");
        int lineCount = 0;

        for (String section : sections) {
            section = section.trim();
            if (section.isEmpty()) continue;

            float sectionWidth = font.getStringWidth(section) / 1000 * fontSize;

            if (sectionWidth <= maxWidth) {
                lineCount++; // 这一部分占一行
            } else {
                // 需要进一步分割这部分文本
                StringBuilder currentLine = new StringBuilder();
                char[] chars = section.toCharArray();

                for (char c : chars) {
                    String charStr = String.valueOf(c);
                    float currentLineWidth = font.getStringWidth(currentLine.toString() + charStr) / 1000 * fontSize;

                    if (currentLineWidth <= maxWidth) {
                        currentLine.append(charStr);
                    } else {
                        lineCount++;
                        currentLine = new StringBuilder(charStr);
                    }
                }

                // 最后一行
                if (currentLine.length() > 0) {
                    lineCount++;
                }
            }
        }

        return Math.max(1, lineCount); // 确保至少返回1行
    }

    /**
     * 在指定位置绘制自动换行的文本
     *
     * @param contentStream PDF内容流
     * @param text          需要绘制的文本
     * @param font          字体
     * @param fontSize      字体大小
     * @param centerX       文本中心X坐标
     * @param centerY       文本中心Y坐标
     * @param maxWidth      文本最大宽度
     * @throws IOException 如果发生I/O错误
     */
    private void drawMultiLineText(PDPageContentStream contentStream, String text, PDFont font,
                                   float fontSize, float centerX, float centerY, float maxWidth) throws IOException {
        // 如果文本为空或null，不绘制任何内容
        if (text == null || text.trim().isEmpty()) {
            return;
        }

        // 按逗号分割文本，作为自然分段
        String[] sections = text.split(",");
        List<String> lines = new ArrayList<>();

        // 处理每个部分，必要时继续分行
        for (String section : sections) {
            section = section.trim();
            if (section.isEmpty()) continue;

            float sectionWidth = font.getStringWidth(section) / 1000 * fontSize;

            // 如果部分文本宽度小于最大宽度，直接添加为一行
            if (sectionWidth <= maxWidth) {
                lines.add(section);
            } else {
                // 否则，需要逐字分割这部分文本
                StringBuilder currentLine = new StringBuilder();
                // 逐字符处理，避免使用复杂正则表达式
                char[] chars = section.toCharArray();

                for (char c : chars) {
                    String charStr = String.valueOf(c);
                    float currentLineWidth = font.getStringWidth(currentLine.toString() + charStr) / 1000 * fontSize;

                    if (currentLineWidth <= maxWidth) {
                        currentLine.append(charStr);
                    } else {
                        lines.add(currentLine.toString());
                        currentLine = new StringBuilder(charStr);
                    }
                }

                // 添加最后一行
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                }
            }
        }

        // 计算文本总高度，用于垂直居中
        float lineHeight = fontSize * 1.2f; // 行高为字体大小的1.2倍
        float totalHeight = lineHeight * lines.size();
        float startY = centerY + totalHeight / 2 - fontSize / 2;

        // 绘制每一行文字
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            float lineWidth = font.getStringWidth(line) / 1000 * fontSize;
            float lineX = centerX - lineWidth / 2;
            float lineY = startY - i * lineHeight;

            contentStream.beginText();
            contentStream.setFont(font, fontSize);
            contentStream.newLineAtOffset(lineX, lineY);
            contentStream.showText(line);
            contentStream.endText();
        }
    }
}
