package com.ruoyi.core.export;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class WordTest {

    /**
     * 测试导出入住申请表Word文档
     */
    public static void main(String[] args) {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "张三");
        data.put("gender", "男");
        data.put("age", "65");
        data.put("ancestral_home", "上海市");
        data.put("date_of_birth", "1956-06-15");
        data.put("ethnic_group", "汉族");
        data.put("zheng_zh_m_m", "群众");
        data.put("marital_status", "已婚");
        data.put("licence", "110101195506153215");
        data.put("licenceSN", "110101195506153215");

        try {
            // 调用导出方法
            Word word = new Word();
            File outputFile = word.exportWord(data);

            if (outputFile != null && outputFile.exists()) {
                System.out.println("Word文档导出成功！");
                System.out.println("文档路径: " + outputFile.getAbsolutePath());
            } else {
                System.out.println("Word文档导出失败！");
            }
        } catch (Exception e) {
            System.out.println("导出过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
