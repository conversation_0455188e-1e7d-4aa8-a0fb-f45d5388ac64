package com.ruoyi.core.mapper;

import com.ruoyi.core.entity.PsoDataDict;
import com.ruoyi.core.entity.PsoViewDict;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface PsoViewDictMapper {

    /**
     * 新增节点
     *
     * @param node 节点实体
     */
    @Insert("""
                INSERT INTO PSO_VIEW_DICT (
                   view_code,script_out,field_name,field_display,field_format,field_config,bind_dimen,bind_tag
                ) VALUES (
                  #{Code}, #{ScriptOut}, #{Field}, #{FieldDisplay},#{FieldFormat}, #{FieldConfig}, #{BindDimen}, #{BindTag}
                         )
            """)
    int insert(PsoViewDict node);

}
