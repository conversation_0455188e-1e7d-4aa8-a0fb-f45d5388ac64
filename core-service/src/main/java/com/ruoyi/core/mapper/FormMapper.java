package com.ruoyi.core.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface FormMapper {

    /**
     * @description 表单右侧分页列表
     * <AUTHOR>
     */
    List<Map<String, Object>> formList(
            @Param("dataCode") String dataCode,
            @Param("appId") String appId,
            @Param("dataSName") String dataSName,
            @Param("state") String state);

    /**
     * @description 表单详情
     * <AUTHOR>
     */
    @Select("""
            select * from node_ext_data where data_code = #{dataCode}
            """)
    Map<String, Object> formInfo(String dataCode);

    /**
     * @description 流程右侧分页列表
     * <AUTHOR>
     */
    List<Map<String, Object>> flowList(@Param("appId") String appId,
                                       @Param("wfCode") String wfCodem,
                                       @Param("wfSname") String wfSname,
                                       @Param("state") String state);

    /**
     * @description 视图右侧分页列表
     * <AUTHOR>
     */
    List<Map<String, Object>> viewList(@Param("appId") String appId,
                                       @Param("viewCode") String viewCode,
                                       @Param("viewSName") String viewSName,
                                       @Param("state") String state);
}
