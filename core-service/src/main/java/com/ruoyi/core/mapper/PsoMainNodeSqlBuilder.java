package com.ruoyi.core.mapper;

import cn.hutool.core.util.StrUtil;
import org.apache.ibatis.jdbc.SQL;

import java.util.Map;
import java.util.Objects;

public class PsoMainNodeSqlBuilder {
    public String update(Map<String, Object> params) {
        return new SQL() {{
            UPDATE("pso_main_node");
            if (Objects.nonNull(params.get("node_name")) && StrUtil.isNotBlank(params.get("node_name").toString())) {
                SET("node_name = #{node_name}");
            }
            if (Objects.nonNull(params.get("node_sname")) && StrUtil.isNotBlank(params.get("node_sname").toString())) {
                SET("node_sname = #{node_sname}");
            }
            if (Objects.nonNull(params.get("node_icon")) && StrUtil.isNotBlank(params.get("node_icon").toString())) {
                SET("node_icon = #{node_icon}");
            }
            WHERE("node_id = #{node_id}");
        }}.toString();
    }
}
