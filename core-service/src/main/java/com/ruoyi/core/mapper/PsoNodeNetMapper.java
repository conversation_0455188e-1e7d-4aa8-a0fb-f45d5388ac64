package com.ruoyi.core.mapper;

import com.ruoyi.core.entity.PsoNodeNet;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PsoNodeNetMapper {

    /**
     * @description 新增节点关系网
     * @param psoNodeNet 节点关系网实体
     */
    @Insert("""
            INSERT INTO pso_node_net (
                node_id,
                node_pid,
                node_path,
                node_order
            ) VALUES (
                         #{nodeId},
                         #{nodePid},
                         #{nodePath},
                         #{nodeOrder}
                     )
    """)
    int insert(PsoNodeNet psoNodeNet);

    /**
     * 删除节点网络关系
     *
     * @param nodeId 节点ID
     * @return 影响行数
     */
    @Delete("""
            DELETE FROM pso_node_net WHERE node_id = #{nodeId}
            """)
    int deleteNodeNet(String nodeId);
}
