package com.ruoyi.core.mapper;

import com.ruoyi.core.entity.PsoDataDict;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PsoDataDictMapper {

    /**
     * 新增节点
     *
     * @param node 节点实体
     */
    @Insert("""
                INSERT INTO PSO_DATA_DICT (
                   app_id,auto_no,data_code,field_name,field_type,field_len,field_format,output_format,control_type,is_sys,
                   is_encry,field_display,is_pkey,is_title,open_fail,open_sense,field_config,bind_dimen,is_secret,encry_type
                ) VALUES (
                             #{appId}, #{AutoNo}, #{Code}, #{Field}, #{FieldType}, #{FieldLen},
                             #{FieldFormat}, #{OutFormat}, #{ControlType}, #{IsSys}, #{IsEncry}, #{FieldDisplay},
                             #{IsPkey}, #{IsTitle}, #{OpenFail}, #{OpenSense}, #{FieldConfig}, #{BindDimen}, #{IsSecret}, #{EncryType}
                         )
            """)
    int insert(PsoDataDict node);

    List<PsoDataDict> selectDictList(String Code);

    int updateExistDict(PsoDataDict entity);


    /**
     * 删除表单字典
     *
     * @param code 节点ID
     * @return 影响行数
     */
    @Delete("""
            DELETE FROM PSO_DATA_DICT WHERE data_code = #{code} and field_name=  #{field}
            """)
    int deleteField(String code,String field);
}
