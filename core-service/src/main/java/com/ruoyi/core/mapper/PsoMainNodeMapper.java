package com.ruoyi.core.mapper;

import com.ruoyi.core.entity.PsoMainNode;
import com.ruoyi.core.entity.RoleAuthStruct;
import org.apache.ibatis.annotations.*;

import java.util.Map;

@Mapper
public interface PsoMainNodeMapper {

    /**
     * 新增节点
     *
     * @param node 节点实体
     */
    @Insert("""
                INSERT INTO pso_main_node (
                    app_id, node_id, node_name, node_sname, node_type, node_status,
                       node_note, node_icon, node_tag, create_time, node_pos, node_auth,
                    node_renter, node_cate, company_id, creator
                ) VALUES (
                             #{appId}, #{nodeId}, #{nodeName}, #{nodeSname}, #{nodeType}, #{nodeStatus},
                             #{nodeNote}, #{nodeIcon}, #{nodeTag}, #{createTime}, #{nodePos}, #{nodeAuth},
                             #{nodeRenter}, #{nodeCate}, #{companyId}, #{creator}
                         )
            """)
    int insert(PsoMainNode node);

    /**
     * 查询节点信息
     *
     * @param nodeId 节点id
     */
    @Select("""
            SELECT
                        ta.*,
                        tb.node_pid,
                        tb.node_path,
                        tb.node_order
                    FROM
                        PSO_MAIN_NODE ta
                            LEFT JOIN
                        pso_node_net tb
                        ON
                            ta.node_id = tb.node_id
                    WHERE
                        ta.node_id = #{nodeId}
            """)
    Map<String, Object> selectNodeInfo(String nodeId);

    /**
     * @description 新增节点权限
     * <AUTHOR>
     */
    @Insert("""
                INSERT INTO role_auth_struct
                (auto_no, role_id, auth_type, node_type, node_id)
                VALUES
                (#{autoNo}, #{roleId}, #{authType}, #{nodeType}, #{nodeId})
            """)
    int insertRoleAuthStruct(RoleAuthStruct roleAuthStruct);

    /**
     * @description 节点详情
     * <AUTHOR>
     */
    @Select("""
                select * from pso_main_node where node_id = #{nodeId}
            """)
    Map<String, Object> nodeInfo(String nodeId);

    /**
     * @description 编辑文件夹/目录/节点
     * <AUTHOR>
     */
    @UpdateProvider(type = PsoMainNodeSqlBuilder.class, method = "update")
    int update(Map<String, Object> params);

    /**
     * 检查节点是否有子节点
     *
     * @param nodeId 节点ID
     * @return 子节点数量
     */
    @Select("""
            SELECT COUNT(*) FROM pso_node_net WHERE node_pid = #{nodeId}
            """)
    int countChildrenNodes(String nodeId);

    /**
     * 检查节点是否有数据
     *
     * @param nodeId 节点ID
     * @return 数据数量
     */
    @Select("""
            SELECT COUNT(*) FROM pso_node_data WHERE node_id = #{nodeId}
            """)
    int countNodeData(String nodeId);

    /**
     * 删除主节点信息
     *
     * @param nodeId 节点ID
     * @return 影响行数
     */
    @Delete("""
            DELETE FROM pso_main_node WHERE node_id = #{nodeId}
            """)
    int deleteMainNode(String nodeId);
}
