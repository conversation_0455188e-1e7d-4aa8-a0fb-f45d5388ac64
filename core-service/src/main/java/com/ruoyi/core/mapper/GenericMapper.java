package com.ruoyi.core.mapper;

import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

public interface GenericMapper {
    @InsertProvider(type = GenericSqlBuilder.class, method = "buildCreate")
    void create(@Param("tableName") String table, @Param("dataMap") Map<String, Object> dataMap);

    @UpdateProvider(type = GenericSqlBuilder.class, method = "buildUpdate")
    int update(@Param("tableName") String table,
               @Param("dataMap") Map<String, Object> dataMap,
               @Param("condition") Map<String, Object> condition);

    @SelectProvider(type = GenericSqlBuilder.class, method = "buildGetList")
    List<Map<String, Object>> getList(@Param("tableName") String table,
                                      @Param("column") String column,
                                      @Param("condition") List<String[]> condition,
                                      @Param("last") String last);

    @DeleteProvider(type = GenericSqlBuilder.class, method = "buildRemove")
    int delete(@Param("tableName") String table,
               @Param("condition") List<String[]> condition);
}
