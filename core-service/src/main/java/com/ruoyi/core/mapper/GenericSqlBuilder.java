package com.ruoyi.core.mapper;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.jdbc.SQL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 通用SQL构建器
 * <p>
 * 提供动态构建SQL语句的功能，支持插入、更新和查询操作。
 * 配合MyBatis的@InsertProvider、@UpdateProvider和@SelectProvider注解使用。
 * </p>
 */
public class GenericSqlBuilder {

    /**
     * 构建通用的插入SQL语句
     * <p>
     * 根据提供的表名和数据映射构建INSERT语句。会自动跳过空值。
     * 与MyBatis的@InsertProvider注解配合使用。
     * </p>
     *
     * @param params 包含参数的Map，必须包含以下键:
     *               - tableName: 表名
     *               - dataMap: 包含列名和值的Map
     * @return 生成的INSERT SQL语句
     * @throws IllegalArgumentException 当表名为空或数据为空时抛出
     */
    public static String buildCreate(Map<String, Object> params) {
        String tableName = (String) params.get("tableName");
        @SuppressWarnings("unchecked")
        Map<String, Object> dataMap = (Map<String, Object>) params.get("dataMap");

        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }

        if (dataMap == null || dataMap.isEmpty()) {
            throw new IllegalArgumentException("数据不能为空");
        }

        SQL sql = new SQL().INSERT_INTO(tableName);

        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String columnName = entry.getKey();
            Object value = entry.getValue();

            // 跳过空值和ID字段（假设ID为自增）
            if (value == null) {
                continue;
            }

            sql.VALUES(columnName, "#{dataMap." + columnName + "}");
        }

        return sql.toString();
    }

    /**
     * 构建通用的更新SQL语句
     * <p>
     * 根据提供的表名、要更新的数据和条件构建UPDATE语句。
     * 会自动跳过空值和ID字段。
     * 与MyBatis的@UpdateProvider注解配合使用。
     * </p>
     *
     * @param params 包含参数的Map，必须包含以下键:
     *               - tableName: 表名
     *               - dataMap: 包含要更新的列名和值的Map
     *               - condition: 包含WHERE条件的Map，键是列名，值是条件值
     * @return 生成的UPDATE SQL语句
     * @throws IllegalArgumentException 当表名为空、更新数据为空或条件为空时抛出
     */
    public static String buildUpdate(Map<String, Object> params) {
        String tableName = (String) params.get("tableName");
        @SuppressWarnings("unchecked")
        Map<String, Object> dataMap = (Map<String, Object>) params.get("dataMap");
        @SuppressWarnings("unchecked")
        Map<String, Object> condition = (Map<String, Object>) params.get("condition");

        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }

        if (dataMap == null || dataMap.isEmpty()) {
            throw new IllegalArgumentException("更新数据不能为空");
        }

        if (condition == null || condition.isEmpty()) {
            throw new IllegalArgumentException("更新条件不能为空");
        }

        SQL sql = new SQL().UPDATE(tableName);

        // 设置更新的字段
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String columnName = entry.getKey();
            Object value = entry.getValue();

            // 跳过空值
            if (value == null || "ID".equalsIgnoreCase(columnName)) {
                continue;
            }

            sql.SET(columnName + " = #{dataMap." + columnName + "}");
        }

        // 设置WHERE条件
        for (Map.Entry<String, Object> entry : condition.entrySet()) {
            String columnName = entry.getKey();
            Object value = entry.getValue();

            if (value == null) {
                continue;
            }

            sql.WHERE(columnName + " = #{condition." + columnName + "}");
        }

        return sql.toString();
    }

    /**
     * 构建通用的查询SQL语句
     * <p>
     * 根据提供的表名和查询条件构建SELECT语句。
     * 支持多种复杂的条件操作符和JSON查询。
     * 与MyBatis的@SelectProvider注解配合使用。
     * </p>
     *
     * @param params 包含参数的Map，必须包含以下键:
     *               - tableName: 表名
     *               可选键:
     *               - column: 要查询的列，可以是字符串、字符串数组或字符串列表，默认为"*"
     *               - conditions: 查询条件列表，每个条件是一个对象数组，格式如下:
     *               ["操作符", "列名", 值, ...]
     *               支持的操作符包括：equal, not-equal, greater, greater-equal, less,
     *               less-equal, like, start-with, end-with, in, not-in, is-null,
     *               is-not-null, between, object-contain, array-contain
     *               - last: 附加在SQL语句末尾的字符串，如排序、分页等
     * @return 生成的SELECT SQL语句
     * @throws IllegalArgumentException 当表名为空时抛出
     */
    public static String buildGetList(Map<String, Object> params) {
        String tableName = (String) params.get("tableName");
        @SuppressWarnings("unchecked")
        List<Object[]> conditions = (List<Object[]>) params.get("condition");

        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }

        SQL sql = new SQL();

        Object columnObj = params.get("column");
        if (columnObj == null) {
            sql.SELECT("*");
        } else if (columnObj instanceof String) {
            sql.SELECT((String) columnObj);
        } else if (columnObj instanceof String[] columns) {
            sql.SELECT(String.join(", ", columns));
        } else if (columnObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> columns = (List<String>) columnObj;
            sql.SELECT(String.join(", ", columns));
        } else {
            sql.SELECT("*");
        }

        sql.FROM(tableName);

        for (Object[] condition : conditions) {
            if (condition == null || condition.length < 2) {
                continue;
            }

            String operator = condition[0].toString().toLowerCase();
            String column = condition[1].toString();

            switch (operator) {
                case "equal":
                    sql.WHERE(column + " = '" + condition[2].toString() + "'");
                    break;
                case "not-equal":
                    sql.WHERE(column + " != '" + condition[2].toString() + "'");
                    break;
                case "greater":
                    sql.WHERE(column + " > '" + condition[2].toString() + "'");
                    break;
                case "greater-equal":
                    sql.WHERE(column + " >= '" + condition[2].toString() + "'");
                    break;
                case "less":
                    sql.WHERE(column + " < '" + condition[2].toString() + "'");
                    break;
                case "less-equal":
                    sql.WHERE(column + " <= '" + condition[2].toString() + "'");
                    break;
                case "like":
                    sql.WHERE(column + " LIKE CONCAT('%', '" + condition[2].toString() + "', '%')");
                    break;
                case "start-with":
                    sql.WHERE(column + " LIKE CONCAT('" + condition[2].toString() + "', '%')");
                    break;
                case "end-with":
                    sql.WHERE(column + " LIKE CONCAT('%', '" + condition[2].toString() + "')");
                    break;
                case "in":
                    if (condition[2] != null) {
                        String[] values = condition[2].toString().split(",");
                        String inClause = Arrays.stream(values)
                                .map(v -> "'" + v.trim() + "'")
                                .collect(Collectors.joining(","));
                        sql.WHERE(column + " IN (" + inClause + ")");
                    }
                    break;
                case "not-in":
                    if (condition[2] != null) {
                        String[] values = condition[2].toString().split(",");
                        String notInClause = Arrays.stream(values)
                                .map(v -> "'" + v.trim() + "'")
                                .collect(Collectors.joining(","));
                        sql.WHERE(column + " NOT IN (" + notInClause + ")");
                    }
                    break;
                case "is-null":
                    sql.WHERE(column + " IS NULL");
                    break;
                case "is-not-null":
                    sql.WHERE(column + " IS NOT NULL");
                    break;
                case "between":
                    if (condition.length > 3) {
                        sql.WHERE(column + " BETWEEN '" + condition[2].toString() + "' AND '" + condition[3].toString() + "'");
                    }
                    break;
                case "object-contain":
                    if (condition.length > 3) {
                        String key = String.valueOf(condition[2]);
                        // 通用JSON查询语法，可能需要根据具体数据库调整
                        sql.WHERE("JSON_CONTAINS(" + column + ", JSON_OBJECT('" + key + "', '" + condition[3].toString() + "'))");
                    }
                    break;
                case "array-contain":
                    // 检查JSON数组字段是否包含指定值
                    sql.WHERE("JSON_CONTAINS('" + column + "', JSON_ARRAY('" + condition[2].toString() + "'))");
                    break;
                default:
                    break;
            }
        }

        String sqlString = sql.toString();

        String last = (String) params.get("last");

        if (last != null && !last.trim().isEmpty()) {
            sqlString += " " + last;
        }

        return sqlString;
    }

    public static String buildRemove(Map<String, Object> params) {
        String tableName = (String) params.get("tableName");
        @SuppressWarnings("unchecked")
        List<String[]> condition = (List<String[]>) params.get("condition");

        if (tableName == null || tableName.trim().isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }

        if (null == condition || condition.isEmpty()) {
            throw new IllegalArgumentException("条件不能为空");
        }

        SQL sql = new SQL().DELETE_FROM(tableName);

        for (String[] c : condition) {
            if (c == null || c.length < 2) {
                continue;
            }

            String operator = c[0].toLowerCase();
            String column = c[1];

            switch (operator) {
                case "equal":
                    sql.WHERE(column + " = '" + c[2] + "'");
                    break;
                case "not-equal":
                    sql.WHERE(column + " != '" + c[2] + "'");
                    break;
                case "greater":
                    sql.WHERE(column + " > '" + c[2] + "'");
                    break;
                case "greater-equal":
                    sql.WHERE(column + " >= '" + c[2] + "'");
                    break;
                case "less":
                    sql.WHERE(column + " < '" + c[2] + "'");
                    break;
                case "less-equal":
                    sql.WHERE(column + " <= '" + c[2] + "'");
                    break;
                default:
                    break;
            }
        }

        return sql.toString();
    }
}
