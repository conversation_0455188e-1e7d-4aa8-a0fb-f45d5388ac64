package com.ruoyi.core.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.service.DataAccessControlService;
import com.ruoyi.core.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/workflow")
public class WorkflowController extends BaseController {
    private final WorkflowService workflowService;
    private final DataAccessControlService dacService;

    public WorkflowController(WorkflowService workflowService, DataAccessControlService dacService) {
        this.workflowService = workflowService;
        this.dacService = dacService;
    }

    @GetMapping("/{workflowCode}")
    public ResponseEntity<Object> getWorkflow(
            @PathVariable String workflowCode
    ) {
        try {
            return ResponseEntity.ok().body(workflowService.getWorkflow(workflowCode));
        } catch (Exception e) {
            log.error("获取工作流失败 {}", e.getMessage());
            return ResponseEntity.internalServerError().body(Map.of("msg", "获取工作流失败"));
        }
    }

    /**
     * @description 流程右侧分页列表
     * <AUTHOR>
     */
    @GetMapping
    public ResponseEntity<Object> flowList(
            @RequestParam(value = "wf_code") String wfCode,
            @RequestParam(value = "wf_sname", required = false) String wfSname,
            @RequestParam(value = "state", required = false) String state
    ) {
        startPage();
        List<Map<String, Object>> dataList = workflowService.flowList(wfCode, wfSname, state);
        if (dataList.isEmpty()) {
            return ResponseEntity.ok().body(List.of());
        }
        dacService.filterAccessibleData(SecurityUtils.getUserId().toString(), dataList, WorkflowService.NODE_EXT_WF);
        return ResponseEntity.ok(getDataTable(dataList));
    }

    /**
     * @description 创建流程
     * <AUTHOR>
     */
    @PostMapping
    public ResponseEntity<Object> add(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(workflowService.add(params));
    }

    /**
     * @description 发布流程
     * <AUTHOR>
     */
    @PostMapping("/publish")
    public ResponseEntity<Object> publish(@RequestBody Map<String, Object> params) throws Exception {
        return ResponseEntity.ok(workflowService.publish(params));
    }


    /**
     * @description 编辑流程/重命名流程
     * <AUTHOR>
     */
    @PutMapping("/{dataCode}")
    public ResponseEntity<Object> rename(
            @PathVariable String dataCode,
            @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(workflowService.rename(dataCode, params));
    }

    /**
     * @description 启用/停用流程
     * <AUTHOR>
     */
    @PatchMapping
    public ResponseEntity<Object> deactivate(
            @RequestParam List<String> wfCodes,
            @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(workflowService.deactivate(wfCodes, params));
    }

    /**
     * 将工作流转换为表单
     *
     * @return 操作结果
     */
    @PostMapping("/formatTrans")
    public ResponseEntity<Object> formatTrans(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(workflowService.formatTrans(params));
    }

    /**
     * @description 流程详情
     * <AUTHOR>
     */
    @GetMapping("/detail/{wfCode}")
    public ResponseEntity<Object> flowInfo(@PathVariable String wfCode) {
        return ResponseEntity.ok(workflowService.flowInfo(wfCode));
    }

    /**
     * 获取流程详细信息（包括表单数据、流程数据、状态数据等）
     */
    @GetMapping("/info/{dataCode}")
    public ResponseEntity<Object> dataInfo(@PathVariable String dataCode) {
        return ResponseEntity.ok(workflowService.info(dataCode));
    }

    /**
     * @description 复制流程
     * <AUTHOR>
     */
    @PostMapping("/copy")
    public ResponseEntity<Object> copy(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(workflowService.copy(params));
    }
}
