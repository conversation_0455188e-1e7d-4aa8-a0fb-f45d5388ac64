package com.ruoyi.core.controller;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.service.ActionService;
import com.ruoyi.core.service.DataAccessControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/action", "/public-api/action"})
public class ActionController {
    private final ActionService actionService;
    private final DataAccessControlService dacService;

    public ActionController(ActionService actionService, DataAccessControlService dacService) {
        this.actionService = actionService;
        this.dacService = dacService;
    }

    @GetMapping("/{actionCode}/config/log")
    public ResponseEntity<Object> getActionConfigLog(
            @PathVariable String actionCode,
            @RequestParam(value = "pageNum", defaultValue = "1") int page,
            @RequestParam(value = "pageSize", defaultValue = "20") int take
    ) {
        try {
            return ResponseEntity.ok(this.actionService.getActionConfigLogList(actionCode, take, (long) (page - 1) * take));
        } catch (Exception e) {
            log.error("获取配置日志失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "获取配置日志失败"));
        }
    }

    @DeleteMapping("/{actionCode}/config/{configCode}")
    public ResponseEntity<Object> removeActionConfig(
            @PathVariable String actionCode,
            @PathVariable String configCode
    ) {
        try {
            this.actionService.removeActionConfig(actionCode, configCode);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("删除配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "删除配置失败"));
        }
    }

    @PostMapping("/{actionCode}/config/{configCode}/copy")
    public ResponseEntity<Object> copyActionConfig(
            @PathVariable String actionCode,
            @PathVariable String configCode
    ) {
        try {
            this.actionService.copyActionConfig(configCode);
            return ResponseEntity.status(HttpStatus.CREATED).body(Map.of("msg", ""));
        } catch (Exception e) {
            log.error("复制配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "复制配置失败"));
        }
    }

    @PutMapping("/{actionCode}/config/{configCode}")
    public ResponseEntity<Object> updateActionConfig(
            @PathVariable String actionCode,
            @PathVariable String configCode,
            @RequestBody Map<String, Object> data
    ) {
        data.put("auto_no", configCode);
        try {
            this.actionService.updateActionConfig(data);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("更新配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "更新配置失败"));
        }
    }

    @GetMapping("/{actionCode}/config/{configCode}")
    public ResponseEntity<Object> getActionConfigByCode(
            @PathVariable String actionCode,
            @PathVariable String configCode
    ) {
        try {
            return ResponseEntity.ok(this.actionService.getActionConfig(actionCode, configCode));
        } catch (Exception e) {
            log.error("获取配置详情失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "获取配置失败"));
        }
    }

    @GetMapping("/{actionCode}/config")
    public ResponseEntity<Object> getActionConfig(
            @PathVariable String actionCode
    ) {
        try {
            List<Map<String, Object>> actionList = this.actionService.getActionConfigList(actionCode);
            dacService.filterAccessibleData(SecurityUtils.getUserId().toString(), actionList, ActionService.TABLE_ACTION_CONFIG);
            return ResponseEntity.ok(actionList);
        } catch (Exception e) {
            log.error("获取配置列表失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "获取配置失败"));
        }
    }

    @PostMapping("/{actionCode}/config")
    public ResponseEntity<Object> createActionConfig(
            @PathVariable String actionCode,
            @RequestBody Map<String, Object> data
    ) {
        data.put("source_code", actionCode);
        try {
            this.actionService.createActionConfig(data);
            return ResponseEntity.status(HttpStatus.CREATED).body(Map.of("msg", ""));
        } catch (Exception e) {
            log.error("创建配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "创建配置失败"));
        }
    }
}
