package com.ruoyi.core.controller;

import com.ruoyi.core.service.ClinicalDiagnosisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 临床诊断
 */
@Slf4j
@RestController
@RequestMapping({"/api/clinical-diagnosis"})
public class ClinicalDiagnosisController {
    private final ClinicalDiagnosisService clinicalDiagnosisService;

    public ClinicalDiagnosisController(ClinicalDiagnosisService clinicalDiagnosisService) {
        this.clinicalDiagnosisService = clinicalDiagnosisService;
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Object> remove(@PathVariable String id) {
        try {
            clinicalDiagnosisService.remove(id);
            return ResponseEntity.ok().body(Map.of("msg", "初步诊断删除成功"));
        } catch (Exception e) {
            log.error("Error deleting clinical diagnosis with id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("message", "删除失败"));
        }
    }

    @GetMapping("")
    public ResponseEntity<Object> getList(@RequestParam Map<String, String> parameter) {
        String option = parameter.getOrDefault("option", "");
        if ("by-zhuyh".equals(option)) {
            String zhuyh = parameter.getOrDefault("zhuyh", "");
            if (zhuyh.isBlank()) {
                return ResponseEntity.badRequest().body(Map.of("message", "住院号不能为空"));
            }
            String zhendlx = parameter.getOrDefault("zhendlx", "");
            List<Map<String, Object>> rows = this.clinicalDiagnosisService.getListByZhuyh(zhuyh, zhendlx);
            return ResponseEntity.ok().body(rows);
        }
        return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).body(Map.of("msg", "不支持的参数"));
    }

    @PostMapping("")
    public ResponseEntity<Object> create(@RequestBody Map<String, Object> data) {
        try {
            System.out.println(data);
            clinicalDiagnosisService.create(data);
            return ResponseEntity.ok().body(Map.of("msg", "初步诊断创建成功"));
        } catch (Exception e) {
            log.error("Error creating clinical diagnosis", e);
            return ResponseEntity.badRequest().body(Map.of("message", "参数错误或数据处理失败"));
        }
    }
}
