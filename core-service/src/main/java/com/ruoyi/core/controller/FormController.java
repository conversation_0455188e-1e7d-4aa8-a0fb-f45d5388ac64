package com.ruoyi.core.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.service.DataAccessControlService;
import com.ruoyi.core.service.FormService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/form")
public class FormController extends BaseController {
    private final Logger log = LoggerFactory.getLogger(FormController.class);
    private final FormService formService;
    private final DataAccessControlService dacControlService;

    public FormController(FormService formService, DataAccessControlService dacControlService) {
        this.formService = formService;
        this.dacControlService = dacControlService;
    }

    /**
     * description  表单右侧分页列表
     *
     * <AUTHOR>
     */
    @GetMapping
    public ResponseEntity<Object> formList(
            @RequestParam(value = "dataCode") String dataCode,
            @RequestParam(value = "dataSName", required = false) String dataSName,
            @RequestParam(value = "state", required = false) String state) {
        startPage();
        List<Map<String, Object>> formList = formService.formList(dataCode, dataSName, state);
        if (formList.isEmpty()) {
            return ResponseEntity.ok().body(List.of());
        }
        dacControlService.filterAccessibleData(SecurityUtils.getUserId().toString(), formList, FormService.NODE_EXT_DATA);
        return ResponseEntity.ok(getDataTable(formList));
    }

    @GetMapping("/{dataCode}/data-dict")
    public ResponseEntity<Object> getDataDictList(
            @PathVariable String dataCode
    ) {
        try {
            List<Map<String, Object>> dataDictList = formService.getDataDictList(dataCode);
            if (dataDictList.isEmpty()) {
                return ResponseEntity.ok().body(List.of());
            }
            dacControlService.filterAccessibleData(SecurityUtils.getUserId().toString(), dataDictList, FormService.TABLE_DATA_DICT);
            return ResponseEntity.ok(dataDictList);
        } catch (Exception e) {
            log.error("获取表单数据字典失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "服务器内部错误"));
        }
    }

    @GetMapping("/{dataCode}/detail")
    @SuppressWarnings("unchecked")
    public ResponseEntity<Object> getDetailList(
            @PathVariable String dataCode,
            @RequestParam(value = "orderBy", defaultValue = "", required = false) String orderBy,
            @RequestParam(value = "limit", defaultValue = "10", required = false) String limit,
            @RequestParam(value = "page", defaultValue = "1", required = false) String page
    ) {
        int take;
        int skip;
        try {
            int pageNum = Integer.parseInt(page);
            if (pageNum < 1) {
                pageNum = 1;
            }
            take = Integer.parseInt(limit);
            skip = (pageNum - 1) * take;
        } catch (Exception e) {
            log.error("分页参数错误 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("msg", "分页参数错误"));
        }

        try {
            Map<String, Object> result = formService.getDetailList(dataCode, orderBy, Integer.toString(take), Integer.toString(skip));
            dacControlService.filterAccessibleData(SecurityUtils.getUserId().toString(), (List<Map<String, Object>>) result.get("detail_list"), FormService.TABLE_PREFIX + dataCode);
            return ResponseEntity.ok(formService.getDetailList(dataCode, orderBy, Integer.toString(take), Integer.toString(skip)));
        } catch (Exception e) {
            log.error("获取表单详情失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "服务器内部错误"));
        }
    }

    /**
     * description  表单详情
     *
     * <AUTHOR>
     */
    @GetMapping("/{dataCode}")
    public ResponseEntity<Object> form(
            @PathVariable String dataCode
    ) {
        try {
            return ResponseEntity.ok(formService.formInfo(dataCode));
        } catch (Exception e) {
            log.error("获取表单数据失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "服务器内部错误"));
        }
    }

    @PostMapping("/{id}/data")
    @SuppressWarnings("unchecked")
    public ResponseEntity<Object> get(
            @PathVariable(value = "id") String id,
            @RequestBody Map<String, Object> body
    ) {
        try {
            String renderId = body.get("render_id").toString();
            int page = Integer.parseInt(body.get("page").toString());
            int limit = Integer.parseInt(body.get("limit").toString());
            int skip = (page - 1) * limit;
            String keyword = "";
            String keys_ = body.get("keys").toString();
            ObjectMapper objectMapper = new ObjectMapper();
            if (null != keys_ && !keys_.isEmpty()) {
                Map<String, Object> keysMap = objectMapper.readValue(keys_, Map.class);
                String firstKey = keysMap.keySet().iterator().next();
                Map<String, Object> nestedMap = (Map<String, Object>) keysMap.get(firstKey);
                keyword = nestedMap.get("value").toString();
            }
            Map<String, Object> result = formService.getForm(id, renderId, keyword, limit, skip);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取表单数据(data)失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "服务器内部错误"));
        }
    }

    /**
     * @description 创建表单
     * <AUTHOR>
     */
    @PostMapping
    public ResponseEntity<Object> add(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(formService.add(params));
    }

    /**
     * @description 发布表单
     * <AUTHOR>
     */
    @PostMapping("/publish")
    public ResponseEntity<Object> publish(@RequestBody Map<String, Object> params) throws Exception {
        return ResponseEntity.ok(formService.publish(params));
    }

    /**
     * @description 编辑表单/表单重命名
     * <AUTHOR>
     */
    @PutMapping("/{dataCode}")
    public ResponseEntity<Object> rename(
            @PathVariable String dataCode,
            @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(formService.rename(dataCode, params));
    }

    /**
     * @description 启用/停用表单
     * <AUTHOR>
     */
    @PatchMapping
    public ResponseEntity<Object> deactivate(
            @RequestParam List<String> dataCodes,
            @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(formService.deactivate(dataCodes, params));
    }

    /**
     * @description 暂存     * <AUTHOR>
     */
    @PutMapping("/staging/{dataCode}")
    public ResponseEntity<Object> staging(@PathVariable String dataCode, @RequestBody Map<String, Object> params) {
        params.put("data_code", dataCode);
        return ResponseEntity.ok(formService.staging(dataCode, params));
    }

    /**
     * @description 将表单转换为流程
     * <AUTHOR>
     */
    @PostMapping("/formatTrans")
    public ResponseEntity<Object> formatTrans(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(formService.formatTrans(params));
    }

    /**
     * @description 复制表单
     * <AUTHOR>
     */
    @PostMapping("/copy")
    public ResponseEntity<Object> copy(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(formService.copy(params));
    }

    /**
     * @description 获取表单/流程/视图的所有数据
     * <AUTHOR>
     */
    @GetMapping("/table")
    public ResponseEntity<Object> getTableList() {
        return ResponseEntity.ok(formService.getDataList());
    }

    /**
     * @description 表单设计内容配置增加修改
     * <AUTHOR>
     */
    @PostMapping("/designConfig")
    public ResponseEntity<Object> designConfig(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(formService.designConfig(params));
    }

    /**
     * description  表单设计信息
     *
     * <AUTHOR>
     */
    @GetMapping("/design/{id}")
    public ResponseEntity<Object> formDesign(
            @PathVariable String id
    ) {
        try {
            return ResponseEntity.ok(formService.designInfo(id));
        } catch (Exception e) {
            log.error("获取表单配置数据失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "服务器内部错误"));
        }
    }
}
