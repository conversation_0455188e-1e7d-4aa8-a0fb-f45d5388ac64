package com.ruoyi.core.controller;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.service.LogService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/log"})
public class LogController {
    private final LogService logService;

    public LogController(LogService logService) {
        this.logService = logService;
    }

    @GetMapping("/{date}")
    public ResponseEntity<Object> getBusinessLogs(
            @PathVariable("date") String date
    ) {
        try {
            return ResponseEntity.ok(logService.getBusinessLogs(date));
        } catch (Exception e) {
            log.error(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "获取日志失败"));
        }
    }

    @PostMapping
    public ResponseEntity<Object> saveLog(
            @RequestHeader(value = "Application-Name", required = false) String applicationName,
            @Valid @RequestBody Entity body
    ) {
        try {
            String bizName = "";
            if ("admin-system".equals(applicationName)) {
                bizName = "管理端";
            } else if ("user-system".equals(applicationName)) {
                bizName = "用户端";
            }
            logService.saveLogWithBizName(SecurityUtils.getUserId().toString(), bizName, body.getModule(), body.getOperation(), null, null);
            return ResponseEntity.status(HttpStatus.CREATED).build();
        } catch (Exception e) {
            log.error(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "记录日志失败"));
        }
    }

    @Data
    static public class Entity {
        @NotEmpty
        private String module;
        @NotEmpty
        private String operation;
    }
}
