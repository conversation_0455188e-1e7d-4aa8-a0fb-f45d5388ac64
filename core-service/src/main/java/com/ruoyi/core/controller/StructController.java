package com.ruoyi.core.controller;

import com.ruoyi.core.service.StructService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/struct")
public class StructController {

    private final StructService structService;

    public StructController(StructService structService) {
        this.structService = structService;
    }

    /**
     * @description 表单左侧树形菜单
     * <AUTHOR>
     */
    @GetMapping("/treeInfo")
    public ResponseEntity<Object> treeInfo(
            @RequestParam(value = "node_id") String nodeId,
            @RequestParam(value = "node_type") String nodeType,
            @RequestParam(value = "leaf") Integer leaf,
            @RequestParam(value = "bind") Integer bind) {
        return ResponseEntity.ok(structService.treeInfo(nodeId, nodeType, leaf, bind));
    }

    /**
     * @description 新增文件夹/目录/节点
     * <AUTHOR>
     */
    @PostMapping("/tree")
    public ResponseEntity<Object> add(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(structService.add(params));
    }

    /**
     * @description 节点详情
     * <AUTHOR>
     */
    @GetMapping("/tree/{nodeId}")
    public ResponseEntity<Object> nodeInfo(@PathVariable String nodeId) {
        return ResponseEntity.ok(structService.nodeInfo(nodeId));
    }

    /**
     * @description 编辑文件夹/目录/节点
     * <AUTHOR>
     */
    @PutMapping("/tree/{nodeId}")
    public ResponseEntity<Object> update(
            @PathVariable String nodeId,
            @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(structService.update(nodeId, params));
    }

    /**
     * @description 删除文件夹/目录/节点
     * <AUTHOR>
     */
    @DeleteMapping("/tree/{nodeId}")
    public ResponseEntity<Object> delete(@PathVariable String nodeId) {
        return structService.delete(nodeId);
    }

    /**
     * 移动树节点
     *
     * <AUTHOR>
     */
    @PatchMapping("/move")
    public ResponseEntity<Object> moveData(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(structService.moveData(params));
    }
}
