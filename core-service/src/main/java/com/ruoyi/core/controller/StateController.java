package com.ruoyi.core.controller;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.service.DataAccessControlService;
import com.ruoyi.core.service.StateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/state"})
public class StateController {
    private final StateService stateService;
    private final DataAccessControlService dacService;

    public StateController(StateService stateService, DataAccessControlService dacService) {
        this.stateService = stateService;
        this.dacService = dacService;
    }

    @DeleteMapping("/{stateCode}/config/{configCode}")
    public ResponseEntity<Object> removeConfig(
            @PathVariable String stateCode,
            @PathVariable String configCode
    ) {
        if (configCode.isEmpty()) {
            return ResponseEntity.badRequest()
                    .body(Map.of("msg", "配置编码不能为空"));
        }
        try {
            this.stateService.removeStateConfig(configCode);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        } catch (Exception e) {
            log.error("删除状态配置失败 {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }

    @PutMapping("/{stateCode}/config/{configCode}")
    public ResponseEntity<Object> updateConfig(
            @PathVariable String stateCode,
            @PathVariable String configCode,
            @RequestBody Map<String, Object> data
    ) {
        data.put("source_code", stateCode);
        data.put("auto_no", configCode);
        try {
            this.stateService.updateConfig(data);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        } catch (Exception e) {
            log.error("更新状态配置失败 {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }

    @GetMapping("/{stateCode}/config/{configCode}")
    public ResponseEntity<Object> getConfig(
            @PathVariable String stateCode,
            @PathVariable String configCode
    ) {
        try {
            return ResponseEntity.ok(this.stateService.getStateConfig(stateCode, configCode));
        } catch (Exception e) {
            log.error("获取状态配置失败 {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }

    @GetMapping("/{stateCode}/config")
    public ResponseEntity<Object> getConfigList(
            @PathVariable String stateCode
    ) {
        try {
            List<Map<String, Object>> configList = this.stateService.getStateConfigList(stateCode);
            if (configList.isEmpty()) {
                return ResponseEntity.ok().body(List.of());
            }
            dacService.filterAccessibleData(SecurityUtils.getUserId().toString(), configList, StateService.TABLE_CONFIG);
            return ResponseEntity.ok().body(configList);
        } catch (Exception e) {
            log.error("获取状态配置列表失败 {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }

    @PostMapping("/{stateCode}/config")
    public ResponseEntity<Object> createConfig(
            @PathVariable String stateCode,
            @RequestBody Map<String, Object> data
    ) {
        data.put("source_code", stateCode);
        try {
            this.stateService.createStateConfig(data);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        } catch (Exception e) {
            log.error("创建状态配置失败 {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }
}
