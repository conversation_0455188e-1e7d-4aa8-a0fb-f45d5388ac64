package com.ruoyi.core.controller;

import com.ruoyi.core.service.DataAccessControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/data-access-control", "/public-api/data-access-control"})
public class DataAccessController {
    private final DataAccessControlService dataAccessControlService;

    public DataAccessController(DataAccessControlService dataAccessControlService) {
        this.dataAccessControlService = dataAccessControlService;
    }

    @GetMapping("/table")
    public ResponseEntity<Object> getTableList() {
        try {
            List<Map<String, Object>> result = dataAccessControlService.getTableList();
            return ResponseEntity.ok().body(result);
        } catch (Exception e) {
            log.error("Error getting table list {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "获取数据库表信息错误"));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Object> remove(
            @PathVariable String id
    ) {
        try {
            dataAccessControlService.remove(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error deleting data access control", e);
            return ResponseEntity.status(500).body(Map.of("msg", "Error deleting data: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Object> update(
            @PathVariable String id,
            @RequestBody Map<String, Object> body
    ) {
        try {
            body.put("id", id);
            dataAccessControlService.update(body);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error updating data access control", e);
            return ResponseEntity.status(500).body(Map.of("msg", "Error updating data: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Object> get(
            @PathVariable String id
    ) {
        try {
            Map<String, Object> data = dataAccessControlService.get(id);
            if (data.isEmpty()) {
                return ResponseEntity.ok().body("{}");
            }
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            log.error("Error fetching data access control", e);
            return ResponseEntity.status(500).body(Map.of("msg", "Error fetching data: " + e.getMessage()));
        }
    }

    @SuppressWarnings("unchecked")
    @GetMapping
    public ResponseEntity<Object> getList(
            @RequestParam(defaultValue = "") String option,
            @RequestParam(defaultValue = "") String type,
            @RequestParam(value = "relation_id", defaultValue = "") String relationID,
            @RequestParam(value = "reference_table", defaultValue = "") String referenceTable,
            @RequestParam(value = "reference_column", defaultValue = "") String referenceColumn,
            @RequestParam(defaultValue = "10") String pageSize,
            @RequestParam(defaultValue = "1") String pageNum
    ) {
        try {
            Map<String, Object> data = dataAccessControlService.getList(
                    type,
                    relationID,
                    referenceTable,
                    referenceColumn,
                    pageSize,
                    pageNum
            );
            for (Map<String, Object> row : (List<Map<String, Object>>) data.get("rows")) {
                row.put("id", row.get("id").toString());
            }
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            log.error("Error fetching data access control list", e);
            return ResponseEntity.status(500).body(Map.of("msg", "Error fetching data: " + e.getMessage()));
        }
    }

    @PostMapping()
    public ResponseEntity<Object> create(
            @RequestBody Map<String, Object> body
    ) {
        try {
            dataAccessControlService.create(body);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error creating data access control", e);
            return ResponseEntity.status(500).body(Map.of("msg", "Error creating data: " + e.getMessage()));
        }
    }
}
