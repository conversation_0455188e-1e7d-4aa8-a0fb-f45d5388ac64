package com.ruoyi.core.controller;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.service.DataAccessControlService;
import com.ruoyi.core.service.PrintService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/print"})
public class PrintController {
    private final PrintService printService;
    private final DataAccessControlService dacService;

    public PrintController(PrintService printService, DataAccessControlService dacService) {
        this.printService = printService;
        this.dacService = dacService;
    }

    @DeleteMapping("/{printCode}/config/{configCode}")
    public ResponseEntity<Object> removePrintConfig(
            @PathVariable String printCode,
            @PathVariable String configCode,
            @RequestBody Map<String, Object> data
    ) {
        if (configCode.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("msg", "配置编码不能为空"));
        }
        try {
            this.printService.removePrintConfig(configCode);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        } catch (Exception e) {
            log.error("删除打印配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
        }
    }

    @PostMapping("/{printCode}/config/{configCode}/copy")
    public ResponseEntity<Object> copyPrintConfig(
            @PathVariable String printCode,
            @PathVariable String configCode
    ) {
        if (configCode.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("msg", "配置编码不能为空"));
        }
        try {
            this.printService.copyPrintConfig(configCode);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        } catch (Exception e) {
            log.error("复制打印配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
        }
    }

    @PutMapping("/{printCode}/config/{configCode}")
    public ResponseEntity<Object> updatePrintConfig(
            @PathVariable String printCode,
            @PathVariable String configCode,
            @RequestBody Map<String, Object> data
    ) {
        data.put("auto_no", configCode);
        try {
            this.printService.updatePrintConfig(data);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        } catch (Exception e) {
            log.error("更新打印配置失败 {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }

    @GetMapping("/{code}/config/{configCode}")
    public ResponseEntity<Object> getPrintConfig(
            @PathVariable String code,
            @PathVariable String configCode
    ) {
        if (code.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("msg", "编码不能为空"));
        }
        if (configCode.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("msg", "配置编码不能为空"));
        }
        try {
            return ResponseEntity.ok().body(this.printService.getPrintConfig(code, configCode));
        } catch (Exception e) {
            log.error("获取打印配置失败 {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }

    @GetMapping("/{printCode}/config")
    public ResponseEntity<Object> getPrintConfigList(
            @PathVariable String printCode
    ) {
        if (printCode.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("msg", "打印编码不能为空"));
        }
        try {
            List<Map<String, Object>> configList = this.printService.getPrintConfigList(printCode);
            if (configList.isEmpty()) {
                return ResponseEntity.ok().body(List.of());
            }
            dacService.filterAccessibleData(SecurityUtils.getUserId().toString(), configList, PrintService.TABLE_CONFIG);
            return ResponseEntity.ok().body(configList);
        } catch (Exception e) {
            log.error("获取打印配置列表失败 {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }

    @PostMapping("/{printCode}/config")
    public ResponseEntity<Object> createPrintConfig(
            @PathVariable String printCode,
            @RequestBody Map<String, Object> data
    ) {
        try {
            data.put("source_code", printCode);
            this.printService.createPrintConfig(data);
            return ResponseEntity.status(HttpStatus.CREATED).body(Map.of("msg", "ok"));
        } catch (Exception e) {
            log.error("创建打印配置失败 {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "服务器错误"));
        }
    }
}
