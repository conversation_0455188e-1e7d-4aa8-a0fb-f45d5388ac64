package com.ruoyi.core.controller;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/public-api/test")
public class TestBizController {
    private final GenericMapper genericMapper;

    public TestBizController(GenericMapper genericMapper) {
        this.genericMapper = genericMapper;
    }

    @PostMapping("/password")
    public ResponseEntity<Object> generatePassword(
            @RequestBody Map<String, String> body
    ) {
        String pwd = body.get("password");
        String bcryptPassword = SecurityUtils.encryptPassword(pwd);
        boolean isPasswordMatch = SecurityUtils.matchesPassword(pwd, bcryptPassword);
        return ResponseEntity.ok().body(Map.of(
                "pwd", pwd,
                "bcryptPassword", bcryptPassword,
                "match", isPasswordMatch
        ));
    }

    @GetMapping("/password")
    public ResponseEntity<Object> password() {
        List<Map<String, Object>> userList = this.genericMapper.getList(
                "sys_user",
                "*",
                List.<String[]>of(
                        new String[]{"equal", "user_id", "100001"}
                ),
                "limit 1"
        );
        if (userList.isEmpty()) {
            return ResponseEntity.ok(Map.of("msg", "用户不存在"));
        }
        Map<String, Object> user = userList.get(0);
        String pwd = "ecd46538a0f71face9ae2df3a49c177f";
        String bcryptPassword = SecurityUtils.encryptPassword(pwd);
        boolean isPasswordMatch = SecurityUtils.matchesPassword(pwd, bcryptPassword);
        return ResponseEntity.ok().body(Map.of(
                "user", user,
                "bcryptPassword", bcryptPassword,
                "match", isPasswordMatch
        ));
    }
}
