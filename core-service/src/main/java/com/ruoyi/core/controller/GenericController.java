package com.ruoyi.core.controller;

import com.ruoyi.core.service.GenericService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/generic", "/public-api/generic"})
public class GenericController {
    private final GenericService genericService;

    public GenericController(GenericService genericService) {
        this.genericService = genericService;
    }

    /**
     * 创建表
     * 根据json对象数组创建数据表 name/列名 type/类型 len/长度
     *
     * @param name 表名
     * @param data 字段定义List，每个Map包含name/type/len
     *             例如：
     *             [
     *             {"name": "id", "type": "int", "len": 11},
     *             {"name": "name", "type": "varchar", "len": 255},
     *             {"name": "created_at", "type": "datetime"}
     *             ]
     */
    @PostMapping("/table")
    public ResponseEntity<Object> createTable(
            @RequestParam String name,
            @RequestBody List<Map<String, Object>> data
    ) {
        try {
            this.genericService.createTable(name, data);
            return ResponseEntity.ok().body(Map.of("msg", "OK"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of("message", "参数错误"));
        }
    }

    /**
     * 创建数据
     * 接收一个表名和一个包含数据的Map对象，创建一条新记录
     * 数据格式：
     * { "data": { "column1": "value1", "column2": "value2" }, "enc": { "column1": "encrypted_value1", "column2": "encrypted_value2" }
     *
     * @param table 表名
     * @param body  包含数据的Map对象
     * @return ResponseEntity
     */
    @SuppressWarnings("unchecked")
    @PostMapping("/{table}")
    public ResponseEntity<Object> create(@PathVariable String table, @RequestBody Map<String, Object> body) {
        try {
            Map<String, Object> data = (Map<String, Object>) body.get("data");
            System.out.println("Received data: " + data);
            Map<String, Object> enc = (Map<String, Object>) body.get("enc");
            this.genericService.create(table, data, enc);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        } catch (Exception e) {
            log.error("创建数据失败: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(Map.of("msg", "服务器错误"));
        }
    }

    /**
     * 删除数据
     * 根据表名和ID删除数据
     *
     * @param table 表名
     * @param id    数据ID
     * @return ResponseEntity
     */
    @DeleteMapping("/{table}/{id}")
    public ResponseEntity<Object> remove(
            @PathVariable String table,
            @PathVariable String id
    ) {
        try {
            this.genericService.removeByLeafID(table, id);
            return ResponseEntity.ok().body(Map.of("msg", "删除成功"));
        } catch (Exception e) {
            log.error("删除数据失败: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(Map.of("msg", "服务器错误"));
        }
    }

    /**
     * 更新数据
     * 根据表名和ID更新数据
     *
     * @param table   表名
     * @param leaf_id 数据ID
     * @param body    包含更新数据的Map对象
     * @return ResponseEntity
     */
    @SuppressWarnings("unchecked")
    @PutMapping("/{table}/{leaf_id}")
    public ResponseEntity<Object> update(
            @PathVariable String table,
            @PathVariable String leaf_id,
            @RequestBody Map<String, Object> body
    ) {
        try {
            Map<String, Object> data = (Map<String, Object>) body.get("data");
            this.genericService.update(table, leaf_id, data);
            return ResponseEntity.ok().body(Map.of("msg", "更新成功"));
        } catch (Exception e) {
            log.error("更新数据失败: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(Map.of("msg", "服务器错误"));
        }
    }

    /**
     * 获取数据列表
     * 根据表名、列名和条件获取数据列表
     * 条件格式为逗号分隔的字符串，每个条件用",_,"分隔，例如：
     * "column1,value1,column2,value2" 表示 column1 = value1 AND column2 = value2
     * 如果需要使用 OR 条件，可以在条件中使用逗号分隔，例如：
     * "column1,value1,_OR_,column2,value2" 表示 (column1 = value1 OR column2 = value2)
     * 最后一个参数 "l" 用于分页，默认为 "limit 10"，
     * 可以指定其他分页参数，例如 "limit 20 offset 10" 表示
     * 获取第 11 到 30 条记录。
     *
     * @param table     表名
     * @param column    列名
     * @param condition 条件字符串
     * @param last      最后的限制字符串，默认为 "limit 10"
     * @return ResponseEntity<Object> 包含数据列表或错误信息
     */
    @GetMapping
    public ResponseEntity<Object> getList(
            @RequestParam(value = "t") String table,
            @RequestParam(value = "c", required = false, defaultValue = "*") String column,
            @RequestParam(value = "f", required = false, defaultValue = "") String condition,
            @RequestParam(value = "l", required = false, defaultValue = "limit 10") String last
    ) {
        table = "`" + table + "`";
        String[] c = new String[]{};
        if (!condition.isBlank()) {
            c = Arrays.stream(condition.split(",_,"))
                    .filter(s -> !s.trim().isEmpty())
                    .toArray(String[]::new);
        }
        List<String[]> conditionList = Arrays.stream(c)
                .map(s -> s.split(","))
                .toList();
        try {
            return ResponseEntity.ok(genericService.getList(table, column, conditionList, last));
        } catch (Exception e) {
            log.error("获取数据列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("message", "参数错误"));
        }
    }
}
