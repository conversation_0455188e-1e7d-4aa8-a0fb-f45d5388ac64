package com.ruoyi.core.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.service.DataAccessControlService;
import com.ruoyi.core.service.ViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/view"})
public class ViewController extends BaseController {
    private final ViewService viewService;
    private final DataAccessControlService dacService;

    public ViewController(ViewService viewService, DataAccessControlService dacService) {
        this.viewService = viewService;
        this.dacService = dacService;
    }

    @DeleteMapping("/{viewCode}/config/{configCode}")
    public ResponseEntity<Object> deleteConfig(
            @PathVariable String viewCode,
            @PathVariable String configCode
    ) {
        if (null == configCode || configCode.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("msg", "参数错误"));
        }
        try {
            this.viewService.removeViewConfig(configCode);
            return ResponseEntity.ok().body(Map.of("msg", ""));
        } catch (Exception e) {
            log.error("删除视图失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "删除失败"));
        }
    }

    @PostMapping("/{viewCode}/config/{configCode}/copy")
    public ResponseEntity<Object> copyConfig(
            @PathVariable String viewCode,
            @PathVariable String configCode
    ) {
        if (null == configCode || configCode.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("msg", "参数错误"));
        }
        try {
            this.viewService.copyViewConfig(configCode);
            return ResponseEntity.ok().body(Map.of("msg", ""));
        } catch (Exception e) {
            log.error("复制视图失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "复制失败"));
        }
    }

    /**
     * todo 记录log
     *
     * @param configCode 配置code
     * @param body       数据
     * @return ResponseEntity
     */
    @PutMapping("/{viewCode}/config/{configCode}")
    public ResponseEntity<Object> updateConfig(
            @PathVariable String viewCode,
            @PathVariable String configCode,
            @RequestBody Map<String, Object> body
    ) {
        body.put("auto_no", configCode);
        try {
            viewService.updateViewConfig(body);
            return ResponseEntity.ok().body(Map.of("msg", ""));
        } catch (Exception e) {
            log.error("更新视图失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "更新失败"));
        }
    }

    @GetMapping("/{viewCode}/config/{configCode}")
    public ResponseEntity<Object> getConfig(
            @PathVariable String viewCode,
            @PathVariable String configCode
    ) {
        try {
            Map<String, Object> config = viewService.getViewConfig(viewCode, configCode);
            return ResponseEntity.ok().body(config);
        } catch (Exception e) {
            log.error("查询视图详情失败 {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("msg", "查询失败"));
        }
    }

    @GetMapping("/{viewCode}/config")
    public ResponseEntity<Object> getConfigList(
            @PathVariable String viewCode
    ) {
        try {
            List<Map<String, Object>> list = viewService.getViewConfigListByView(viewCode);
            if (list.isEmpty()) {
                return ResponseEntity.ok().body(List.of());
            }
            dacService.filterAccessibleData(SecurityUtils.getUserId().toString(), list, ViewService.TABLE_CONFIG);
            return ResponseEntity.ok().body(list);
        } catch (Exception e) {
            log.error("查询视图失败 {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("msg", "查询失败"));
        }
    }

    @PostMapping("/{viewCode}/config")
    public ResponseEntity<Object> createConfig(
            @PathVariable String viewCode,
            @RequestBody Map<String, Object> body
    ) {
        body.put("source_code", viewCode);
        try {
            viewService.createConfig(body);
            return ResponseEntity.ok().body(Map.of("msg", "创建成功"));
        } catch (Exception e) {
            log.error("创建视图失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "创建失败"));
        }
    }

    /**
     * @description 右侧分页列表
     * <AUTHOR>
     */
    @GetMapping
    public ResponseEntity<Object> viewList(
            @RequestParam(value = "viewCode") String viewCode,
            @RequestParam(value = "viewSName", required = false) String viewSName,
            @RequestParam(value = "state", required = false) String state
    ) {
        startPage();
        return ResponseEntity.ok().body(getDataTable(viewService.viewList(viewCode, viewSName, state)));
    }

    /**
     * @description 创建视图
     * <AUTHOR>
     */
    @PostMapping
    public ResponseEntity<Object> add(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(viewService.addView(params));
    }

    /**
     * @description 发布视图
     * <AUTHOR>
     */
    @PostMapping("/publish")
    public ResponseEntity<Object> publish(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(viewService.publish(params));
    }

    /**
     * @description 编辑视图
     * <AUTHOR>
     */
    @PutMapping("/{viewCode}")
    public ResponseEntity<Object> update(@PathVariable String viewCode,
                                         @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(viewService.update(viewCode, params));
    }

    /**
     * @description 启用/停用视图
     * <AUTHOR>
     */
    @PatchMapping
    public ResponseEntity<Object> deactivate(
            @RequestParam List<String> viewCodes,
            @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(viewService.deactivate(viewCodes, params));
    }

    /**
     * @description 视图详情
     * <AUTHOR>
     */
    @GetMapping("/{viewCode}")
    public ResponseEntity<Object> viewInfo(@PathVariable String viewCode) {
        return ResponseEntity.ok(viewService.getView(viewCode));
    }

    /**
     * @description 复制视图
     * <AUTHOR>
     */
    @PostMapping("/copy")
    public ResponseEntity<Object> copyView(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(viewService.copyView(params));
    }
}
