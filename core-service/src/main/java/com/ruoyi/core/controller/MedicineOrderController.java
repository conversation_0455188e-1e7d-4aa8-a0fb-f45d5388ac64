package com.ruoyi.core.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.core.service.MedicineOrderService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/medicine")
public class MedicineOrderController {

    private final MedicineOrderService medicineOrderService;

    public MedicineOrderController(MedicineOrderService medicineOrderService) {
        this.medicineOrderService = medicineOrderService;
    }

    /**
     * @description 获取药品库存列表
     * <AUTHOR>
     */
    @GetMapping("/inventory")
    public ResponseEntity<Object> inventory(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "5") Integer pageSize,
            @RequestParam(required = false) String query
    ){
        return ResponseEntity.ok(medicineOrderService.inventory(pageNum,pageSize,query));
    }

    /**
     * @description 新增用药医嘱
     * <AUTHOR>
     */
    @PostMapping
    public ResponseEntity<Object> add(@RequestBody Map<String,Object> params){
        return ResponseEntity.ok(medicineOrderService.add(params));
    }

    /**
     * @description 已保存的用药医嘱列表
     * <AUTHOR>
     */
    @GetMapping
    public ResponseEntity<Object> list(@RequestParam String patientId){
        return ResponseEntity.ok(medicineOrderService.list(patientId));
    }

    /**
     * @description 用药医嘱详情
     * <AUTHOR>
     */
    @GetMapping("/{id}/{patientId}")
    public ResponseEntity<Object> info(@PathVariable Long id, @PathVariable String patientId){
        return ResponseEntity.ok(medicineOrderService.info(id,patientId));
    }

    /**
     * @description 删除用药医嘱
     * <AUTHOR>
     */
    @DeleteMapping("/{id}")
    public  ResponseEntity<Object> delete(@PathVariable Long id){
        return ResponseEntity.ok(medicineOrderService.delete(id));
    }

    /**
     * @description 开方
     * <AUTHOR>
     */
    @PatchMapping
    public ResponseEntity<Object> prescribe(@RequestParam List<Long> ids){
        return ResponseEntity.ok(medicineOrderService.prescribe(ids));
    }

    /**
     * @description 取消开方
     * <AUTHOR>
     */
    @PatchMapping("/cancelPrescription")
    public ResponseEntity<Object> cancelPrescription(@RequestParam List<Long> ids){
        return ResponseEntity.ok(medicineOrderService.cancelPrescription(ids));
    }

    /**
     * @description 修改用药医嘱
     * <AUTHOR>
     */
    @PutMapping("/{id}")
    public ResponseEntity<Object> update(@PathVariable Long id, @RequestBody Map<String,Object> params){
        return ResponseEntity.ok(medicineOrderService.update(id,params));
    }

    /**
     * @description 获取处方信息
     * <AUTHOR>
     */
    @GetMapping("/prescription/{patientId}")
    public ResponseEntity<Object> getPrescriptionInfo(@PathVariable String patientId) {
        return ResponseEntity.ok(medicineOrderService.getPrescriptionInfo(patientId));
    }

    /**
     * @description 获取最近配药信息列表
     * <AUTHOR>
     */
    @GetMapping("/recent-medication/{patientId}")
    public ResponseEntity<Object> getRecentMedicationHistory(
            @PathVariable String patientId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "5") Integer pageSize,
            @RequestParam(required = false) String query
    ) {
        return ResponseEntity.ok(medicineOrderService.getRecentMedicationHistory(patientId, pageNum, pageSize, query));
    }

    /**
     * @description 获取上次使用情况
     * <AUTHOR>
     */
    @GetMapping("/getLastUsageInfo")
    public AjaxResult getLastUsageInfo(@RequestParam String patientId, @RequestParam String drugStockId) {
        return medicineOrderService.getLastUsageInfo(patientId,drugStockId);
    }

    /**
     * @description 获取用药医嘱分页列表
     * <AUTHOR>
     */
    @GetMapping("/pageList")
    public ResponseEntity<Object> pageList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String quy,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String orderType,
            @RequestParam(required = false) String orderDate,
            @RequestParam(required = false) String administrationRoute) {
        return ResponseEntity.ok(medicineOrderService.pageList(pageNum, pageSize, quy, status, orderType, orderDate, administrationRoute));
    }

    /**
     * @description 批量核对用药医嘱
     * <AUTHOR>
     */
    @PatchMapping("/batchVerify")
    public ResponseEntity<Object> batchVerify(@RequestParam List<Long> ids) {
        return ResponseEntity.ok(medicineOrderService.batchVerify(ids));
    }

    /**
     * @description 批量生成用药医嘱
     * <AUTHOR>
     */
    @PatchMapping("/batchGenerate")
    public ResponseEntity<Object> batchGenerate(@RequestParam List<Long> ids) {
        return ResponseEntity.ok(medicineOrderService.batchGenerate(ids));
    }


    /**
     * @description 获取所有护理部的人员
     * <AUTHOR>
     */
    @GetMapping("/getNurse")
    public ResponseEntity<Object> getNurse(){
        return ResponseEntity.ok(medicineOrderService.getNurse());
    }

}
