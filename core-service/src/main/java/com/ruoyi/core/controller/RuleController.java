package com.ruoyi.core.controller;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.service.DataAccessControlService;
import com.ruoyi.core.service.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/rule"})
public class RuleController {
    private final RuleService ruleService;
    private final DataAccessControlService dacService;

    public RuleController(RuleService ruleService, DataAccessControlService dacService) {
        this.ruleService = ruleService;
        this.dacService = dacService;
    }

    @DeleteMapping("/{ruleCode}/config/{configCode}")
    public ResponseEntity<Object> removeRuleConfig(
            @PathVariable String ruleCode,
            @PathVariable String configCode
    ) {
        try {
            this.ruleService.removeRuleConfig(configCode);
            return ResponseEntity.ok().body(Map.of("msg", ""));
        } catch (Exception e) {
            log.error("删除规则配置失败 {}", e.getMessage());
            return ResponseEntity.status(500)
                    .body(Map.of("msg", "服务器错误"));
        }
    }

    @PostMapping("/{ruleCode}/config/{configCode}/copy")
    public ResponseEntity<Object> copyRuleConfig(
            @PathVariable String ruleCode,
            @PathVariable String configCode
    ) {
        if (configCode.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("msg", "配置编码不能为空"));
        }
        try {
            this.ruleService.copyRuleConfig(configCode);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        } catch (Exception e) {
            log.error("复制规则配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
        }
    }

    @PutMapping("/{ruleCode}/config/{configCode}")
    public ResponseEntity<Object> updateRuleConfig(
            @PathVariable String ruleCode,
            @PathVariable String configCode,
            @RequestBody Map<String, Object> data
    ) {
        data.put("auto_no", configCode);
        data.put("source_code", ruleCode);
        try {
            this.ruleService.updateRuleConfig(data);
            return ResponseEntity.ok().body(Map.of("msg", ""));
        } catch (Exception e) {
            log.error("更新规则配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "服务器错误"));
        }
    }

    @GetMapping("/{ruleCode}/config/{configCode}")
    public ResponseEntity<Object> getRuleConfig(
            @PathVariable String ruleCode,
            @PathVariable String configCode
    ) {
        try {
            Map<String, Object> ruleConfig = this.ruleService.getRuleConfig(ruleCode, configCode);
            return ResponseEntity.ok().body(ruleConfig);
        } catch (Exception e) {
            log.error("获取规则配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "服务器错误"));
        }
    }

    @GetMapping("/{ruleCode}/config")
    public ResponseEntity<Object> getRuleConfigList(
            @PathVariable String ruleCode
    ) {
        try {
            List<Map<String, Object>> ruleConfigList = this.ruleService.getRuleConfigList(ruleCode);
            if (ruleConfigList.isEmpty()) {
                return ResponseEntity.ok().body("{}");
            }
            dacService.filterAccessibleData(SecurityUtils.getUserId().toString(), ruleConfigList, RuleService.TABLE_CONFIG);
            return ResponseEntity.ok().body(ruleConfigList);
        } catch (Exception e) {
            log.error("获取规则配置列表失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "服务器错误"));
        }
    }

    @PostMapping("/{ruleCode}/config")
    public ResponseEntity<Object> createRuleConfig(
            @PathVariable String ruleCode,
            @RequestBody Map<String, Object> data
    ) {
        data.put("source_code", ruleCode);
        try {
            this.ruleService.createRuleConfig(data);
            return ResponseEntity.status(HttpStatus.CREATED).body(Map.of("msg", ""));
        } catch (Exception e) {
            log.error("创建规则配置失败 {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("msg", "服务器错误"));
        }
    }
}
