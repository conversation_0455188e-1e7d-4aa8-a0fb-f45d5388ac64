package com.ruoyi.core.controller;

import com.ruoyi.core.service.ScriptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/script")
public class ScriptController {
    private final ScriptService scriptService;

    public ScriptController(ScriptService scriptService) {
        this.scriptService = scriptService;
    }

    @GetMapping("/{driverID}")
    public ResponseEntity<Object> getScriptByDriver(
            @PathVariable String driverID
    ) {
        try {
            return ResponseEntity.ok().body(scriptService.getScriptByDriver(driverID));
        } catch (Exception e) {
            log.error("获取脚本失败 {}", e.getMessage());
            return ResponseEntity.ok().body(Map.of("msg", "获取脚本失败", "error", e.getMessage()));
        }
    }
}
