package com.ruoyi.core.controller;

import com.ruoyi.core.service.OutStopMedicineService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/out-stop-medicine")
public class OutStopMedicineController {

    private final OutStopMedicineService outStopMedicineService;

    /**
     * @description 批量新增外出停药记录
     * <AUTHOR>
     */
    @PostMapping("/batch")
    public ResponseEntity<Object> batchAdd(@RequestBody List<Map<String, Object>> paramsList) {
        return ResponseEntity.ok(outStopMedicineService.batchAdd(paramsList));
    }

    /**
     * @description 删除外出停药记录
     * <AUTHOR>
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Object> delete(@PathVariable String id) {
        return ResponseEntity.ok(outStopMedicineService.delete(id));
    }

    /**
     * @description 根据ID获取外出停药记录详情
     * <AUTHOR>
     */
    @GetMapping("/{id}")
    public ResponseEntity<Object> getDetailById(@PathVariable String id) {
        return ResponseEntity.ok(outStopMedicineService.getDetailById(id));
    }

    /**
     * @description 分页查询外出停药记录列表
     * <AUTHOR>
     */
    @GetMapping
    public ResponseEntity<Object> getPageList(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "patientName", required = false) String patientName,
            @RequestParam(value = "hospitalizationId", required = false) String hospitalizationId,
            @RequestParam(value = "bedNumber", required = false) String bedNumber
    ) {
        return ResponseEntity.ok(outStopMedicineService.getPageList(pageNum, pageSize, patientName, hospitalizationId, bedNumber));
    }

    /**
     * @description 根据住院号查询病人详情
     * <AUTHOR>
     */
    @GetMapping("/patient/{hospitalizationId}")
    public ResponseEntity<Object> getPatientDetailByHospitalizationId(@PathVariable String hospitalizationId) {
        return ResponseEntity.ok(outStopMedicineService.getPatientDetailByHospitalizationId(hospitalizationId));
    }

    /**
     * @description 根据住院号查询长期医嘱（用药医嘱和外配药医嘱）
     * <AUTHOR>
     */
    @GetMapping("/medication-orders/{hospitalizationId}")
    public ResponseEntity<Object> getMedicationOrdersByHospitalizationId(@PathVariable String hospitalizationId) {
        return ResponseEntity.ok(outStopMedicineService.getMedicationOrdersByHospitalizationId(hospitalizationId));
    }

    /**
     * @description 回院接口 - 根据主键id处理回院逻辑
     * <AUTHOR>
     */
    @PatchMapping("/return-hospital/{id}")
    public ResponseEntity<Object> returnHospital(@PathVariable String id) {
        return ResponseEntity.ok(outStopMedicineService.returnHospital(id));
    }
}
