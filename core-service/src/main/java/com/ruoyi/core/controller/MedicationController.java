package com.ruoyi.core.controller;

import com.ruoyi.core.service.MedicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 药卡
 */
@Slf4j
@RestController
@RequestMapping({"/api/medication"})
public class MedicationController {
    private final MedicationService medicationService;

    public MedicationController(MedicationService medicationService) {
        this.medicationService = medicationService;
    }

    /**
     * 根据住院号 查询医嘱
     *
     * @param hid 住院号
     * @return 医嘱信息
     */
    @GetMapping("/hid/{hid}")
    public ResponseEntity<Object> get(@PathVariable String hid) {
        try {
            Map<String, Object> order = medicationService.getByHID(hid);
            return ResponseEntity.ok().body(order);
        } catch (Exception e) {
            log.error("Error fetching medical order with id: {}", hid, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
        }
    }

    @GetMapping()
    public ResponseEntity<Object> getMedicationList(
            @RequestParam(defaultValue = "") String sn,
            @RequestParam(defaultValue = "") String name,
            @RequestParam(defaultValue = "") String area,
            @RequestParam(defaultValue = "") String bed,
            @RequestParam(defaultValue = "0") String skip,
            @RequestParam(defaultValue = "10") String take
    ) {
        try {
            return ResponseEntity.ok().body(medicationService.getList(sn, name, area, bed, skip, take));
        } catch (Exception e) {
            log.error("Error fetching medication list", e);
            return ResponseEntity.status(500).body("服务器错误");
        }
    }

    @GetMapping("/areaList")
    public ResponseEntity<Object> areaList(){
        return ResponseEntity.ok(medicationService.areaList());
    }

    @GetMapping("/patientList")
    public ResponseEntity<Object> patientList(@RequestParam(required = false) String query){
        return ResponseEntity.ok(medicationService.patientList(query));
    }

    @GetMapping("/getPatientInfo")
    public ResponseEntity<Object> getPatientInfo(@RequestParam String id){
        return ResponseEntity.ok(medicationService.getPatientInfo(id));
    }

    /**
     * @description 生成药卡
     * <AUTHOR>
     */
    @GetMapping("/generate")
    public ResponseEntity<Object> generate(String queryDate){
        return ResponseEntity.ok(medicationService.generate(queryDate));
    }
}
