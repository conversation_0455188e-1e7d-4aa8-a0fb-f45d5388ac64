package com.ruoyi.core.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.service.DataAccessControlService;
import com.ruoyi.core.service.PatientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/patient")
public class PatientController extends BaseController {
    private final PatientService patientService;
    private final DataAccessControlService dacService;

    public PatientController(PatientService patientService, DataAccessControlService dacService) {
        this.patientService = patientService;
        this.dacService = dacService;
    }

    /**
     * @description 门诊病历生成
     * <AUTHOR>
     */
    @PostMapping
    public ResponseEntity<Object> add(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(patientService.add(params));
    }

    /**
     * @description 获取所有部门为“卫生所”的人员，作为带教医生/医生的选项
     * <AUTHOR>
     */
    @GetMapping("/supervisors")
    public ResponseEntity<Object> getSupervisors() {
        return ResponseEntity.ok(patientService.getSupervisors());
    }

    /**
     * @description 分页列表
     * <AUTHOR>
     */
    @GetMapping
    public ResponseEntity<Object> getPatientList(
            @RequestParam(value = "zhuyuanh", required = false) String zhuyuanh,
            @RequestParam(value = "xingming", required = false) String xingming,
            @RequestParam(value = "chuangwNo", required = false) String chuangwNo
    ) {
        startPage();
        List<Map<String, Object>> patientList = patientService.getPatientList(zhuyuanh, xingming, chuangwNo);
        if (patientList.isEmpty()) {
            return ResponseEntity.ok(getDataTable(List.of()));
        }
        dacService.filterAccessibleData(SecurityUtils.getUserId().toString(), patientList, PatientService.TABLE_MENZBL);
        return ResponseEntity.ok(getDataTable(patientService.getPatientList(zhuyuanh, xingming, chuangwNo)));
    }

    /**
     * @description 编辑门诊病历
     * <AUTHOR>
     */
    @PutMapping("/{id}")
    public ResponseEntity<Object> update(
            @PathVariable String id,
            @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(patientService.update(id, params));
    }

    /**
     * @description 门诊病历详情
     * <AUTHOR>
     */
    @GetMapping("/{id}")
    public ResponseEntity<Object> getPatientById(@PathVariable("id") String id) {
        return ResponseEntity.ok(patientService.getPatientById(id));
    }

    /**
     * @description 删除门诊病历
     * <AUTHOR>
     */
    @DeleteMapping
    public ResponseEntity<Object> deleteByIds(@RequestParam List<String> ids) {
        patientService.deleteByIds(ids);
        return ResponseEntity.ok("删除成功");
    }

    /**
     * @description 获取所有患者信息
     * <AUTHOR>
     */
    @GetMapping("/getPatientList")
    public ResponseEntity<Object> getPatientInfo(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String query
    ) {
        return ResponseEntity.ok(patientService.getPatientList(pageNum, pageSize, query));
    }

    /**
     * @description 根据老人id获取老人信息
     * <AUTHOR>
     */
    @GetMapping("/getPatientInfo/{id}")
    public ResponseEntity<Object> getPatientInfo(@PathVariable String id) {
        return ResponseEntity.ok(patientService.getPatientInfo(id));
    }

    /**
     * @description 根据姓名和住院号获取患者的化验结果
     * <AUTHOR>
     */
    @GetMapping("/getLabResults")
    @SuppressWarnings("unchecked")
    public ResponseEntity<Object> getLabResults(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestParam String xingming,
            @RequestParam String zhuyuanh,
            @RequestParam(defaultValue = "7") Integer dateRange,
            @RequestParam(required = false) String query
    ) {
        try {
            Map<String, Object> result = (Map<String, Object>) patientService.getLabResults(pageNum, pageSize, xingming, zhuyuanh, dateRange, query);
            dacService.filterAccessibleData(SecurityUtils.getUserId().toString(), (List<Map<String, Object>>) result.get("rows"), PatientService.TALBE_HUAYJGXX);
            return ResponseEntity.ok().body(result);
        } catch (Exception e) {
            log.error("获取化验结果失败: {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of("msg", "获取化验结果失败: " + e.getMessage()));
        }
    }

    /**
     * @description 拼接化验结果
     * <AUTHOR>
     */
    @GetMapping("/spliceLabResults")
    public ResponseEntity<Object> spliceLabResults(@RequestParam List<String> ids) {
        return ResponseEntity.ok(patientService.spliceLabResults(ids));
    }

    /**
     * @description 获取疾病诊断信息
     * <AUTHOR>
     */
    @GetMapping("/getClinicalDiagnosis")
    public ResponseEntity<Object> getClinicalDiagnosis() {
        return ResponseEntity.ok(patientService.getClinicalDiagnosis());
    }

    /**
     * @description 获取所有病历模板
     * <AUTHOR>
     */
    @GetMapping("/getPatientTemplate")
    public ResponseEntity<Object> getPatientTemplate(@RequestParam(defaultValue = "1") Integer pageNum,
                                                     @RequestParam(defaultValue = "10") Integer pageSize,
                                                     @RequestParam(required = false) String query) {
        return ResponseEntity.ok(patientService.getPatientTemplate(pageNum, pageSize, query));
    }

    /**
     * @description 根据模板ID获取病历模板
     * <AUTHOR>
     */
    @GetMapping("/getTemplateById/{id}")
    public ResponseEntity<Object> getTemplateById(@PathVariable String id) {
        return ResponseEntity.ok(patientService.getTemplateById(id));
    }

    /**
     * @description 生成病程录
     * <AUTHOR>
     */
    @PostMapping("/saveMedicalRecord")
    public ResponseEntity<Object> saveMedicalRecord(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(patientService.saveMedicalRecord(params));
    }

    /**
     * @description 获取病程录模板信息
     * <AUTHOR>
     */
    @GetMapping("/initMedicalRecord/{id}")
    public ResponseEntity<Object> initMedicalRecord(@PathVariable String id) {
        return ResponseEntity.ok(patientService.initMedicalRecord(id));
    }

    /**
     * @description 根据姓名和住院号获取病人历史病程录
     * <AUTHOR>
     */
    @GetMapping("/medicalRecordList")
    public ResponseEntity<Object> medicalRecordList(@RequestParam String zhuyuanh,
                                                    @RequestParam String xingming) {
        return ResponseEntity.ok(patientService.medicalRecordList(zhuyuanh, xingming));
    }

    /**
     * @description 生成就医记录
     * <AUTHOR>
     */
    @PostMapping("/saveDoctorRecord")
    public ResponseEntity<Object> saveDoctorRecord(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(patientService.saveDoctorRecord(params));
    }

    /**
     * @description 根据老人id获取血压数据
     * <AUTHOR>
     */
    @GetMapping("/bloodPressure/{id}")
    public ResponseEntity<Object> getBloodPressure(@PathVariable String id,
                                                   @RequestParam(defaultValue = "1") Integer pageNum,
                                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        return ResponseEntity.ok(patientService.getBloodPressure(id, pageNum, pageSize));
    }

    /**
     * @description 根据老人姓名和住院号获取血压数据
     * <AUTHOR>
     */
    @GetMapping("/temperature")
    public ResponseEntity<Object> temperature(@RequestParam String oldName,
                                              @RequestParam String zyno,
                                              @RequestParam(defaultValue = "1") Integer pageNum,
                                              @RequestParam(defaultValue = "10") Integer pageSize) {
        return ResponseEntity.ok(patientService.temperature(oldName, zyno, pageNum, pageSize));
    }

    /**
     * @description 根据门诊病历id获取老人详细信息
     * <AUTHOR>
     */
    @GetMapping("/getPatientInfoById/{id}")
    public ResponseEntity<Object> getPatientInfoById(@PathVariable String id){
        return ResponseEntity.ok(patientService.getPatientInfoById(id));
    }

    /**
     * @description 保存疾病诊断
     * <AUTHOR>
     */
    @PostMapping("/saveLinczd")
    public AjaxResult saveLinczd(@RequestBody Map<String,Object> params){
        return patientService.saveLinczd(params);
    }
}
