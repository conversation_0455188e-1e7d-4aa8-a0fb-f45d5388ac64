package com.ruoyi.core.controller;

import com.ruoyi.core.service.ClinicalOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/clinical")
public class ClinicalOrderController {

    private final ClinicalOrderService clinicalOrderService;

    /**
     * @description 获取诊疗信息列表
     * <AUTHOR>
     */
    @GetMapping("/clinicalInfo")
    public ResponseEntity<Object> clinicalInfo(@RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize,
                                       @RequestParam(required = false) String query) {
        return ResponseEntity.ok(clinicalOrderService.clinicalInfo(pageNum, pageSize, query));
    }

    /**
     * @description 新增诊疗医嘱
     * <AUTHOR>
     */
    @PostMapping
    public ResponseEntity<Object> add(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(clinicalOrderService.add(params));
    }

    /**
     * @description 修改诊疗医嘱
     * <AUTHOR>
     */
    @PutMapping("/{id}")
    public ResponseEntity<Object> update(@PathVariable Long id, @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(clinicalOrderService.update(id, params));
    }

    /**
     * @description 删除诊疗医嘱
     * <AUTHOR>
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Object> delete(@PathVariable Long id) {
        return ResponseEntity.ok(clinicalOrderService.delete(id));
    }

    /**
     * @description 查看诊疗医嘱详情
     * <AUTHOR>
     */
    @GetMapping("/{id}")
    public ResponseEntity<Object> info(@PathVariable Long id) {
        return ResponseEntity.ok(clinicalOrderService.info(id));
    }

    /**
     * @description 根据门诊病历ID获取诊疗医嘱列表
     * <AUTHOR>
     */
    @GetMapping("/list")
    public ResponseEntity<Object> list(@RequestParam String medicalRecordId) {
        return ResponseEntity.ok(clinicalOrderService.list(medicalRecordId));
    }

    /**
     * @description 获取诊疗处方信息
     * <AUTHOR>
     */
    @GetMapping("/prescription/{patientId}")
    public ResponseEntity<Object> getPrescriptionInfo(@PathVariable String patientId) {
        return ResponseEntity.ok(clinicalOrderService.getPrescriptionInfo(patientId));
    }

    /**
     * @description 诊疗医嘱开方
     * <AUTHOR>
     */
    @PatchMapping("/prescribe")
    public ResponseEntity<Object> prescribe(@RequestParam List<Long> ids) {
        return ResponseEntity.ok(clinicalOrderService.prescribe(ids));
    }

    /**
     * @description 获取诊疗医嘱分页列表
     * <AUTHOR>
     */
    @GetMapping("/pageList")
    public ResponseEntity<Object> pageList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String quy,
            @RequestParam(required = false) Integer status) {
        return ResponseEntity.ok(clinicalOrderService.pageList(pageNum, pageSize, quy, status));
    }

    /**
     * @description 诊疗医嘱取消开方
     * <AUTHOR>
     */
    @PatchMapping("/cancelPrescription")
    public ResponseEntity<Object> cancelPrescription(@RequestParam List<Long> ids) {
        return ResponseEntity.ok(clinicalOrderService.cancelPrescription(ids));
    }

    /**
     * @description 批量核对诊疗医嘱
     * <AUTHOR>
     */
    @PatchMapping("/batchVerify")
    public ResponseEntity<Object> batchVerify(@RequestParam List<Long> ids) {
        return ResponseEntity.ok(clinicalOrderService.batchVerify(ids));
    }

    /**
     * @description 批量生成诊疗医嘱
     * <AUTHOR>
     */
    @PatchMapping("/batchGenerate")
    public ResponseEntity<Object> batchGenerate(@RequestParam List<Long> ids) {
        return ResponseEntity.ok(clinicalOrderService.batchGenerate(ids));
    }

    /**
     * @description 批量执行诊疗医嘱
     * <AUTHOR>
     */
    @PatchMapping("/batchExecute")
    public ResponseEntity<Object> batchExecute(@RequestParam List<Long> ids) {
        return ResponseEntity.ok(clinicalOrderService.batchExecute(ids));
    }
}
