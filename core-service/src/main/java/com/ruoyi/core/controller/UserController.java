package com.ruoyi.core.controller;

import com.ruoyi.core.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping({"/api/user", "/public-api/user"})
public class UserController {
    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping("/{id}/signature")
    public ResponseEntity<Object> getSignatureByID(@PathVariable String id) {
        try {
            return ResponseEntity.ok().body(this.userService.getUserSignatureByID(id));
        } catch (Exception e) {
            log.error("获取用户签名失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "获取用户签名失败"));
        }
    }

    /**
     * 获取用户列表
     *
     * @param option   选项，默认值为"default"，表示获取分页列表；"by-ids"表示根据用户ID获取用户列表
     * @param name     用户姓名
     * @param dept     部门ID
     * @param role     角色ID
     * @param position 职位ID
     * @param ids      用户ID列表，逗号分隔，仅在option为"by-ids"时有效
     * @param pageNum  页码，默认值为1
     * @param pageSize 每页大小，默认值为10
     * @return 用户列表或错误信息
     */
    @GetMapping
    public ResponseEntity<Object> getUserList(
            @RequestParam String option,
            @RequestParam(defaultValue = "") String name,
            @RequestParam(defaultValue = "") String dept,
            @RequestParam(defaultValue = "") String role,
            @RequestParam(defaultValue = "") String position,
            @RequestParam(required = false) String ids,
            @RequestParam(defaultValue = "1") String pageNum,
            @RequestParam(defaultValue = "10") String pageSize
    ) {
        if ("default".equals(option)) {
            try {
                String skip = String.valueOf((Integer.parseInt(pageNum) - 1) * Integer.parseInt(pageSize));
                Map<String, Object> parameters = Map.of(
                        "name", name,
                        "dept", dept,
                        "role", role,
                        "position", position,
                        "skip", skip,
                        "take", pageSize
                );
                Map<String, Object> result = this.userService.getUserList(parameters);
                return ResponseEntity.ok().body(result);
            } catch (Exception e) {
                log.error(e.getMessage());
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "获取用户列表失败"));
            }
        }
        if ("for-component".equals(option)) {
            try {
                Map<String, String> parameters = Map.of(
                        "name", name,
                        "dept", dept,
                        "role", role,
                        "position", position
                );
                List<Map<String, Object>> result = this.userService.getUserListForComponent(parameters);
                return ResponseEntity.ok().body(result);
            } catch (Exception e) {
                log.error(e.getMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "获取用户列表失败"));
            }
        }
        if ("by-ids".equals(option)) {
            if (ids.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("msg", "ids不能为空"));
            }
            List<Map<String, Object>> userList = this.userService.getUserListByIDs(ids);
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map<String, Object> user : userList) {
                result.add(Map.of(
                        "user_id", user.get("user_id").toString(),
                        "user_name", user.get("user_name").toString(),
                        "nick_name", user.get("nick_name").toString()
                ));
            }
            try {
                return ResponseEntity.ok().body(result);
            } catch (Exception e) {
                log.error(e.getMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "获取用户列表失败"));
            }
        }
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(Map.of("msg", "没有数据"));
    }
}
