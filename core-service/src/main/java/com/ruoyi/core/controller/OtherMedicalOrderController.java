package com.ruoyi.core.controller;

import com.ruoyi.core.service.OtherMedicalOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/other-order")
public class OtherMedicalOrderController {

    private final OtherMedicalOrderService otherMedicalOrderService;

    /**
     * @description 新增其他医嘱
     * <AUTHOR>
     */
    @PostMapping
    public ResponseEntity<Object> add(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(otherMedicalOrderService.add(params));
    }

    /**
     * @description 修改其他医嘱
     * <AUTHOR>
     */
    @PutMapping("/{id}")
    public ResponseEntity<Object> update(@PathVariable Long id, @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(otherMedicalOrderService.update(id, params));
    }

    /**
     * @description 删除其他医嘱
     * <AUTHOR>
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Object> delete(@PathVariable Long id) {
        return ResponseEntity.ok(otherMedicalOrderService.delete(id));
    }

    /**
     * @description 根据门诊病历ID查看其他医嘱详情
     * <AUTHOR>
     */
    @GetMapping("/info/{patientId}")
    public ResponseEntity<Object> info(@PathVariable String patientId) {
        return ResponseEntity.ok(otherMedicalOrderService.info(patientId));
    }

    /**
     * @description 根据ID获取其他医嘱详情
     * <AUTHOR>
     */
    @GetMapping("/{id}")
    public ResponseEntity<Object> getDetailById(@PathVariable Long id) {
        return ResponseEntity.ok(otherMedicalOrderService.getDetailById(id));
    }

    /**
     * @description 获取其他医嘱项目类型
     * <AUTHOR>
     */
    @GetMapping("/getItemType")
    public ResponseEntity<Object> getItemType() {
        return ResponseEntity.ok(otherMedicalOrderService.getItemType());
    }

    /**
     * @description 根据门诊病历ID获取其他医嘱列表
     * <AUTHOR>
     */
    @GetMapping("/list/{medicalRecordId}")
    public ResponseEntity<Object> getListByMedicalRecordId(@PathVariable String medicalRecordId) {
        return ResponseEntity.ok(otherMedicalOrderService.getListByMedicalRecordId(medicalRecordId));
    }

    /**
     * @description 其他医嘱开方
     * <AUTHOR>
     */
    @PatchMapping("/prescribe")
    public ResponseEntity<Object> prescribe(@RequestParam Long id) {
        return ResponseEntity.ok(otherMedicalOrderService.prescribe(id));
    }

    /**
     * @description 其他医嘱取消开方
     * <AUTHOR>
     */
    @PatchMapping("/cancelPrescription")
    public ResponseEntity<Object> cancelPrescription(@RequestParam Long id) {
        return ResponseEntity.ok(otherMedicalOrderService.cancelPrescription(id));
    }

    /**
     * @description 获取其他医嘱处方信息
     * <AUTHOR>
     */
    @GetMapping("/prescription/{patientId}")
    public ResponseEntity<Object> getPrescriptionInfo(@PathVariable String patientId) {
        return ResponseEntity.ok(otherMedicalOrderService.getPrescriptionInfo(patientId));
    }
}
