package com.ruoyi.core.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.core.service.ExternalRxService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/external")
public class ExternalRxController {

    private final ExternalRxService externalRxService;

    /**
     * @description 获取外配药信息列表
     * <AUTHOR>
     */
    @GetMapping("/inventory")
    public ResponseEntity<Object> inventory(@RequestParam(defaultValue = "1") Integer pageNum,
                                            @RequestParam(defaultValue = "10") Integer pageSize,
                                            @RequestParam(required = false) String query) {
        return ResponseEntity.ok(externalRxService.inventory(pageNum, pageSize, query));
    }

    /**
     * @description 新增外配药医嘱
     * <AUTHOR>
     */
    @PostMapping
    public ResponseEntity<Object> add(@RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(externalRxService.add(params));
    }

    /**
     * @description 修改外配药医嘱
     * <AUTHOR>
     */
    @PutMapping("/{id}")
    public ResponseEntity<Object> update(@PathVariable Long id, @RequestBody Map<String, Object> params) {
        return ResponseEntity.ok(externalRxService.update(id, params));
    }

    /**
     * @description 删除外配药医嘱
     * <AUTHOR>
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Object> delete(@PathVariable Long id) {
        return ResponseEntity.ok(externalRxService.delete(id));
    }

    /**
     * @description 查看外配药医嘱详情
     * <AUTHOR>
     */
    @GetMapping("/{id}")
    public ResponseEntity<Object> info(@PathVariable Long id) {
        return ResponseEntity.ok(externalRxService.info(id));
    }

    /**
     * @description 根据medical_record_id查询外配药医嘱列表
     * <AUTHOR>
     */
    @GetMapping("/list/{medicalRecordId}")
    public ResponseEntity<Object> list(@PathVariable String medicalRecordId) {
        return ResponseEntity.ok(externalRxService.list(medicalRecordId));
    }

    /**
     * @description 获取外配药处方信息
     * <AUTHOR>
     */
    @GetMapping("/prescription/{patientId}")
    public ResponseEntity<Object> getPrescriptionInfo(@PathVariable String patientId) {
        return ResponseEntity.ok(externalRxService.getPrescriptionInfo(patientId));
    }

    /**
     * @description 获取外配药上次使用情况
     * <AUTHOR>
     */
    @GetMapping("/getLastUsageInfo")
    public AjaxResult getLastUsageInfo(@RequestParam String patientId, @RequestParam String drugStockId) {
        return externalRxService.getLastUsageInfo(patientId, drugStockId);
    }

    /**
     * @description 外配药开方
     * <AUTHOR>
     */
    @PatchMapping("/prescribe")
    public ResponseEntity<Object> prescribe(@RequestParam List<Long> ids) {
        return ResponseEntity.ok(externalRxService.prescribe(ids));
    }

    /**
     * @description 外配药取消开方
     * <AUTHOR>
     */
    @PatchMapping("/cancelPrescription")
    public ResponseEntity<Object> cancelPrescription(@RequestParam List<Long> ids) {
        return ResponseEntity.ok(externalRxService.cancelPrescription(ids));
    }

    /**
     * @description 获取医嘱时间和医嘱日期
     * <AUTHOR>
     */
    @GetMapping("/getDateAndTime/{patientId}")
    public AjaxResult getDateAndTime(@PathVariable Long patientId){
        return externalRxService.getDateAndTime(patientId);
    }
}
