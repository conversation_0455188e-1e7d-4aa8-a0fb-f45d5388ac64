package com.ruoyi.core.controller;

import com.ruoyi.core.service.EnumService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/enum")
public class EnumController {
    private final EnumService enumService;

    public EnumController(EnumService enumService) {
        this.enumService = enumService;
    }

    @GetMapping
    public ResponseEntity<Object> getEnumList(
            @RequestParam String option,
            @RequestParam(value = "keys", required = false) String keys
    ) {
        if (null == option || option.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("msg", "Option cannot be null or empty"));
        }
        if ("by-in".equals(option)) {
            if (null == keys || keys.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("msg", "Keys cannot be null or empty"));
            }
            List<String> keysList = List.of(keys.split(","));
            List<Map<String, Object>> result = enumService.getEnumListByIn(keysList);
            return ResponseEntity.ok().body(result);
        }
        return ResponseEntity.badRequest().body(Map.of("msg", "Invalid option"));
    }
}
