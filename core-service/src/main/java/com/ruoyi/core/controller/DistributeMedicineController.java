package com.ruoyi.core.controller;

import com.ruoyi.core.service.DistributeMedicineService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/distribute")
public class DistributeMedicineController {

    private final DistributeMedicineService distributeMedicineService;

    /**
     * @description 外配药发药列表
     * <AUTHOR>
     */
    @GetMapping
    public ResponseEntity<Object> list(String queryDate){
        return ResponseEntity.ok(distributeMedicineService.list(queryDate));
    }
}
