package com.ruoyi.core.controller;

import com.ruoyi.core.service.MedicalOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 医嘱
 */
@Slf4j
@RestController
@RequestMapping({"/api/medical-order", "/public-api/medical-order"})
public class MedicalOrderController {
    private final MedicalOrderService medicalOrderService;

    public MedicalOrderController(MedicalOrderService medicalOrderService) {
        this.medicalOrderService = medicalOrderService;
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Object> remove(@PathVariable String id) {
        try {
            medicalOrderService.remove(id);
            return ResponseEntity.ok().body(Map.of("msg", "删除成功"));
        } catch (Exception e) {
            log.error("Error deleting medical order with id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
        }
    }

    @PutMapping("/{id}/content")
    public ResponseEntity<Object> updateContent(@PathVariable String id, @RequestBody Map<String, Object> body) {
        try {
            String content = (String) body.get("content");
            if (content == null || content.isBlank()) {
                return ResponseEntity.badRequest().body(Map.of("msg", "内容不能为空"));
            }
            medicalOrderService.updateOrderContent(id, content);
            return ResponseEntity.ok().body(Map.of("msg", "内容更新成功"));
        } catch (Exception e) {
            log.error("Error updating content for medical order with id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
        }
    }

    @PutMapping("/{id}/action/stop")
    public ResponseEntity<Object> switchStopState(@PathVariable String id, @RequestParam String state) {
        try {
            medicalOrderService.switchStopState(id, state);
            return ResponseEntity.ok().body(Map.of("msg", "操作成功"));
        } catch (Exception e) {
            log.error("Error switching stop state for medical order with id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Object> update(
            @PathVariable String id,
            @RequestParam Map<String, String> parameters,
            @RequestBody Map<String, Object> body
    ) {
        String option = parameters.getOrDefault("option", "");
        if ("carry-out".equals(option)) {
            Map<String, String> p = Map.of("id", id);
            this.medicalOrderService.update4CarryOut(p, body);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        }
        if ("check".equals(option)) {
            Map<String, String> p = Map.of("id", id);
            this.medicalOrderService.update4Check(p, body);
            return ResponseEntity.ok().body(Map.of("msg", "ok"));
        }
        return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).body(Map.of("msg", "WIP"));
    }

    @GetMapping("")
    public ResponseEntity<Object> getList(@RequestParam Map<String, String> parameters) {
        String option = parameters.getOrDefault("option", "");
        if ("".equals(option)) {
            try {
                Map<String, Object> p = Map.of(
                        "is_deleted", 0,
                        "type", parameters.getOrDefault("type", ""),
                        "sn", parameters.getOrDefault("sn", ""),
                        "name", parameters.getOrDefault("name", ""),
                        "bed", parameters.getOrDefault("bed", ""),
                        "stopped", parameters.getOrDefault("stopped", ""),
                        "doctor", parameters.getOrDefault("doctor", ""),
                        "dateBegin", parameters.getOrDefault("dateBegin", ""),
                        "dateEnd", parameters.getOrDefault("dateEnd", ""),
                        "content", parameters.getOrDefault("content", "")
                );
                return ResponseEntity.ok().body(medicalOrderService.getList(p));
            } catch (Exception e) {
                log.error("Error fetching medical order list", e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
            }
        }
        if ("新开医嘱执行".equals(option)) {
            try {
                String take = parameters.getOrDefault("pageSize", "10");
                int skip = (Integer.parseInt(parameters.getOrDefault("pageNum", "1")) - 1) * Integer.parseInt(take);
                Map<String, String> p = Map.of(
                        "area", parameters.getOrDefault("area", ""),
                        "name", parameters.getOrDefault("name", ""),
                        "type", parameters.getOrDefault("type", ""),
                        "dateBegin", parameters.getOrDefault("dateBegin", ""),
                        "dateEnd", parameters.getOrDefault("dateEnd", ""),
                        "skip", String.valueOf(skip),
                        "take", take
                );
                return ResponseEntity.ok().body(medicalOrderService.getList4CarryOut(p));
            } catch (Exception e) {
                log.error("Error fetching new medical order execution list", e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
            }
        }
        if ("新开医嘱核对".equals(option)) {
            try {
                String take = parameters.getOrDefault("pageSize", "10");
                int skip = (Integer.parseInt(parameters.getOrDefault("pageNum", "1")) - 1) * Integer.parseInt(take);
                Map<String, String> p = Map.of(
                        "area", parameters.getOrDefault("area", ""),
                        "name", parameters.getOrDefault("name", ""),
                        "type", parameters.getOrDefault("type", ""),
                        "dateBegin", parameters.getOrDefault("dateBegin", ""),
                        "dateEnd", parameters.getOrDefault("dateEnd", ""),
                        "skip", String.valueOf(skip),
                        "take", take
                );
                return ResponseEntity.ok().body(medicalOrderService.getList4Check(p));
            } catch (Exception e) {
                log.error("Error fetching new medical order check list", e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
            }
        }
        if ("长期医嘱核对".equals(option)) {
            String area = parameters.getOrDefault("area", "");
            try {
                return ResponseEntity.ok().body(this.medicalOrderService.getList4CheckStanding(Map.of("area", area)));
            } catch (Exception e) {
                log.error("Error fetching long-term medical order check list", e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
            }
        }
        if ("长期医嘱核对-查看".equals(option)) {
            String sn = parameters.getOrDefault("sn", "");
            if (sn.isBlank()) {
                return ResponseEntity.badRequest().body(Map.of("msg", "住院号不能为空"));
            }
            try {
                return ResponseEntity.ok().body(this.medicalOrderService.getList4CheckStandingBySN(Map.of("sn", sn)));
            } catch (Exception e) {
                log.error("Error fetching long-term medical order check by SN", e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("msg", "服务器错误"));
            }
        }
        return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).body(Map.of("msg", "不支持的操作选项"));
    }
}
