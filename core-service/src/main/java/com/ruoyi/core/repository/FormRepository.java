package com.ruoyi.core.repository;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class FormRepository {
    private final JdbcTemplate jdbcTemplate;

    public FormRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public Map<String, Object> getContentList(String table, String keyword, int take, long skip) {
        String p = "%" + keyword + "%";
        try {
            Map<String, Object> sumResult = jdbcTemplate.queryForMap("""
                    SELECT COUNT(1) AS total FROM db_sfy_product_3.data_ext_P000000192911465
                    WHERE (fuwdxxm LIKE ? OR fuwdxsj LIKE ? OR sfzhm LIKE ?)
                    """, p, p, p);
            List<Map<String, Object>> result = jdbcTemplate.queryForList("""
                    SELECT * FROM db_sfy_product_3.data_ext_P000000192911465
                    WHERE (fuwdxxm LIKE ? OR fuwdxsj LIKE ? OR sfzhm LIKE ?)
                        ORDER BY create_time DESC
                    LIMIT ?, ?
                    """, p, p, p, take, skip);
            return Map.of("total", sumResult.get("total"), "rows", result);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return Map.of("total", 0, "rows", List.of());
        }
    }

    public List<Map<String, Object>> getViewList(String viewCode, String dataCode) {
        String sql = """
                SELECT * FROM db_sfy_product_3.DATA_VIEW_CONFIG
                WHERE auto_no = ? and source_code = ?
                """;
        try {
            return jdbcTemplate.queryForList(sql, viewCode, dataCode);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return List.of();
        }
    }

    public List<Map<String, Object>> getColDefList(String dataCode) {
        String sql = """
                SELECT * FROM db_sfy_product_3.PSO_DATA_DICT WHERE data_code = ?""";
        try {
            return jdbcTemplate.queryForList(sql, dataCode);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return List.of();
        }
    }

    public Map<String, Object> get(String dataCode) {
        String sql = "SELECT * FROM NODE_EXT_DATA WHERE data_code = ?";
        try {
            return jdbcTemplate.queryForMap(sql, dataCode);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return Map.of();
        }
    }
}
