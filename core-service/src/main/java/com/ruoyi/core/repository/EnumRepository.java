package com.ruoyi.core.repository;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class EnumRepository {
    private final JdbcTemplate jdbcTemplate;

    public EnumRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 表单枚举
     *
     * @param enumCode 枚举code
     */
    public List<Map<String, Object>> getEnumListByIn(List<String> enumCode) {
        if (null == enumCode || enumCode.isEmpty()) {
            return List.of();
        }

        NamedParameterJdbcTemplate namedTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
        String sql = """
                SELECT * FROM
                (
                    SELECT * FROM PSO_ENUM_NET
                    WHERE is_sys=1 OR enum_status=1
                ) pa
                WHERE enum_id IN (:enumIds)
                ORDER BY enum_order
                """;
        MapSqlParameterSource parameterSource = new MapSqlParameterSource();
        parameterSource.addValue("enumIds", enumCode);
        try {
            return namedTemplate.queryForList(sql, parameterSource);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return List.of();
        }
    }
}
