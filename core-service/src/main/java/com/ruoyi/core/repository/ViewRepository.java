package com.ruoyi.core.repository;

import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class ViewRepository {
    private final JdbcTemplate jdbcTemplate;

    public ViewRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<Map<String, Object>> getViewConfigListByAppId(String appId) {
        String sql = """
                SELECT * FROM DATA_VIEW_CONFIG
                WHERE app_id = ? AND source_code IN
                  (SELECT view_code FROM node_ext_view WHERE app_id = ?)""";
        try {
            return jdbcTemplate.queryForList(sql, appId, appId);
        } catch (EmptyResultDataAccessException e) {
            return List.of();
        }
    }

    public List<Map<String, Object>> getViewConfigListByView(String formCode) {
        String sql = """
                SELECT
                    ta.*,
                    tb.view_name,
                    tb.view_sname
                FROM
                    DATA_VIEW_CONFIG ta
                LEFT JOIN
                    node_ext_view tb ON ta.source_code = tb.view_code
                WHERE
                    ta.source_code = ?
                """;
        try {
            return jdbcTemplate.queryForList(sql, formCode);
        } catch (EmptyResultDataAccessException e) {
            return List.of();
        }
    }
}
