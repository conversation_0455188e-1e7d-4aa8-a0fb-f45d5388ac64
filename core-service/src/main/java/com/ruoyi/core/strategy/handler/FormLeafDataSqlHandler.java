package com.ruoyi.core.strategy.handler;

import com.ruoyi.core.strategy.LeafDataSqlStrategy;
import org.springframework.stereotype.Component;

@Component
public class FormLeafDataSqlHandler implements LeafDataSqlStrategy {
    @Override
    public String type() {
        return "node_ext_data";
    }

    @Override
    public String buildSql(boolean isAppIdEmpty) {
        String appCondition = isAppIdEmpty ?
                "WHERE data_type IN (0, 2, 8)" :
                "WHERE (ta.app_id = :app_id AND data_type = 0) OR data_type IN (2, 8)";

        String appJoinCondition = isAppIdEmpty ?
                "LEFT JOIN (SELECT data_code, app_design FROM data_app_config) tc ON ta.data_code = tc.data_code" :
                "LEFT JOIN (SELECT data_code, app_design FROM data_app_config WHERE app_id = :app_id) tc ON ta.data_code = tc.data_code";

        return """
                SELECT * FROM (
                    SELECT
                        ta.company_id,
                        ta.app_id,
                        data_type,
                        ta.data_code AS node_id,
                        ta.data_name AS node_name,
                        ta.data_sname AS node_sname,
                        tb.node_type,
                        tb.node_id AS node_pid,
                        CONCAT(tb.node_path, '-', tb.node_id) AS node_path,
                        node_order,
                        '1' AS leaf,
                        ta.data_code,
                        open_invent,
                        is_share,
                        0 AS app_share,
                        data_status AS is_pub,
                        tc.app_design,
                        ta.creator
                    FROM node_ext_data ta
                    JOIN (
                        SELECT
                            ta.node_data_id,
                            ta.node_data_tag,
                            ta.data_order AS node_order,
                            tb.*,
                            tc.node_pid,
                            tc.node_path
                        FROM PSO_NODE_DATA ta
                        LEFT JOIN pso_main_node tb ON ta.node_id = tb.node_id
                        LEFT JOIN pso_node_net tc ON tb.node_id = tc.node_id
                        WHERE ta.node_data_tag = :tag
                    ) tb ON ta.data_code = tb.node_data_id
                    %s
                    %s
                ) pa
                """.formatted(appJoinCondition, appCondition);
    }
}
