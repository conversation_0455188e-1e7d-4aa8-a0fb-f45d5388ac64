package com.ruoyi.core.strategy.handler;

import com.ruoyi.core.strategy.LeafDataSqlStrategy;
import org.springframework.stereotype.Component;

@Component
public class FlowLeafDataSqlHandler implements LeafDataSqlStrategy {
    @Override
    public String type() {
        return "node_ext_wf";
    }

    @Override
    public String buildSql(boolean isAppIdEmpty) {
        String appCondition = isAppIdEmpty ?
                "WHERE node_pid is not null" :
                "WHERE ta.app_id = :app_id AND node_pid is not null";
        return """
                select * from (
                    select
                        ta.company_id,
                        ta.app_id,
                        ta.wf_code as node_id,
                        ta.wf_name as node_name,
                        ta.wf_sname as node_sname,
                        tb.node_type,
                        tb.node_id as node_pid,
                        CONCAT("tb.node_path","'-'","tb.node_id") as node_path,
                        node_order,
                        '1' as leaf,
                        is_share,
                        0 as app_share,
                        is_pub,
                        ta.creator
                    from node_ext_wf ta
                    left join (
                        select
                            ta.node_data_id,
                            ta.data_order as node_order,
                            tb.*,
                            tc.node_pid,
                            tc.node_path
                        from PSO_NODE_DATA ta
                        left join pso_main_node tb on ta.node_id = tb.node_id
                        left join pso_node_net tc on tb.node_id = tc.node_id
                        where ta.node_data_tag = :tag
                    ) tb on ta.wf_code = tb.node_data_id
                    %s
                ) pa
                """.formatted(appCondition);
    }
}
