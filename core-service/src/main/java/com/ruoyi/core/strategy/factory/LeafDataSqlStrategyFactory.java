package com.ruoyi.core.strategy.factory;

import com.ruoyi.core.strategy.LeafDataSqlStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class LeafDataSqlStrategyFactory {

    private final Map<String, LeafDataSqlStrategy> strategyMap = new HashMap<>();

    @Autowired
    public LeafDataSqlStrategyFactory(List<LeafDataSqlStrategy> strategyList) {
        for (LeafDataSqlStrategy strategy : strategyList) {
            strategyMap.put(strategy.type(), strategy);
        }
    }

    public LeafDataSqlStrategy getStrategy(String type) {
        LeafDataSqlStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            throw new IllegalArgumentException("No SQL strategy found for type: " + type);
        }
        return strategy;
    }
}
