package com.ruoyi.core.strategy.handler;

import com.ruoyi.core.strategy.LeafDataSqlStrategy;
import org.springframework.stereotype.Component;

@Component
public class ViewLeafDataSqlHandler implements LeafDataSqlStrategy {
    @Override
    public String type() {
        return "node_ext_view";
    }

    @Override
    public String buildSql(boolean isAppIdEmpty) {
        String appCondition = isAppIdEmpty ?
                "WHERE node_pid IS NOT NULL" :
                "WHERE (ta.app_id = :app_id OR is_sys >= 1) AND node_pid IS NOT NULL";
        return """
                SELECT * FROM (
                    SELECT
                        ta.company_id,
                        ta.app_id,
                        ta.view_code AS node_id,
                        ta.view_name AS node_name,
                        ta.view_sname AS node_sname,
                        tb.node_tag,
                        tb.node_type,
                        tb.node_id AS node_pid,
                        CONCAT(tb.node_path, '-', tb.node_id) AS node_path,
                        node_order,
                        '1' AS leaf,
                        view_status AS is_pub,
                        ta.creator
                    FROM node_ext_view ta
                    LEFT JOIN (
                        SELECT
                            ta.node_data_id,
                            tb.*,
                            tc.node_pid,
                            tc.node_path,
                            ta.data_order AS node_order
                        FROM PSO_NODE_DATA ta
                        LEFT JOIN pso_main_node tb ON ta.node_id = tb.node_id
                        LEFT JOIN pso_node_net tc ON tb.node_id = tc.node_id
                        WHERE ta.node_data_tag = :tag
                    ) tb ON ta.view_code = tb.node_data_id
                    %s
                ) pa
                """.formatted(appCondition);
    }
}