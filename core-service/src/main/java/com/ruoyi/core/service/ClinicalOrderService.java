package com.ruoyi.core.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.enums.ClinicalOrderStatusEnum;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ruoyi.core.service.LogService.MODULE_CLINICAL_ORDER;

@Service
public class ClinicalOrderService {

    private final GenericMapper genericMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final LogService logService;

    public ClinicalOrderService(GenericMapper genericMapper, NamedParameterJdbcTemplate namedParameterJdbcTemplate, LogService logService) {
        this.genericMapper = genericMapper;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.logService = logService;
    }

    /**
     * @description 获取诊疗信息分页列表
     * <AUTHOR>
     */
    public Object clinicalInfo(Integer pageNum, Integer pageSize, String query) {
        String sql = """
                SELECT
                    IFNULL(leaf_id, '') AS leaf_id,
                    IFNULL(zhenlpy, '') AS zhenlpy,
                    IFNULL(zhenlmc, '') AS zhenlmc,
                    IFNULL(zhenljg, '') AS zhenljg,
                    IFNULL(zhenllx, '') AS zhenllx,
                    IFNULL(zhenldw, '') AS zhenldw
                FROM
                    data_ext_zhenldmxx
                WHERE 1=1
                """;

        int offset = (pageNum - 1) * pageSize;
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        if (StrUtil.isNotBlank(query)) {
            params.put("query", "%" + query + "%");
            sql = sql + " AND (zhenlpy LIKE :query OR zhenlmc LIKE :query)";
        }

        String countSql = "SELECT COUNT(*) FROM (" + sql + ") AS count_table";
        sql = sql + """
                 ORDER BY c_time DESC
                 LIMIT :offset, :pageSize
                """;

        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);
        Map<String, Object> map = createDefaultPageResult();
        if (CollectionUtil.isEmpty(list)) {
            return map;
        }

        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        map.replace("rows", list);
        map.replace("total", count);
        return map;
    }

    /**
     * @description 新增诊疗医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object add(Map<String, Object> params) {
        long id = IdUtil.getSnowflakeNextId();
        String username = SecurityUtils.getUsername();
        Map<String, Object> map = new HashMap<>(params);
        map.put("id", id);
        map.put("create_by", username);
        map.put("status", 0); // 默认状态为新开

        // 新增诊疗医嘱记录
        genericMapper.create("clinical_order", map);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_CLINICAL_ORDER, "新增诊疗医嘱", map, null);
        return AjaxResult.success("添加成功", String.valueOf(id));
    }

    /**
     * @description 修改诊疗医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object update(Long id, Map<String, Object> params) {
        String username = SecurityUtils.getUsername();
        Map<String, Object> updateValues = new HashMap<>(params);
        updateValues.put("update_by", username);

        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        condition.put("is_deleted", 0);

        int result = genericMapper.update("clinical_order", updateValues, condition);
        if (result > 0) {
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_CLINICAL_ORDER, "修改诊疗医嘱", params, null);
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.error("修改失败，记录不存在或已被删除");
        }
    }

    /**
     * @description 删除诊疗医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object delete(Long id) {
        String username = SecurityUtils.getUsername();
        Map<String, Object> updateValues = new HashMap<>();
        updateValues.put("is_deleted", 1);
        updateValues.put("update_by", username);

        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        condition.put("is_deleted", 0);

        int result = genericMapper.update("clinical_order", updateValues, condition);
        if (result > 0) {
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_CLINICAL_ORDER, "删除诊疗医嘱", Map.of("id", id), null);
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.error("删除失败，记录不存在或已被删除");
        }
    }

    /**
     * @description 查看诊疗医嘱详情
     * <AUTHOR>
     */
    public Object info(Long id) {
        String sql = selectAllSql() + " WHERE id = :id AND is_deleted = 0";
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);

        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);

            // 添加 status_label 字段
            Object statusObj = result.get("status");
            if (statusObj != null) {
                int status = Integer.parseInt(statusObj.toString());
                result.put("status_label", ClinicalOrderStatusEnum.getValueByKey(status));
            } else {
                result.put("status_label", "未知状态");
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("记录不存在");
        }
    }

    /**
     * @description 根据门诊病历ID获取诊疗医嘱列表
     * <AUTHOR>
     */
    public Object list(String medicalRecordId) {
        String sql = selectAllSql() + " WHERE medical_record_id = :medical_record_id AND is_deleted = 0 ORDER BY create_time DESC";
        Map<String, Object> params = new HashMap<>();
        params.put("medical_record_id", medicalRecordId);

        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);

        if (CollectionUtil.isNotEmpty(list)) {
            // 为每个记录添加 status_label 字段
            for (Map<String, Object> record : list) {
                Object statusObj = record.get("status");
                if (statusObj != null) {
                    int status = Integer.parseInt(statusObj.toString());
                    record.put("status_label", ClinicalOrderStatusEnum.getValueByKey(status));
                } else {
                    record.put("status_label", "未知状态");
                }
            }
        }

        return AjaxResult.success(list);
    }

    /**
     * @description 获取诊疗处方信息（处方号和处方日期）
     * <AUTHOR>
     */
    public Object getPrescriptionInfo(String patientId) {
        String sql = """
                SELECT
                    prescription_number,
                    prescription_date,
                    status
                FROM
                    clinical_order
                WHERE
                    medical_record_id = :patientId
                    AND is_deleted = 0
                LIMIT 1
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("patientId", patientId);

        // 没有记录时返回空对象
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("prescription_number", null);
        emptyResult.put("prescription_date", null);
        emptyResult.put("flag", false);
        emptyResult.put("cancelFlag", false);

        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);
            if (result.get("status").toString().equals("0") || result.get("status").toString().equals("5") || result.get("status").toString().equals("6")) {
                return AjaxResult.success(emptyResult);
            }

            // 处理处方日期格式
            Object prescriptionDateObj = result.get("prescription_date");
            if (prescriptionDateObj != null) {
                String prescriptionDate = prescriptionDateObj.toString();
                DateTime dt = DateUtil.parse(prescriptionDate, "yyyy-MM-dd'T'HH:mm:ss");
                String formatted = DateUtil.format(dt, "yyyy-MM-dd HH:mm:ss");
                result.replace("prescription_date", formatted);
            }

            // true为可以取消开方
            result.put("flag", true);
            String status = result.get("status").toString();
            if (status.equals("1")) {
                result.put("cancelFlag", true);
            } else {
                result.put("cancelFlag", false);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.success(emptyResult);
        }
    }

    /**
     * @description 诊疗医嘱开方
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object prescribe(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要开方的医嘱");
        }

        // 生成统一的处方号和处方日期
        long prescriptionNumber = RandomUtil.randomLong(100000, 999999);
        String prescriptionDate = DateUtil.now();

        // 查询需要开方的医嘱
        String queryOrdersSql = """
                SELECT id
                FROM clinical_order
                WHERE id IN (:ids)
                AND is_deleted = 0
                AND (status = 0 OR status = 5 OR status = 6)
                """;

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ids", ids);

        List<Map<String, Object>> orders;
        try {
            orders = namedParameterJdbcTemplate.queryForList(queryOrdersSql, queryParams);
        } catch (Exception e) {
            return AjaxResult.error("查询医嘱信息失败");
        }

        if (orders.isEmpty()) {
            return AjaxResult.error("未找到可开方的医嘱记录");
        }

        String username = SecurityUtils.getUsername();
        // 批量更新医嘱状态
        String updateOrderSql = """
                UPDATE clinical_order
                SET status = 1,
                    prescription_number = :prescription_number,
                    prescription_date = :prescription_date,
                    prescriber = :prescriber,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("prescription_number", prescriptionNumber);
        updateParams.put("prescription_date", prescriptionDate);
        updateParams.put("update_by", username);
        updateParams.put("prescriber", username);
        updateParams.put("ids", ids);

        namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        // 开方成功后，为每个医嘱插入doctor_order记录
        for (Long id : ids) {
            insertDoctorOrderOnPrescribe(id, SecurityUtils.getUsername());
        }

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_CLINICAL_ORDER, "诊疗医嘱开方", ids, null);
        return AjaxResult.success("开方成功，处方号：" + prescriptionNumber);
    }

    /**
     * @description 获取诊疗医嘱分页列表
     * <AUTHOR>
     */
    public Object pageList(Integer pageNum, Integer pageSize, String quy, Integer status) {
        // 1. 如果有quy参数，先从data_ext_menzbl表中获取对应的leaf_id集合
        List<String> leafIds = null;
        if (StrUtil.isNotBlank(quy)) {
            String leafIdSql = """
                    SELECT leaf_id
                    FROM data_ext_menzbl
                    WHERE quy LIKE :quy
                    """;
            Map<String, Object> quyParams = new HashMap<>();
            quyParams.put("quy", "%" + quy + "%");

            try {
                List<Map<String, Object>> leafIdResults = namedParameterJdbcTemplate.queryForList(leafIdSql, quyParams);
                leafIds = leafIdResults.stream()
                        .map(row -> (String) row.get("leaf_id"))
                        .toList();

                // 如果没有找到匹配的leaf_id，直接返回空结果
                if (leafIds.isEmpty()) {
                    return createDefaultPageResult();
                }
            } catch (Exception e) {
                return createDefaultPageResult();
            }
        }

        // 2. 构建关联查询SQL，包含患者姓名和床位号
        String sql = selectAllWithPatientInfoSql() + " WHERE co.is_deleted = 0 and co.order_type != '继续用药'";

        int offset = (pageNum - 1) * pageSize;
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 3. 如果有leaf_id集合，添加IN条件
        if (CollectionUtil.isNotEmpty(leafIds)) {
            sql += " AND co.medical_record_id IN (:leafIds)";
            params.put("leafIds", leafIds);
        }

        // 4. 如果有status参数，添加status条件
        if (status != null) {
            sql += " AND co.status = :status";
            params.put("status", status);
        }

        // 5. 添加排序和分页
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") AS count_table";
        sql += " ORDER BY co.create_time DESC LIMIT :offset, :pageSize";

        // 6. 执行查询
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);
        Map<String, Object> result = createDefaultPageResult();

        if (CollectionUtil.isEmpty(list)) {
            return result;
        }

        // 7. 获取总数并返回结果
        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        result.replace("rows", list);
        result.replace("total", count);
        return result;
    }

    /**
     * @description 诊疗医嘱取消开方
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object cancelPrescription(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要取消开方的医嘱");
        }

        // 查询需要取消开方的医嘱
        String queryOrdersSql = """
                SELECT id
                FROM clinical_order
                WHERE id IN (:ids)
                AND is_deleted = 0
                AND (status = 1 OR status = 2 OR status = 3)
                """;

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ids", ids);

        List<Map<String, Object>> orders;
        try {
            orders = namedParameterJdbcTemplate.queryForList(queryOrdersSql, queryParams);
        } catch (Exception e) {
            return AjaxResult.error("查询医嘱信息失败");
        }

        if (orders.isEmpty()) {
            return AjaxResult.error("未找到已开方的医嘱记录");
        }

        // 批量更新医嘱状态（诊疗医嘱取消开方状态为5：已退诊）
        String updateOrderSql = """
                UPDATE clinical_order
                SET status = 5,
                    prescription_number = NULL,
                    prescription_date = NULL,
                    prescriber = NULL,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("update_by", SecurityUtils.getUsername());
        updateParams.put("ids", ids);

        namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        // 删除doctor_order表中的记录
        for (Long id : ids) {
            deleteDoctorOrder(id);
        }

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_CLINICAL_ORDER, "诊疗医嘱取消开方", ids, null);
        return AjaxResult.success("取消开方成功");
    }

    /**
     * @description 批量核对诊疗医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object batchVerify(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要核对的医嘱");
        }

        String username = SecurityUtils.getUsername();
        // 批量更新医嘱状态为已核对
        String updateOrderSql = """
                UPDATE clinical_order
                SET status = 2,
                    dispensing_date = NOW(),
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                AND status = 1
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("update_by", username);
        updateParams.put("ids", ids);

        int updatedCount = namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_CLINICAL_ORDER, "批量核对诊疗医嘱", ids, null);
        return AjaxResult.success("核对成功，共核对 " + updatedCount + " 条医嘱");
    }

    /**
     * @description 批量生成诊疗医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object batchGenerate(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要生成的医嘱");
        }

        String username = SecurityUtils.getUsername();
        // 批量更新医嘱状态为已生成
        String updateOrderSql = """
                UPDATE clinical_order
                SET status = 3,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                AND status = 2
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("update_by", username);
        updateParams.put("ids", ids);

        int updatedCount = namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_CLINICAL_ORDER, "批量生成诊疗医嘱", ids, null);
        return AjaxResult.success("生成成功，共生成 " + updatedCount + " 条医嘱");
    }

    /**
     * @description 批量执行诊疗医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object batchExecute(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要执行的医嘱");
        }

        String username = SecurityUtils.getUsername();
        // 批量更新医嘱状态为已执行
        String updateOrderSql = """
                UPDATE clinical_order
                SET status = 4,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                AND status = 3
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("update_by", username);
        updateParams.put("ids", ids);

        int updatedCount = namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_CLINICAL_ORDER, "批量执行诊疗医嘱", ids, null);
        return AjaxResult.success("执行成功，共执行 " + updatedCount + " 条医嘱");
    }

    /**
     * @return SQL字符串
     * @description 构建查询所有字段的SQL
     */
    private String selectAllSql() {
        return """
                SELECT
                    CAST(id AS CHAR) AS id,
                    medical_record_id,
                    clinical_info_id,
                    treatment_name,
                    unit,
                    price,
                    quantity,
                    clinical_date,
                    daily_frequency,
                    order_date,
                    order_time,
                    order_type,
                    doctor_name,
                    nurse_name,
                    note,
                    status,
                    prescription_number,
                    prescription_date,
                    prescriber,
                    dispensing_date,
                    is_deleted,
                    create_time,
                    create_by,
                    update_time,
                    update_by,
                    'exist' as state
                FROM clinical_order
                """;
    }

    /**
     * @return SQL字符串
     * @description 构建关联患者信息的查询SQL
     */
    private String selectAllWithPatientInfoSql() {
        return """
                SELECT
                    CAST(co.id AS CHAR) AS id,
                    co.medical_record_id,
                    co.clinical_info_id,
                    co.treatment_name,
                    co.unit,
                    co.price,
                    co.quantity,
                    co.clinical_date,
                    co.daily_frequency,
                    co.order_date,
                    co.order_time,
                    co.order_type,
                    co.doctor_name,
                    co.nurse_name,
                    co.note,
                    co.status,
                    co.prescription_number,
                    co.prescription_date,
                    co.prescriber,
                    co.dispensing_date,
                    co.is_deleted,
                    co.create_time,
                    co.create_by,
                    co.update_time,
                    co.update_by,
                    IFNULL(mb.xingming, '') AS patient_name,
                    IFNULL(mb.chuangw_no, '') AS bed_number,
                    IFNULL(mb.zhuyuanh, '') AS hospitalization_id,
                    IFNULL(mb.quy, '') AS area,
                    'exist' as state
                FROM clinical_order co
                LEFT JOIN data_ext_menzbl mb ON co.medical_record_id = mb.leaf_id
                """;
    }

    /**
     * @param clinicalOrderId 诊疗医嘱ID
     * @param username        用户名
     * @description 开方时插入医嘱表记录
     */
    private void insertDoctorOrderOnPrescribe(Long clinicalOrderId, String username) {
        try {
            // 查询clinical_order的完整数据
            String querySql = """
                    SELECT * FROM clinical_order
                    WHERE id = :id AND is_deleted = 0
                    """;
            Map<String, Object> clinicalOrderData;
            try {
                clinicalOrderData = namedParameterJdbcTemplate.queryForMap(
                        querySql, Map.of("id", clinicalOrderId));
            } catch (Exception e) {
                return;
            }

            // 获取患者信息
            String medicalRecordId = clinicalOrderData.get("medical_record_id").toString();
            Map<String, Object> patientInfo = getPatientInfo(medicalRecordId);

            // 生成医嘱详细内容（诊疗名称+数量）
            String content = generateOrderContent(clinicalOrderData);

            // 构建doctor_order数据
            Map<String, Object> doctorOrderData = new HashMap<>();
            doctorOrderData.put("id", IdUtil.getSnowflakeNextIdStr());
            doctorOrderData.put("medication_order_id", clinicalOrderId);
            doctorOrderData.put("area", patientInfo.get("quy"));
            doctorOrderData.put("bed_number", patientInfo.get("chuangw_no"));
            doctorOrderData.put("hospitalization_id", patientInfo.get("zhuyuanh"));
            doctorOrderData.put("patient_name", patientInfo.get("xingming"));
            doctorOrderData.put("order_time", DateUtil.now());
            doctorOrderData.put("doctor_name", username);
            doctorOrderData.put("order_name", "诊疗医嘱");
            doctorOrderData.put("order_type", clinicalOrderData.get("order_type"));
            doctorOrderData.put("content", content);
            doctorOrderData.put("created_by", username);
            doctorOrderData.put("is_deleted", 0);
            doctorOrderData.put("status", 0);

            genericMapper.create("doctor_order", doctorOrderData);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("开方时插入医嘱表失败: " + e.getMessage());
        }
    }

    /**
     * @param clinicalOrderId 诊疗医嘱ID
     * @description 删除医嘱表记录
     */
    private void deleteDoctorOrder(Long clinicalOrderId) {
        try {
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("is_deleted", 1);

            Map<String, Object> condition = new HashMap<>();
            condition.put("medication_order_id", clinicalOrderId);

            genericMapper.update("doctor_order", updateData, condition);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("删除医嘱表记录失败: " + e.getMessage());
        }
    }

    /**
     * @param medicalRecordId 门诊病历ID
     * @return 患者信息
     * @description 获取患者信息
     */
    private Map<String, Object> getPatientInfo(String medicalRecordId) {
        String sql = """
                SELECT quy, chuangw_no, zhuyuanh, xingming
                FROM data_ext_menzbl
                WHERE leaf_id = :medicalRecordId
                """;
        try {
            return namedParameterJdbcTemplate.queryForMap(sql, Map.of("medicalRecordId", medicalRecordId));
        } catch (Exception e) {
            // 返回默认值
            Map<String, Object> defaultInfo = new HashMap<>();
            defaultInfo.put("quy", "");
            defaultInfo.put("chuangw_no", "");
            defaultInfo.put("zhuyuanh", "");
            defaultInfo.put("xingming", "");
            return defaultInfo;
        }
    }

    /**
     * @param clinicalOrderData 诊疗医嘱数据
     * @return 医嘱详细内容
     * @description 生成诊疗医嘱详细内容（诊疗名称+数量）
     */
    private String generateOrderContent(Map<String, Object> clinicalOrderData) {
        String treatmentName = getStringValue(clinicalOrderData, "treatment_name");
        String quantity = getStringValue(clinicalOrderData, "quantity");

        StringBuilder content = new StringBuilder();
        content.append(treatmentName);
        if (StrUtil.isNotBlank(quantity)) {
            content.append(quantity);
        }

        return content.toString();
    }

    /**
     * @param map 数据Map
     * @param key 键
     * @return 字符串值，如果为null则返回空字符串
     * @description 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 创建默认的分页返回结果
     *
     * @return 包含默认值的Map对象
     */
    private Map<String, Object> createDefaultPageResult() {
        Map<String, Object> countMap = new HashMap<>();
        countMap.put("code", 200);
        countMap.put("rows", Collections.EMPTY_LIST);
        countMap.put("total", 0);
        return countMap;
    }
}
