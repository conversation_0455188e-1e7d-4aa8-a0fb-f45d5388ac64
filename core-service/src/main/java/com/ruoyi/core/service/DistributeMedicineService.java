package com.ruoyi.core.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.core.mapper.GenericMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
public class DistributeMedicineService {

    private final GenericMapper genericMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    /**
     * @description 外配药发药列表
     * <AUTHOR>
     */
    public Object list(String queryDate) {
        if (StrUtil.isBlank(queryDate)) {
            return Map.of("code", 400, "msg", "查询日期不能为空");
        }

        try {
            // 解析查询日期
            java.time.LocalDate targetDate = java.time.LocalDate.parse(queryDate);
            java.time.DayOfWeek dayOfWeek = targetDate.getDayOfWeek();
            String weekDayName = getChineseWeekDay(dayOfWeek);

            List<Map<String, Object>> result = new ArrayList<>();

            // 1. 查询所有长期医嘱（status=2，已核对）
            List<Map<String, Object>> longTermOrders = getLongTermOrders();

            // 2. 处理外配药医嘱
            List<Map<String, Object>> externalRxOrders = getExternalRxOrders(longTermOrders);
            for (Map<String, Object> order : externalRxOrders) {
                if (shouldShowOnDate(order, targetDate, weekDayName)) {
                    Map<String, Object> medicationCard = buildMedicationCard(order);
                    result.add(medicationCard);
                }
            }

            // 按住院号排序
            result.sort((a, b) -> {
                String hospitalIdA = (String) a.get("hospitalizationId");
                String hospitalIdB = (String) b.get("hospitalizationId");
                if (hospitalIdA == null && hospitalIdB == null) return 0;
                if (hospitalIdA == null) return 1;
                if (hospitalIdB == null) return -1;
                return hospitalIdA.compareTo(hospitalIdB);
            });

            // 3. 处理外配药发药记录插入逻辑
            processExternalRxDistributionRecords(externalRxOrders, targetDate, weekDayName);

            return Map.of("code", 200, "data", result, "total", result.size());

        } catch (Exception e) {
            return Map.of("code", 500, "msg", "生成外配药发药列表失败: " + e.getMessage());
        }
    }

    /**
     * @description 批量发药
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object batchDistribute(String date, List<Long> ids) {
        if (StrUtil.isBlank(date)) {
            return Map.of("code", 400, "msg", "发药日期不能为空");
        }

        if (ids == null || ids.isEmpty()) {
            return Map.of("code", 400, "msg", "外配药医嘱ID列表不能为空");
        }

        try {
            // 1. 根据ID查询外配药医嘱信息
            List<Map<String, Object>> externalRxList = getExternalRxByIds(ids);
            if (externalRxList.isEmpty()) {
                return Map.of("code", 400, "msg", "未找到对应的外配药医嘱记录");
            }

            int successCount = 0;
            List<String> failedMessages = new ArrayList<>();

            // 2. 逐个处理发药
            for (Map<String, Object> externalRx : externalRxList) {
                try {
                    boolean success = processSingleDistribute(externalRx, date);
                    if (success) {
                        successCount++;
                    } else {
                        String drugName = (String) externalRx.getOrDefault("drug_name", "未知药品");
                        failedMessages.add(drugName + ": 库存不足");
                    }
                } catch (Exception e) {
                    String drugName = (String) externalRx.getOrDefault("drug_name", "未知药品");
                    failedMessages.add(drugName + ": " + e.getMessage());
                }
            }

            // 3. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("total", externalRxList.size());
            result.put("success", successCount);
            result.put("failed", externalRxList.size() - successCount);

            if (failedMessages.isEmpty()) {
                return Map.of("code", 200, "msg", "批量发药成功", "data", result);
            } else {
                result.put("failedMessages", failedMessages);
                return Map.of("code", 200, "msg", "部分发药成功", "data", result);
            }

        } catch (Exception e) {
            return Map.of("code", 500, "msg", "批量发药失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有长期医嘱（status=2，已核对）
     */
    private List<Map<String, Object>> getLongTermOrders() {
        String sql = """
                SELECT medication_order_id, order_type, order_name
                FROM doctor_order
                WHERE status = 2
                AND order_type = '长期'
                AND is_deleted = 0
                AND order_name = '外配药医嘱'
                """;
        return namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
    }

    /**
     * 获取外配药医嘱（口服给药途径，排除已停药的医嘱）
     */
    private List<Map<String, Object>> getExternalRxOrders(List<Map<String, Object>> longTermOrders) {
        if (longTermOrders.isEmpty()) {
            return new ArrayList<>();
        }

        // 过滤出外配药医嘱类型的订单ID
        List<String> externalRxOrderIds = longTermOrders.stream()
                .filter(order -> "外配药医嘱".equals(order.get("order_name")))
                .map(order -> (String) order.get("medication_order_id"))
                .distinct()
                .toList();

        if (externalRxOrderIds.isEmpty()) {
            return new ArrayList<>();
        }

        String sql = """
                SELECT er.*, mb.xingming, mb.zhuyuanh, mb.chuangw_no, wkc.meiryl, wkc.kucsl
                FROM external_rx er
                LEFT JOIN data_ext_menzbl mb ON er.medical_record_id = mb.leaf_id
                LEFT JOIN data_ext_waipykc wkc ON er.drug_stock_id = wkc.leaf_id
                LEFT JOIN out_stop_medicine osm ON er.id = osm.medication_order_id
                    AND osm.status = 1
                    AND osm.is_deleted = 0
                WHERE er.id IN (:orderIds)
                AND er.administration_route = '口服'
                AND er.is_deleted = 0
                AND osm.id IS NULL
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("orderIds", externalRxOrderIds);
        return namedParameterJdbcTemplate.queryForList(sql, params);
    }

    /**
     * 判断是否应该在指定日期显示
     */
    private boolean shouldShowOnDate(Map<String, Object> order, java.time.LocalDate targetDate, String weekDayName) {
        String administrationMethod = (String) order.get("administration_method");
        if (StrUtil.isBlank(administrationMethod)) {
            return false;
        }

        administrationMethod = administrationMethod.toUpperCase();

        // 简单的每日给药方法，需要检查是否在医嘱开始日期之后
        if (administrationMethod.equals("QD") || administrationMethod.equals("BID") ||
                administrationMethod.equals("TID") || administrationMethod.equals("QID") ||
                administrationMethod.equals("Q4H") || administrationMethod.equals("Q6H") ||
                administrationMethod.equals("Q8H") || administrationMethod.equals("Q12H") ||
                administrationMethod.equals("QN")) {

            java.time.LocalDate orderDate = getOrderDate(order);
            if (orderDate == null) {
                // 如果无法获取医嘱日期，默认显示
                return true;
            }
            // 只有在医嘱开始日期当天或之后才显示
            return !targetDate.isBefore(orderDate);
        }

        // 复杂的给药方法需要计算
        java.time.LocalDate orderDate = getOrderDate(order);
        if (orderDate == null) {
            return false;
        }

        switch (administrationMethod) {
            case "QOD": // 隔天一次
                return calculateQodDisplay(orderDate, targetDate);
            case "QW": // 一周一次
            case "BIW": // 一周两次
            case "TIW": // 一周三次
                return calculateWeeklyDisplay(order, targetDate, weekDayName);
            default:
                return false;
        }
    }

    /**
     * 获取医嘱开始日期
     */
    private java.time.LocalDate getOrderDate(Map<String, Object> order) {
        // 优先使用 prescription_date
        Object orderDateObj = order.get("prescription_date");
        if (orderDateObj == null) {
            return null;
        }

        try {
            if (orderDateObj instanceof java.time.LocalDate) {
                return (java.time.LocalDate) orderDateObj;
            } else if (orderDateObj instanceof java.time.LocalDateTime) {
                return ((java.time.LocalDateTime) orderDateObj).toLocalDate();
            } else if (orderDateObj instanceof java.sql.Date) {
                return ((java.sql.Date) orderDateObj).toLocalDate();
            } else if (orderDateObj instanceof java.sql.Timestamp) {
                return ((java.sql.Timestamp) orderDateObj).toLocalDateTime().toLocalDate();
            } else if (orderDateObj instanceof java.util.Date) {
                return java.time.Instant.ofEpochMilli(((java.util.Date) orderDateObj).getTime())
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDate();
            } else if (orderDateObj instanceof String dateStr) {
                // 尝试解析不同的日期格式
                if (dateStr.contains("-")) {
                    if (dateStr.length() == 10) { // yyyy-MM-dd
                        return java.time.LocalDate.parse(dateStr);
                    } else if (dateStr.contains(" ")) { // yyyy-MM-dd HH:mm:ss
                        return java.time.LocalDateTime.parse(dateStr.replace(" ", "T")).toLocalDate();
                    }
                }
            }
        } catch (Exception e) {
            // 日期解析失败，记录日志但不抛出异常
            System.err.println("日期解析失败: " + orderDateObj + " (类型: " + orderDateObj.getClass().getName() + "), 错误: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 计算隔天一次(QOD)是否在目标日期显示
     */
    private boolean calculateQodDisplay(java.time.LocalDate orderDate, java.time.LocalDate targetDate) {
        // 如果目标日期在医嘱开始日期之前，不显示
        if (targetDate.isBefore(orderDate)) {
            return false;
        }

        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(orderDate, targetDate);
        // 当天开的医嘱（daysBetween = 0）应该显示
        // 隔天一次：第0天、第2天、第4天...显示
        return daysBetween % 2 == 0;
    }

    /**
     * 计算每周给药是否在目标日期显示
     */
    private boolean calculateWeeklyDisplay(Map<String, Object> order, java.time.LocalDate targetDate, String weekDayName) {
        // 首先检查是否在医嘱开始日期之后
        java.time.LocalDate orderDate = getOrderDate(order);
        if (orderDate != null && targetDate.isBefore(orderDate)) {
            return false;
        }

        String weekDay = (String) order.get("week_day");
        if (StrUtil.isBlank(weekDay)) {
            return false;
        }

        // 解析week_day字段，格式如："一，二" 或 "一、二、三"
        String[] weekDays = weekDay.split("[，,、]");
        for (String day : weekDays) {
            if (day.trim().equals(weekDayName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将英文星期转换为中文
     */
    private String getChineseWeekDay(java.time.DayOfWeek dayOfWeek) {
        return switch (dayOfWeek) {
            case MONDAY -> "一";
            case TUESDAY -> "二";
            case WEDNESDAY -> "三";
            case THURSDAY -> "四";
            case FRIDAY -> "五";
            case SATURDAY -> "六";
            case SUNDAY -> "日";
        };
    }

    /**
     * 检查外配药库存是否充足
     */
    private boolean hasEnoughStock(Map<String, Object> order) {
        String drugName = (String) order.get("drug_name");
        String zhuyuanh = (String) order.get("zhuyuanh");
        String xingming = (String) order.get("xingming");

        if (StrUtil.isBlank(drugName) || StrUtil.isBlank(zhuyuanh) || StrUtil.isBlank(xingming)) {
            return false;
        }

        String sql = """
                SELECT kucsl
                FROM data_ext_waipykc
                WHERE yaopmc = :drugName
                AND zhuyh = :zhuyuanh
                AND xingm = :xingming
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("drugName", drugName);
        params.put("zhuyuanh", zhuyuanh);
        params.put("xingming", xingming);

        try {
            Map<String, Object> stockInfo = namedParameterJdbcTemplate.queryForMap(sql, params);
            String kucslStr = (String) stockInfo.get("kucsl");
            if (StrUtil.isNotBlank(kucslStr)) {
                double stock = Double.parseDouble(kucslStr);
                return stock > 0;
            }
        } catch (Exception e) {
            // 查询失败或没有库存记录，认为库存不足
        }
        return false;
    }

    /**
     * 构建药卡信息
     */
    private Map<String, Object> buildMedicationCard(Map<String, Object> order) {
        Map<String, Object> card = new HashMap<>();

        // 外配药医嘱主键ID（转为字符串）
        Object idObj = order.get("id");
        String externalRxId = idObj != null ? String.valueOf(idObj) : "";
        card.put("id", externalRxId);

        // 患者信息
        card.put("patientName", order.get("xingming"));
        card.put("hospitalizationId", order.get("zhuyuanh"));
        card.put("bedNumber", order.get("chuangw_no"));

        // 药品信息
        card.put("drugName", order.getOrDefault("drug_name", ""));
        card.put("drugSpec", order.getOrDefault("drug_spec", ""));
        card.put("dailyUsage", order.getOrDefault("administration_method", ""));
        card.put("orderDate", order.get("order_date"));

        // 构建用法字符串（早中晚夜以及对应的时间）
        String usage = buildUsageString(order);
        card.put("usage", usage);

        // 添加发药标志
        boolean distributeFlag = calculateDistributeFlag(order);
        card.put("distribute_flag", distributeFlag);

        // 每日数量
        card.put("meiryl", order.getOrDefault("meiryl", ""));

        return card;
    }

    /**
     * 构建用法字符串
     */
    private String buildUsageString(Map<String, Object> order) {
        StringBuilder usage = new StringBuilder();
        String singleDoseUnit = (String) order.get("single_dose_unit");
        if (StrUtil.isBlank(singleDoseUnit)) {
            singleDoseUnit = "";
        }

        // 早
        if (order.get("morning_quantity") != null &&
                ((Number) order.get("morning_quantity")).doubleValue() > 0) {
            usage.append("早").append(order.get("morning_quantity"));
            if (order.get("morning_time") != null) {
                usage.append("(").append(order.get("morning_time")).append(")");
            }
            usage.append(singleDoseUnit).append(" ");
        }

        // 中
        if (order.get("noon_quantity") != null &&
                ((Number) order.get("noon_quantity")).doubleValue() > 0) {
            usage.append("中").append(order.get("noon_quantity"));
            if (order.get("noon_time") != null) {
                usage.append("(").append(order.get("noon_time")).append(")");
            }
            usage.append(singleDoseUnit).append(" ");
        }

        // 晚
        if (order.get("evening_quantity") != null &&
                ((Number) order.get("evening_quantity")).doubleValue() > 0) {
            usage.append("晚").append(order.get("evening_quantity"));
            if (order.get("evening_time") != null) {
                usage.append("(").append(order.get("evening_time")).append(")");
            }
            usage.append(singleDoseUnit).append(" ");
        }

        // 夜
        if (order.get("night_quantity") != null &&
                ((Number) order.get("night_quantity")).doubleValue() > 0) {
            usage.append("夜").append(order.get("night_quantity"));
            if (order.get("night_time") != null) {
                usage.append("(").append(order.get("night_time")).append(")");
            }
            usage.append(singleDoseUnit).append(" ");
        }

        return usage.toString().trim();
    }

    /**
     * 计算发药标志
     * 提取meiryl中的数字，如果数字大于kucsl，则distribute_flag为false，否则为true
     */
    private boolean calculateDistributeFlag(Map<String, Object> order) {
        String meiryl = (String) order.get("meiryl");
        String kucsl = (String) order.get("kucsl");

        // 如果meiryl或kucsl为空，默认不能发药
        if (StrUtil.isBlank(meiryl) || StrUtil.isBlank(kucsl)) {
            return false;
        }

        try {
            // 提取meiryl中的数字
            double meirylNumber = extractNumberFromString(meiryl);
            // 解析kucsl
            double kucslNumber = Double.parseDouble(kucsl);

            // 如果meiryl中的数字大于kucsl，则不能发药
            return meirylNumber <= kucslNumber;

        } catch (Exception e) {
            // 解析失败，默认不能发药
            return false;
        }
    }

    /**
     * 从字符串中提取数字
     */
    private double extractNumberFromString(String str) {
        if (StrUtil.isBlank(str)) {
            return 0.0;
        }

        // 使用正则表达式提取数字（包括小数）
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+(\\.\\d+)?");
        java.util.regex.Matcher matcher = pattern.matcher(str);

        if (matcher.find()) {
            return Double.parseDouble(matcher.group());
        }

        return 0.0;
    }

    /**
     * 根据ID查询外配药医嘱信息
     */
    private List<Map<String, Object>> getExternalRxByIds(List<Long> ids) {
        String sql = """
                SELECT er.*, wkc.meiryl, wkc.kucsl
                FROM external_rx er
                LEFT JOIN data_ext_waipykc wkc ON er.drug_stock_id = wkc.leaf_id
                WHERE er.id IN (:ids)
                AND er.is_deleted = 0
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        return namedParameterJdbcTemplate.queryForList(sql, params);
    }

    /**
     * 处理单个发药
     */
    private boolean processSingleDistribute(Map<String, Object> externalRx, String date) {
        String drugStockId = (String) externalRx.getOrDefault("drug_stock_id", "");
        String meiryl = (String) externalRx.getOrDefault("meiryl", "");
        String kucsl = (String) externalRx.getOrDefault("kucsl", "");
        Long externalRxId = (Long) externalRx.get("id");

        if (StrUtil.isBlank(drugStockId) || StrUtil.isBlank(meiryl) || StrUtil.isBlank(kucsl)) {
            return false;
        }

        try {
            // 1. 提取每日用量中的数字
            double dailyUsage = extractNumberFromString(meiryl);
            double currentStock = Double.parseDouble(kucsl);

            // 2. 检查库存是否充足
            if (currentStock < dailyUsage) {
                return false;
            }

            // 3. 计算新库存
            double newStock = currentStock - dailyUsage;

            // 4. 更新库存
            boolean stockUpdated = updateStock(drugStockId, String.valueOf(newStock));
            if (!stockUpdated) {
                return false;
            }

            // 5. 更新发药表状态
            updateDistributionStatus(externalRxId, drugStockId, date);

            return true;

        } catch (Exception e) {
            throw new RuntimeException("发药处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理外配药发药记录插入逻辑
     */
    private void processExternalRxDistributionRecords(List<Map<String, Object>> externalRxOrders,
                                                      java.time.LocalDate targetDate,
                                                      String weekDayName) {
        for (Map<String, Object> order : externalRxOrders) {
            if (shouldShowOnDate(order, targetDate, weekDayName)) {
                insertDistributionRecordIfNotExists(order, targetDate);
            }
        }
    }

    /**
     * 如果发药记录不存在则插入
     */
    private void insertDistributionRecordIfNotExists(Map<String, Object> order, java.time.LocalDate targetDate) {
        String zhuyh = (String) order.getOrDefault("zhuyuanh", "");
        String drugStockId = (String) order.getOrDefault("drug_stock_id", "");
        String targetDateStr = targetDate.toString();

        if (StrUtil.isBlank(zhuyh) || StrUtil.isBlank(drugStockId)) {
            return;
        }

        // 检查是否已存在记录
        if (existsDistributionRecord(zhuyh, drugStockId, targetDateStr)) {
            return;
        }

        // 获取外配药库存信息
        Map<String, Object> stockInfo = getStockInfo(drugStockId);
        if (stockInfo == null || stockInfo.isEmpty()) {
            return;
        }

        // 插入发药记录
        insertDistributionRecord(order, stockInfo, targetDateStr);
    }

    /**
     * 检查发药记录是否已存在
     */
    private boolean existsDistributionRecord(String zhuyh, String drugStockId, String targetDate) {
        String sql = """
                SELECT COUNT(1) as count
                FROM data_ext_waipyfy
                WHERE zhuyh = :zhuyh
                AND waipykcb = :drugStockId
                AND DATE(c_time) = :targetDate
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("zhuyh", zhuyh);
        params.put("drugStockId", drugStockId);
        params.put("targetDate", targetDate);

        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);
            Long count = (Long) result.get("count");
            return count != null && count > 0;
        } catch (Exception e) {
            // 查询失败，默认认为不存在
            return false;
        }
    }

    /**
     * 获取外配药库存信息
     */
    private Map<String, Object> getStockInfo(String drugStockId) {
        String sql = """
                SELECT *
                FROM data_ext_waipykc
                WHERE leaf_id = :drugStockId
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("drugStockId", drugStockId);

        try {
            return namedParameterJdbcTemplate.queryForMap(sql, params);
        } catch (Exception e) {
            // 查询失败或没有找到记录
            return null;
        }
    }

    /**
     * 插入发药记录
     */
    private void insertDistributionRecord(Map<String, Object> order, Map<String, Object> stockInfo, String targetDate) {
        try {
            String leafId = IdUtil.getSnowflakeNextIdStr();
            String currentTime = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            Map<String, Object> params = new HashMap<>();
            // 基础字段
            params.put("app_id", "");
            params.put("data_code", "waipyfy");
            params.put("leaf_id", leafId);
            params.put("d_status", 0);
            params.put("d_stage", 0);
            params.put("d_audit", 0);
            params.put("sync_status", 0);
            params.put("is_chain", 0);
            params.put("pub_status", 0);
            params.put("share_status", 0);
            params.put("creator", "admin");
            params.put("x_creator", "admin");
            params.put("c_time", currentTime);

            // 关联字段
            params.put("waipykcb", stockInfo.getOrDefault("leaf_id", "")); // 外配药库存ID
            params.put("zhuyh", order.getOrDefault("zhuyuanh", "")); // 住院号
            params.put("xingm", order.getOrDefault("xingming", "")); // 姓名

            // 从库存表复制的字段
            params.put("quy", stockInfo.getOrDefault("quy", ""));
            params.put("x_quy", stockInfo.getOrDefault("quy", ""));
            params.put("chuangwh", stockInfo.getOrDefault("chuangwh", ""));
            params.put("yaoppy", stockInfo.getOrDefault("yaoppy", ""));
            params.put("yaopmc", stockInfo.getOrDefault("yaopmc", ""));
            params.put("yaopgg", stockInfo.getOrDefault("yaopgg", ""));
            params.put("shengccj", stockInfo.getOrDefault("shengccj", ""));
            params.put("yaoply", stockInfo.getOrDefault("yaoply", ""));
            params.put("x_yaoply", stockInfo.getOrDefault("x_yaoply", ""));
            params.put("rukrq", targetDate); // 入库日期设为目标日期
            params.put("youxrq", stockInfo.getOrDefault("tixrq", "")); // 有效日期从库存表的提取日期
            params.put("yaoppc", ""); // 药品批次
            params.put("jix", stockInfo.getOrDefault("jix", ""));
            params.put("x_jix", stockInfo.getOrDefault("x_jix", ""));
            params.put("kucsl", stockInfo.getOrDefault("kucsl", ""));
            params.put("rics", stockInfo.getOrDefault("rics", ""));
            params.put("meiryl", stockInfo.getOrDefault("meiryl", ""));
            params.put("ciyl", stockInfo.getOrDefault("ciyl", ""));
            params.put("yaopdw", stockInfo.getOrDefault("yaopdw", ""));

            genericMapper.create("data_ext_waipyfy", params);

        } catch (Exception e) {
            // 插入失败，记录日志但不抛出异常，避免影响主流程
            System.err.println("插入外配药发药记录失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 更新库存
     */
    private boolean updateStock(String drugStockId, String newStock) {
        String sql = """
                UPDATE data_ext_waipykc
                SET kucsl = :newStock
                WHERE leaf_id = :drugStockId
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("newStock", newStock);
        params.put("drugStockId", drugStockId);

        try {
            int updatedRows = namedParameterJdbcTemplate.update(sql, params);
            return updatedRows > 0;
        } catch (Exception e) {
            System.err.println("更新库存失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 更新发药表状态
     */
    private void updateDistributionStatus(Long externalRxId, String drugStockId, String date) {
        // 查找对应的发药记录
        String findSql = """
                SELECT leaf_id
                FROM data_ext_waipyfy
                WHERE waipykcb = :drugStockId
                AND DATE(c_time) = :date
                LIMIT 1
                """;

        Map<String, Object> findParams = new HashMap<>();
        findParams.put("drugStockId", drugStockId);
        findParams.put("date", date);

        try {
            Map<String, Object> distributionRecord = namedParameterJdbcTemplate.queryForMap(findSql, findParams);
            String leafId = (String) distributionRecord.get("leaf_id");

            // 更新状态为已发药
            String updateSql = """
                    UPDATE data_ext_waipyfy
                    SET d_status = 1
                    WHERE leaf_id = :leafId
                    """;

            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("leafId", leafId);
            namedParameterJdbcTemplate.update(updateSql, updateParams);

        } catch (Exception e) {
            System.err.println("更新发药状态失败: " + e.getMessage());
            // 不抛出异常，避免影响库存扣减
        }
    }

}
