package com.ruoyi.core.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.core.mapper.GenericMapper;
import com.ruoyi.core.utility.Number2String;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class UserService {
    private static final String TABLE_USER = "sys_user";
    private static final String TABLE_USER_ROLE = "sys_user_role";
    private static final String TABLE_USER_POSITION = "sys_user_post";
    private final GenericMapper genericMapper;

    public UserService(GenericMapper genericMapper) {
        this.genericMapper = genericMapper;
    }

    @SuppressWarnings("unchecked")
    public Map<String, Object> getUserSignatureByID(String id) {
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "user_id", id});
        List<Map<String, Object>> userList = genericMapper.getList(
                TABLE_USER,
                "user_id, detail_json",
                conditions,
                "limit 1"
        );
        if (userList.isEmpty()) {
            return Map.of("signature", "");
        } else {
            Map<String, Object> user = userList.get(0);
            ObjectMapper objectMapper = new ObjectMapper();
            String detailJson = (String) user.get("detail_json");
            if (detailJson == null || detailJson.isBlank()) {
                return Map.of("user_id", id, "signature", "");
            }
            Map<String, Object> detail;
            String signature = "";
            try {
                detail = objectMapper.readValue(detailJson, Map.class);
                if (detail.containsKey("signature")) {
                    signature = detail.get("signature").toString();
                }
            } catch (Exception e) {
                return Map.of("user_id", id, "signature", "");
            }
            if (signature == null || signature.isBlank()) {
                return Map.of("user_id", id, "signature", "");
            } else {
                return Map.of("user_id", id, "signature", signature);
            }
        }
    }

    public List<Map<String, Object>> getUserListForComponent(Map<String, String> parameter) {
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "del_flag", "0"});

        String deptID = parameter.get("dept");
        if (deptID != null && !deptID.isBlank()) {
            conditions.add(new String[]{"equal", "dept_id", deptID});
        }

        String roleID = parameter.get("role");
        if (roleID != null && !roleID.isBlank()) {
            List<Map<String, Object>> userRoleList = genericMapper.getList(
                    TABLE_USER_ROLE,
                    "user_id",
                    List.<String[]>of(new String[]{"equal", "role_id", roleID}),
                    ""
            );
            if (userRoleList.isEmpty()) {
                return List.of();
            }
            List<String> userIDs = userRoleList.stream().map(user -> user.get("user_id").toString()).toList();
            if (!userIDs.isEmpty()) {
                conditions.add(new String[]{"in", "user_id", String.join(",", userIDs)});
            } else {
                return List.of();
            }
        }

        String positionID = parameter.get("position");
        if (positionID != null && !positionID.isBlank()) {
            List<Map<String, Object>> userPositionList = genericMapper.getList(
                    TABLE_USER_POSITION,
                    "user_id",
                    List.<String[]>of(new String[]{"equal", "post_id", positionID}),
                    ""
            );
            if (userPositionList.isEmpty()) {
                return List.of();
            }
            List<String> userIDs = userPositionList.stream().map(user -> user.get("user_id").toString()).toList();
            if (!userIDs.isEmpty()) {
                conditions.add(new String[]{"in", "user_id", String.join(",", userIDs)});
            } else {
                return List.of();
            }
        }

        String column = "user_id,dept_id,user_name,nick_name,email,phonenumber";
        List<Map<String, Object>> rows = genericMapper.getList(TABLE_USER, column, conditions, "order by user_id desc");

        List<Integer> userIDs = rows.stream()
                .map(row -> Integer.parseInt(row.get("user_id").toString()))
                .distinct()
                .toList();
        if (!userIDs.isEmpty()) {
            List<Map<String, Object>> userRoleList = genericMapper.getList(
                    TABLE_USER_ROLE,
                    "user_id, role_id",
                    List.<String[]>of(new String[]{"in", "user_id", String.join(",", userIDs.stream().map(String::valueOf).toList())}),
                    ""
            );
            if (userRoleList.isEmpty()) {
                for (Map<String, Object> row : rows) {
                    row.put("role_id", "");
                }
            } else {
                Map<Integer, Map<String, Object>> userRoleMap = userRoleList.stream()
                        .collect(Collectors.toMap(
                                userRole -> Integer.parseInt(userRole.get("user_id").toString()),
                                userRole -> Map.of("role_id", userRole.get("role_id"))
                        ));
                for (Map<String, Object> row : rows) {
                    Integer userId = Integer.parseInt(row.get("user_id").toString());
                    if (userRoleMap.containsKey(userId)) {
                        row.put("role_id", userRoleMap.get(userId).get("role_id").toString());
                    } else {
                        row.put("role_id", "");
                    }
                }
            }

            List<Map<String, Object>> userPositionList = genericMapper.getList(
                    TABLE_USER_POSITION,
                    "user_id, post_id",
                    List.<String[]>of(new String[]{"in", "user_id", String.join(",", userIDs.stream().map(String::valueOf).toList())}),
                    ""
            );
            if (userPositionList.isEmpty()) {
                for (Map<String, Object> row : rows) {
                    row.put("post_id", "");
                }
            } else {
                Map<Integer, Map<String, Object>> userPositionMap = userPositionList.stream()
                        .collect(Collectors.toMap(
                                userPosition -> Integer.parseInt(userPosition.get("user_id").toString()),
                                userPosition -> Map.of("post_id", userPosition.get("post_id"))
                        ));
                for (Map<String, Object> row : rows) {
                    Integer userId = Integer.parseInt(row.get("user_id").toString());
                    if (userPositionMap.containsKey(userId)) {
                        row.put("post_id", userPositionMap.get(userId).get("post_id").toString());
                    } else {
                        row.put("post_id", "");
                    }
                }
            }
        }

        return Number2String.number2String4ListMap.apply(rows);
    }

    public Map<String, Object> getUserList(Map<String, Object> parameters) {
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "del_flag", "0"});

        String deptID = (String) parameters.get("dept");
        if (deptID != null && !deptID.isBlank()) {
            conditions.add(new String[]{"equal", "dept_id", deptID});
        }

        String roleID = (String) parameters.get("role");
        if (roleID != null && !roleID.isBlank()) {
            List<Map<String, Object>> userRoleList = genericMapper.getList(
                    TABLE_USER_ROLE,
                    "user_id",
                    List.<String[]>of(new String[]{"equal", "role_id", roleID}),
                    ""
            );
            if (userRoleList.isEmpty()) {
                return Map.of("total", 0, "rows", List.of());
            }
            List<String> userIDs = userRoleList.stream().map(user -> user.get("user_id").toString()).toList();
            if (!userIDs.isEmpty()) {
                conditions.add(new String[]{"in", "user_id", String.join(",", userIDs)});
            } else {
                return Map.of("total", 0, "rows", List.of());
            }
        }

        String positionID = (String) parameters.get("position");
        if (positionID != null && !positionID.isBlank()) {
            List<Map<String, Object>> userPositionList = genericMapper.getList(
                    TABLE_USER_POSITION,
                    "user_id",
                    List.<String[]>of(new String[]{"equal", "post_id", positionID}),
                    ""
            );
            if (userPositionList.isEmpty()) {
                return Map.of("total", 0, "rows", List.of());
            }
            List<String> userIDs = userPositionList.stream().map(user -> user.get("user_id").toString()).toList();
            if (!userIDs.isEmpty()) {
                conditions.add(new String[]{"in", "user_id", String.join(",", userIDs)});
            } else {
                return Map.of("total", 0, "rows", List.of());
            }
        }

        if (parameters.get("name") != null && !((String) parameters.get("name")).isBlank()) {
            conditions.add(new String[]{"like", "user_name", (String) parameters.get("name")});
        }

        List<Map<String, Object>> totalResult = genericMapper.getList(
                TABLE_USER,
                "count(*) as qty",
                conditions,
                ""
        );
        long total = totalResult.isEmpty() ? 0 : (long) totalResult.get(0).get("qty");
        if (total == 0) {
            return Map.of("total", 0, "rows", List.of());
        }

        String skip = (String) parameters.getOrDefault("skip", "0");
        String take = (String) parameters.getOrDefault("take", "10");

        List<Map<String, Object>> rows = genericMapper.getList(TABLE_USER, "*", conditions, "order by user_id desc limit " + skip + "," + take);
        for (Map<String, Object> row : rows) {
            row.remove("password");
            row.remove("json_doc");
        }

        List<Integer> userIDs = rows.stream()
                .map(row -> Integer.parseInt(row.get("user_id").toString()))
                .distinct()
                .toList();
        if (!userIDs.isEmpty()) {
            List<Map<String, Object>> userRoleList = genericMapper.getList(
                    TABLE_USER_ROLE,
                    "user_id, role_id",
                    List.<String[]>of(new String[]{"in", "user_id", String.join(",", userIDs.stream().map(String::valueOf).toList())}),
                    ""
            );
            if (userRoleList.isEmpty()) {
                for (Map<String, Object> row : rows) {
                    row.put("role_id", "");
                }
            } else {
                Map<Integer, Map<String, Object>> userRoleMap = userRoleList.stream()
                        .collect(Collectors.toMap(
                                userRole -> Integer.parseInt(userRole.get("user_id").toString()),
                                userRole -> Map.of("role_id", userRole.get("role_id"))
                        ));
                for (Map<String, Object> row : rows) {
                    Integer userId = Integer.parseInt(row.get("user_id").toString());
                    if (userRoleMap.containsKey(userId)) {
                        row.put("role_id", userRoleMap.get(userId).get("role_id").toString());
                    } else {
                        row.put("role_id", "");
                    }
                }
            }

            List<Map<String, Object>> userPositionList = genericMapper.getList(
                    TABLE_USER_POSITION,
                    "user_id, post_id",
                    List.<String[]>of(new String[]{"in", "user_id", String.join(",", userIDs.stream().map(String::valueOf).toList())}),
                    ""
            );
            if (userPositionList.isEmpty()) {
                for (Map<String, Object> row : rows) {
                    row.put("post_id", "");
                }
            } else {
                Map<Integer, Map<String, Object>> userPositionMap = userPositionList.stream()
                        .collect(Collectors.toMap(
                                userPosition -> Integer.parseInt(userPosition.get("user_id").toString()),
                                userPosition -> Map.of("post_id", userPosition.get("post_id"))
                        ));
                for (Map<String, Object> row : rows) {
                    Integer userId = Integer.parseInt(row.get("user_id").toString());
                    if (userPositionMap.containsKey(userId)) {
                        row.put("post_id", userPositionMap.get(userId).get("post_id").toString());
                    } else {
                        row.put("post_id", "");
                    }
                }
            }
        }

        return Map.of(
                "total", total,
                "rows", Number2String.number2String4ListMap.apply(rows)
        );
    }

    public List<Map<String, Object>> getUserListByIDs(String ids) {
        List<Map<String, Object>> userList = genericMapper.getList(
                TABLE_USER,
                "*",
                List.<String[]>of(new String[]{"in", "user_id", ids}),
                ""
        );
        if (userList.isEmpty()) {
            return List.of();
        }
        return userList;
    }
}
