package com.ruoyi.core.service;

import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class StateService {
    public static final String TABLE_CONFIG = "data_status_config";
    private final GenericMapper genericMapper;

    public StateService(GenericMapper genericMapper) {
        this.genericMapper = genericMapper;
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param configCode 配置编码
     */
    public void removeStateConfig(String configCode) {
        List<String[]> condition = List.<String[]>of(
                new String[]{"equal", "auto_no", configCode}
        );
        this.genericMapper.delete(TABLE_CONFIG, condition);
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param data 变更数据
     */
    public void updateConfig(Map<String, Object> data) {
        Map<String, Object> condition = Map.of("auto_no", data.get("auto_no").toString());
        data.remove("auto_no");
        this.genericMapper.update(TABLE_CONFIG, data, condition);
    }

    public Map<String, Object> getStateConfig(String stateCode, String configCode) {
        List<Map<String, Object>> configList = this.genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.of(
                        new String[]{"equal", "source_code", stateCode},
                        new String[]{"equal", "auto_no", configCode}
                ),
                "limit 1"
        );
        if (configList.isEmpty()) {
            return Map.of();
        }
        return configList.get(0);
    }

    public List<Map<String, Object>> getStateConfigList(String stateCode) {
        List<Map<String, Object>> configList = this.genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.<String[]>of(new String[]{"equal", "source_code", stateCode}),
                ""
        );
        if (configList.isEmpty()) {
            return List.of();
        }
        return configList;
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param data 数据
     */
    public void createStateConfig(Map<String, Object> data) {
        List<Map<String, Object>> configList = this.genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.<String[]>of(
                        new String[]{"equal", "source_code", data.get("source_code").toString()},
                        new String[]{"equal", "status_type", data.get("status_type").toString()},
                        new String[]{"equal", "status_value", data.get("status_value").toString()}
                ),
                ""
        );
        if (!configList.isEmpty()) {
            throw new RuntimeException("状态配置已存在");
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            data.put("auto_no", IdUtil.getSnowflakeNextIdStr());
            data.put("status_config", objectMapper.writeValueAsString(data.get("status_config")));
            this.genericMapper.create(TABLE_CONFIG, data);
        } catch (Exception e) {
            throw new RuntimeException("状态配置创建失败");
        }
    }
}
