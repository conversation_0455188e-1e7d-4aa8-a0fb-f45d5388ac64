package com.ruoyi.core.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.enums.OtherMedicalOrderStatusEnum;
import com.ruoyi.core.mapper.GenericMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ruoyi.core.service.LogService.MODULE_OTHER_MEDICAL_ORDER;

@RequiredArgsConstructor
@Service
public class OtherMedicalOrderService {

    private final GenericMapper genericMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final LogService logService;

    /**
     * @description 新增其他医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object add(Map<String, Object> params) {
        long id = IdUtil.getSnowflakeNextId();
        String username = SecurityUtils.getUsername();
        Map<String, Object> map = new HashMap<>(params);
        map.put("id", id);
        map.put("create_by", username);
        map.put("status", 0); // 默认状态为已保存
        mergeContent(map);
        // 新增其他医嘱记录
        genericMapper.create("other_medical_order", map);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_OTHER_MEDICAL_ORDER, "新增其他医嘱", map, null);
        return AjaxResult.success("添加成功", String.valueOf(id));
    }

    /**
     * @description 处理医嘱项目内容拼接的内容
     * <AUTHOR>
     */
    private void mergeContent(Map<String, Object> map) {
        String itemType = map.get("item_type").toString();
        Object itemContent = map.get("item_content");
        JSONObject contentObj = JSONUtil.parseObj(itemContent);
        String selected = contentObj.get("selected").toString();
        StringBuilder stringBuilder = new StringBuilder();

        // 校验selected是否为空字符串
        if (StrUtil.isNotBlank(selected)) {
            try {
                List<Integer> dictValues = Arrays.stream(selected.split(","))
                        .map(String::trim)
                        .filter(StrUtil::isNotBlank) // 过滤空字符串
                        .map(Integer::valueOf)
                        .toList();

                // 只有当dictValues不为空时才查询字典
                if (!dictValues.isEmpty()) {
                    String sql = """
                            select dict_label,dict_value from sys_dict_data where dict_type = :dictType and dict_value in (:dictValues)
                            """;
                    Map<String, Object> params = new HashMap<>();
                    params.put("dictType", itemType);
                    params.put("dictValues", dictValues);
                    List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);
                    for (Map<String, Object> item : list) {
                        stringBuilder.append(item.get("dict_label")).append(",");
                    }
                }
            } catch (NumberFormatException e) {
                // 如果selected包含非数字内容，记录日志但不中断流程
                System.err.println("selected字段包含非数字内容: " + selected);
            }
        }

        // 处理其他字段
        contentObj.remove("selected");
        contentObj.forEach((k, v) -> {
            if (StrUtil.isNotBlank(v.toString())) {
                stringBuilder.append(v).append(",");
            }
        });

        // 移除最后一个逗号
        if (!stringBuilder.isEmpty()) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }

        map.put("merge_content", stringBuilder.toString());
    }

    /**
     * @description 修改其他医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object update(Long id, Map<String, Object> params) {
        String username = SecurityUtils.getUsername();
        Map<String, Object> updateValues = new HashMap<>(params);
        updateValues.put("update_by", username);

        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        condition.put("is_deleted", 0);
        mergeContent(updateValues);
        int result = genericMapper.update("other_medical_order", updateValues, condition);
        if (result > 0) {
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_OTHER_MEDICAL_ORDER, "修改其他医嘱", params, null);
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.error("修改失败，记录不存在或已被删除");
        }
    }

    /**
     * @description 删除其他医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object delete(Long id) {
        String username = SecurityUtils.getUsername();
        Map<String, Object> updateValues = new HashMap<>();
        updateValues.put("is_deleted", 1);
        updateValues.put("update_by", username);

        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        condition.put("is_deleted", 0);

        int result = genericMapper.update("other_medical_order", updateValues, condition);
        if (result > 0) {
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_OTHER_MEDICAL_ORDER, "删除其他医嘱", Map.of("id", id), null);
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.error("删除失败，记录不存在或已被删除");
        }
    }

    /**
     * @description 根据medical_record_id查看其他医嘱详情
     * <AUTHOR>
     */
    public Object info(String patientId) {
        String sql = selectAllSql() + " WHERE medical_record_id = :medical_record_id AND is_deleted = 0 LIMIT 1";
        Map<String, Object> params = new HashMap<>();
        params.put("medical_record_id", patientId);

        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);

            // 添加 status_label 字段
            Object statusObj = result.get("status");
            if (statusObj != null) {
                int status = Integer.parseInt(statusObj.toString());
                result.put("status_label", OtherMedicalOrderStatusEnum.getValueByKey(status));
            } else {
                result.put("status_label", "未知状态");
            }

            // 添加 item_type_label 字段
            Object itemTypeObj = result.get("item_type");
            if (itemTypeObj != null) {
                String itemType = itemTypeObj.toString();
                String dictSql = """
                        select dict_name from sys_dict_type where dict_type = :dictType
                        """;
                try {
                    String itemTypeLabel = namedParameterJdbcTemplate.queryForObject(dictSql, Map.of("dictType", itemType), String.class);
                    result.put("item_type_label", itemTypeLabel != null ? itemTypeLabel : "未知类型");
                } catch (Exception e) {
                    result.put("item_type_label", "未知类型");
                }
            } else {
                result.put("item_type_label", "未知类型");
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            // 没有记录时返回空的JSON对象
            return AjaxResult.success(new HashMap<>());
        }
    }

    /**
     * @description 根据ID获取其他医嘱详情
     * <AUTHOR>
     */
    public Object getDetailById(Long id) {
        if (id == null) {
            return AjaxResult.error("ID不能为空");
        }

        String sql = selectAllSql() + " WHERE id = :id AND is_deleted = 0";
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);

        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);

            // 添加 status_label 字段
            Object statusObj = result.get("status");
            if (statusObj != null) {
                int status = Integer.parseInt(statusObj.toString());
                result.put("status_label", OtherMedicalOrderStatusEnum.getValueByKey(status));
            } else {
                result.put("status_label", "未知状态");
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            // 没有记录时返回错误信息
            return AjaxResult.error("未找到对应的其他医嘱记录");
        }
    }

    /**
     * @description 查询所有字段的SQL
     * <AUTHOR>
     */
    private String selectAllSql() {
        return """
                SELECT
                    CAST(id AS CHAR) AS id,
                    medical_record_id,
                    prescription_number,
                    prescription_date,
                    status,
                    item_type,
                    item_content,
                    order_type,
                    order_date,
                    order_time,
                    doctor_name,
                    nurse_name,
                    merge_content,
                    remark,
                    is_deleted,
                    create_time,
                    create_by,
                    update_time,
                    update_by
                FROM other_medical_order
                """;
    }

    /**
     * @description 获取其他医嘱项目类型
     * <AUTHOR>
     */
    public Object getItemType() {
        String sql = """
                select dict_name as label,dict_type as value from sys_dict_type where dict_type in
                ('diet','nursing_level','prevention','drug_allergy','vital_signs','urological_orders','other')
                order by create_time desc
                """;
        return AjaxResult.success(namedParameterJdbcTemplate.queryForList(sql, new HashMap<>()));
    }

    /**
     * @description 根据medical_record_id获取其他医嘱列表
     * <AUTHOR>
     */
    public Object getListByMedicalRecordId(String medicalRecordId) {
        String sql = selectAllSql() + " WHERE medical_record_id = :medical_record_id AND is_deleted = 0 ORDER BY create_time DESC";
        Map<String, Object> params = new HashMap<>();
        params.put("medical_record_id", medicalRecordId);

        try {
            List<Map<String, Object>> resultList = namedParameterJdbcTemplate.queryForList(sql, params);

            // 为每条记录添加 status_label 和 item_type_label 字段
            for (Map<String, Object> record : resultList) {
                // 添加 status_label 字段
                Object statusObj = record.get("status");
                if (statusObj != null) {
                    int status = Integer.parseInt(statusObj.toString());
                    record.put("status_label", OtherMedicalOrderStatusEnum.getValueByKey(status));
                } else {
                    record.put("status_label", "未知状态");
                }

                // 添加 item_type_label 字段
                Object itemTypeObj = record.get("item_type");
                if (itemTypeObj != null) {
                    String itemType = itemTypeObj.toString();
                    String dictSql = """
                            select dict_name from sys_dict_type where dict_type = :dictType
                            """;
                    try {
                        String itemTypeLabel = namedParameterJdbcTemplate.queryForObject(dictSql, Map.of("dictType", itemType), String.class);
                        record.put("item_type_label", itemTypeLabel != null ? itemTypeLabel : "未知类型");
                    } catch (Exception e) {
                        record.put("item_type_label", "未知类型");
                    }
                } else {
                    record.put("item_type_label", "未知类型");
                }
            }

            return AjaxResult.success(resultList);
        } catch (Exception e) {
            // 查询异常时返回空列表
            return AjaxResult.success(List.of());
        }
    }

    /**
     * @description 获取其他医嘱处方信息（处方号和处方日期）
     * <AUTHOR>
     */
    public Object getPrescriptionInfo(String patientId) {
        String sql = """
                SELECT
                    prescription_number,
                    prescription_date,
                    status
                FROM
                    `other_medical_order`
                WHERE
                    medical_record_id = :patientId
                    AND is_deleted = 0
                LIMIT 1
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("patientId", patientId);

        // 空结果默认值
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("prescription_number", "");
        emptyResult.put("prescription_date", "");
        emptyResult.put("flag", false);
        emptyResult.put("cancelFlag", false);

        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);
            if (result.get("status").toString().equals("0") || result.get("status").toString().equals("4")) {
                return AjaxResult.success(emptyResult);
            }

            // 处理处方日期格式
            Object prescriptionDateObj = result.get("prescription_date");
            if (prescriptionDateObj != null) {
                String prescriptionDate = prescriptionDateObj.toString();
                result.replace("prescription_date", prescriptionDate);
            }

            // true为可以取消开方
            result.put("flag", true);
            if (result.get("status").toString().equals("1")) {
                result.put("cancelFlag", true);
            } else {
                result.put("cancelFlag", false);
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.success(emptyResult);
        }
    }

    /**
     * @description 其他医嘱开方
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object prescribe(Long id) {
        if (id == null) {
            return AjaxResult.error("请选择需要开方的医嘱");
        }

        // 生成处方号和处方日期
        long prescriptionNumber = cn.hutool.core.util.RandomUtil.randomLong(100000, 999999);
        String prescriptionDate = DateUtil.now();

        // 查询需要开方的医嘱
        String queryOrdersSql = """
                SELECT id
                FROM `other_medical_order`
                WHERE id = :id
                AND is_deleted = 0
                AND (status = 0 OR status = 4)
                """;

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("id", id);

        List<Map<String, Object>> orders;
        try {
            orders = namedParameterJdbcTemplate.queryForList(queryOrdersSql, queryParams);
        } catch (Exception e) {
            return AjaxResult.error("查询医嘱信息失败");
        }

        if (orders.isEmpty()) {
            return AjaxResult.error("未找到可开方的医嘱记录");
        }

        // 更新医嘱状态
        String updateOrderSql = """
                UPDATE `other_medical_order`
                SET status = 1,
                    prescription_number = :prescription_number,
                    prescription_date = :prescription_date,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id = :id
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("prescription_number", prescriptionNumber);
        updateParams.put("prescription_date", prescriptionDate);
        updateParams.put("update_by", SecurityUtils.getUsername());
        updateParams.put("id", id);

        namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        // 开方成功后，插入doctor_order记录
        insertDoctorOrderOnPrescribe(id, SecurityUtils.getUsername());

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_OTHER_MEDICAL_ORDER, "其他医嘱开方", id, null);
        return AjaxResult.success("开方成功");
    }

    /**
     * @description 其他医嘱取消开方
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object cancelPrescription(Long id) {
        if (id == null) {
            return AjaxResult.error("请选择需要取消开方的医嘱");
        }

        // 查询需要取消开方的医嘱
        String queryOrdersSql = """
                SELECT id
                FROM `other_medical_order`
                WHERE id = :id
                AND is_deleted = 0
                AND status = 1
                """;

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("id", id);

        List<Map<String, Object>> orders;
        try {
            orders = namedParameterJdbcTemplate.queryForList(queryOrdersSql, queryParams);
        } catch (Exception e) {
            return AjaxResult.error("查询医嘱信息失败");
        }

        if (orders.isEmpty()) {
            return AjaxResult.error("未找到已开方的医嘱记录");
        }

        // 更新医嘱状态（其他医嘱取消开方状态为4）
        String updateOrderSql = """
                UPDATE `other_medical_order`
                SET status = 4,
                    prescription_number = NULL,
                    prescription_date = NULL,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id = :id
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("update_by", SecurityUtils.getUsername());
        updateParams.put("id", id);

        namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        // 删除doctor_order表中的记录
        deleteDoctorOrder(id);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_OTHER_MEDICAL_ORDER, "其他医嘱取消开方", id, null);
        return AjaxResult.success("取消开方成功");
    }

    /**
     * @param otherMedicalOrderId 其他医嘱ID
     * @param username            用户名
     * @description 开方时插入医嘱表记录
     */
    private void insertDoctorOrderOnPrescribe(Long otherMedicalOrderId, String username) {
        try {
            // 查询other_medical_order的完整数据
            String querySql = """
                    SELECT * FROM `other_medical_order`
                    WHERE id = :id AND is_deleted = 0
                    """;
            Map<String, Object> otherMedicalOrderData;
            try {
                otherMedicalOrderData = namedParameterJdbcTemplate.queryForMap(
                        querySql, Map.of("id", otherMedicalOrderId));
            } catch (Exception e) {
                return;
            }

            // 获取患者信息
            String medicalRecordId = otherMedicalOrderData.get("medical_record_id").toString();
            Map<String, Object> patientInfo = getPatientInfo(medicalRecordId);

            // 生成医嘱详细内容（merge_content + remark）
            String content = generateOrderContent(otherMedicalOrderData);

            String dictSql = """
                    select dict_name from sys_dict_type where dict_type = :dictType
                    """;
            String itemType;
            try {
                itemType = namedParameterJdbcTemplate.queryForObject(dictSql, Map.of("dictType", otherMedicalOrderData.get("item_type")), String.class);
            } catch (Exception e) {
                itemType = "";
            }

            // 构建doctor_order数据
            Map<String, Object> doctorOrderData = new HashMap<>();
            doctorOrderData.put("id", IdUtil.getSnowflakeNextId());
            doctorOrderData.put("medication_order_id", otherMedicalOrderId);
            doctorOrderData.put("area", patientInfo.get("quy"));
            doctorOrderData.put("bed_number", patientInfo.get("chuangw_no"));
            doctorOrderData.put("hospitalization_id", patientInfo.get("zhuyuanh"));
            doctorOrderData.put("patient_name", patientInfo.get("xingming"));
            doctorOrderData.put("order_time", DateUtil.now());
            doctorOrderData.put("doctor_name", username);
            doctorOrderData.put("order_name", "其他医嘱");
            doctorOrderData.put("order_type", otherMedicalOrderData.get("order_type"));
            doctorOrderData.put("other_order_type", itemType);
            doctorOrderData.put("content", content);
            doctorOrderData.put("created_by", username);
            doctorOrderData.put("is_deleted", 0);
            doctorOrderData.put("status", 0);
            if ("长期".equals(String.valueOf(otherMedicalOrderData.get("order_type")))) {
                doctorOrderData.put("is_stopped", 0);
            }

            genericMapper.create("doctor_order", doctorOrderData);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("开方时插入医嘱表失败: " + e.getMessage());
        }
    }

    /**
     * @param otherMedicalOrderId 其他医嘱ID
     * @description 删除医嘱表记录
     */
    private void deleteDoctorOrder(Long otherMedicalOrderId) {
        try {
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("is_deleted", 1);

            Map<String, Object> condition = new HashMap<>();
            condition.put("medication_order_id", otherMedicalOrderId);

            genericMapper.update("doctor_order", updateData, condition);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("删除医嘱表记录失败: " + e.getMessage());
        }
    }

    /**
     * @param medicalRecordId 门诊病历ID
     * @return 患者信息
     * @description 获取患者信息
     */
    private Map<String, Object> getPatientInfo(String medicalRecordId) {
        String sql = """
                SELECT quy, chuangw_no, zhuyuanh, xingming
                FROM data_ext_menzbl
                WHERE leaf_id = :medicalRecordId
                """;
        try {
            return namedParameterJdbcTemplate.queryForMap(sql, Map.of("medicalRecordId", medicalRecordId));
        } catch (Exception e) {
            // 返回默认值
            Map<String, Object> defaultInfo = new HashMap<>();
            defaultInfo.put("quy", "");
            defaultInfo.put("chuangw_no", "");
            defaultInfo.put("zhuyuanh", "");
            defaultInfo.put("xingming", "");
            return defaultInfo;
        }
    }

    /**
     * @param otherMedicalOrderData 其他医嘱数据
     * @return 医嘱详细内容
     * @description 生成其他医嘱详细内容（merge_content + remark）
     */
    private String generateOrderContent(Map<String, Object> otherMedicalOrderData) {
        String mergeContent = getStringValue(otherMedicalOrderData, "merge_content");
        String remark = getStringValue(otherMedicalOrderData, "remark");

        StringBuilder content = new StringBuilder();

        if (StrUtil.isNotBlank(mergeContent)) {
            content.append(mergeContent);
        }

        if (StrUtil.isNotBlank(remark)) {
            if (!content.isEmpty()) {
                content.append(" ");
            }
            content.append(remark);
        }

        return content.toString();
    }

    /**
     * @param data 数据Map
     * @param key  键名
     * @return 字符串值
     * @description 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : "";
    }
}
