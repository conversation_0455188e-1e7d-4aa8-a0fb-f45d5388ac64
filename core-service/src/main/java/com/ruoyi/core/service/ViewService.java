package com.ruoyi.core.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.entity.PsoViewDict;
import com.ruoyi.core.mapper.FormMapper;
import com.ruoyi.core.mapper.GenericMapper;
import com.ruoyi.core.mapper.PsoViewDictMapper;
import com.ruoyi.core.repository.ViewRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.ruoyi.core.constants.PsoConstant.*;
import static com.ruoyi.core.service.LogService.MODULE_VIEW;

@Slf4j
@Service
public class ViewService {
    final String TABLE_FORM = "node_ext_data";
    final String TABLE_VIEW = "node_ext_view";
    public static final String TABLE_CONFIG = "data_view_config";
    private final ViewRepository viewRepository;
    private final GenericMapper genericMapper;
    private final FormMapper formMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final LogService logService;
    private final PsoViewDictMapper psoViewDictMapper;
    private final ScriptService scriptService;

    public ViewService(ViewRepository viewRepository, GenericMapper genericMapper,
                       FormMapper formMapper,PsoViewDictMapper psoViewDictMapper,ScriptService scriptService, NamedParameterJdbcTemplate namedParameterJdbcTemplate, LogService logService) {
        this.viewRepository = viewRepository;
        this.genericMapper = genericMapper;
        this.formMapper = formMapper;
        this.psoViewDictMapper=psoViewDictMapper;
        this.scriptService=scriptService;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.logService = logService;
    }

    /**
     * todo 记录log
     *
     * @param configCode 配置code
     */
    public void removeViewConfig(String configCode) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_CONFIG,
                "source_code",
                List.<String[]>of(new String[]{"equal", "auto_no", configCode}),
                "limit 1"
        );
        if (configList.isEmpty()) {
            throw new RuntimeException("未找到视图配置");
        }
        genericMapper.delete(TABLE_CONFIG, List.<String[]>of(new String[]{"equal", "auto_no", configCode}));
    }

    public void copyViewConfig(String configCode) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.<String[]>of(new String[]{"equal", "auto_no", configCode}),
                "limit 1"
        );
        if (configList.isEmpty()) {
            throw new RuntimeException("未找到视图配置");
        }
        Map<String, Object> config = configList.get(0);
        config.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        config.put("view_name", config.get("view_name") + "【复制】");
        genericMapper.create(TABLE_CONFIG, config);
    }

    public void updateViewConfig(Map<String, Object> body) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_CONFIG,
                "source_code",
                List.<String[]>of(new String[]{"equal", "auto_no", body.get("auto_no").toString()}),
                "limit 1"
        );
        if (configList.isEmpty()) {
            throw new RuntimeException("未找到视图配置");
        }
        Map<String, Object> condition = Map.of("auto_no", body.get("auto_no").toString());
        body.remove("auto_no");
        genericMapper.update(TABLE_CONFIG, body, condition);
    }

    public Map<String, Object> getViewConfig(String viewCode, String configCode) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.of(
                        new String[]{"equal", "source_code", viewCode},
                        new String[]{"equal", "auto_no", configCode}),
                "limit 1"
        );
        if (configList.isEmpty()) {
            throw new RuntimeException("未找到视图配置");
        }
        return configList.get(0);
    }

    public List<Map<String, Object>> getViewConfigListByView(String formCode) {
        return viewRepository.getViewConfigListByView(formCode);
    }

    /**
     * todo 记录log
     * todo 清除redis缓存
     *
     * @param body 数据
     */
    public void createConfig(Map<String, Object> body) {
        List<String[]> condition = List.of(
                new String[]{"equal", "source_code", body.get("source_code").toString()},
                new String[]{"equal", "source_type", "0"},
                new String[]{"equal", "view_name", body.get("view_name").toString()}
        );
        List<Map<String, Object>> configList = genericMapper.getList(TABLE_CONFIG, "*", condition, "limit 1");
        if (!configList.isEmpty()) {
            throw new RuntimeException("视图配置已存在");
        }
        List<Map<String, Object>> viewList = genericMapper.getList(
                TABLE_FORM,
                "data_code",
                List.<String[]>of(new String[]{"equal", "data_code", body.get("source_code").toString()}),
                "limit 1"
        );
        if (viewList.isEmpty()) {
            throw new RuntimeException("表单不存在");
        }
        body.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        body.put("app_id", "");
        genericMapper.create(TABLE_CONFIG, body);
    }

    public List<Map<String, Object>> viewList(String viewCode, String viewSName, String state) {
        return formMapper.viewList(APP_ID, viewCode, viewSName, state);
    }

    /**
     * @description 创建视图
     * <AUTHOR>
     */
    public Object addView(Map<String, Object> params) {
        // 生成视图编码
        String viewCode = IdUtil.getSnowflakeNextIdStr();
        String username = SecurityUtils.getUsername();

        // 创建视图信息
        Map<String, Object> viewData = new HashMap<>();
        viewData.put("app_id", APP_ID);
        viewData.put("view_code", viewCode);
        viewData.put("view_name", params.get("view_name"));
        viewData.put("view_status", 0);
        viewData.put("create_time", DateUtil.now());
        viewData.put("creator", username);
        viewData.put("upuser", "");
        viewData.put("input_time", "");
        viewData.put("is_entity", params.getOrDefault("is_entity", 0));
        viewData.put("is_share", params.getOrDefault("is_share", 0));
        // 根据query_type设置is_sys和is_share
        viewData.put("is_sys", params.getOrDefault("is_sys", 0));
        viewData.put("data_state", JSONUtil.createObj().set("status", "启用").toString());
        viewData.put("company_id", params.getOrDefault("company_id", ""));

        // 如果没有提供view_sname，使用view_name
        if (!params.containsKey("view_sname") || null == params.get("view_sname") || params.get("view_sname").toString().isEmpty()) {
            viewData.put("view_sname", params.get("view_name"));
        } else {
            viewData.put("view_sname", params.get("view_sname"));
        }

        // 添加视图设计信息
        if (params.containsKey("view_design")) {
            viewData.put("view_design", params.get("view_design"));
        }

        // 添加驱动ID
//        if (params.containsKey("driver_id")) {
//            viewData.put("driver_id", params.get("driver_id"));
//        }
        viewData.put("driver_id", viewCode);
        // 插入视图信息
        genericMapper.create(TABLE_VIEW, viewData);

        // 如果提供了node_id，添加节点数据关联
        if (params.containsKey("node_id") && null != params.get("node_id") && !params.get("node_id").toString().isEmpty()) {
            Map<String, Object> nodeData = new HashMap<>();
            nodeData.put("node_id", params.get("node_id"));
            nodeData.put("node_data_tag", TABLE_VIEW);
            nodeData.put("node_data_id", viewCode);
            nodeData.put("data_order", DEFAULT_ORDER_VALUE);
            nodeData.put("struct_type", "tree");
            genericMapper.create("PSO_NODE_DATA", nodeData);
        }
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_VIEW, "创建视图", viewData, null);
        return viewCode;
    }

    public Object update(String viewCode, Map<String, Object> params) {
        Map<String, Object> viewData = new HashMap<>();
        String username = SecurityUtils.getUsername();
        viewData.put("view_name", params.get("view_name"));
        viewData.put("view_sname", params.getOrDefault("view_sname", params.get("view_name")));
        viewData.put("is_entity", params.get("is_entity"));
        viewData.put("input_time", DateUtil.now());
        viewData.put("upuser", username);
        genericMapper.update(TABLE_VIEW, params, Map.of("view_code", viewCode));
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_VIEW, "更新视图", params, viewCode);
        return 1;
    }

    /**
     * @description 发布视图
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object publish(Map<String, Object> params) {
        // 生成视图编码
//        String username = SecurityUtils.getUsername();
        String code=params.get("view_code").toString();
        Map<String,Object> viewMap= (Map<String, Object>) getView(code);
        JSONObject viewObj=JSONUtil.parseObj(viewMap.get("view_design"));
        JSONObject configObj=viewObj.getJSONObject("queryWgtMap");
        if (configObj.isEmpty()) return "视图配置不存在";
        namedParameterJdbcTemplate.update(
                "delete from PSO_VIEW_DICT where view_code=:code",
                Map.of(
                        "code", code
                )
        );
        for (String dataKey : configObj.keySet()) {
            JSONObject dataObj = configObj.getJSONObject(dataKey);
            if (!dataObj.containsKey("fields") || dataObj.getJSONArray("fields").isEmpty()) {
                continue;
            }
            JSONArray tableFields = dataObj.getJSONArray("fields");
            // 插入字典表
            for (int num = 0; num < tableFields.size(); num++) {
                JSONObject fieldObj = tableFields.getJSONObject(num);
                PsoViewDict dict = new PsoViewDict();
                dict.setCode(code).setScriptOut(dataKey).setField(fieldObj.getStr("field")).setFieldDisplay(fieldObj.getStr("field_display"))
                        .setBindDimen("").setBindTag("").setFieldFormat(fieldObj.getStr("field_format")).setFieldConfig(fieldObj.toString());
                psoViewDictMapper.insert(dict);
            }
        }
        //
        int isEntity=Integer.parseInt(viewMap.get("is_entity").toString());
        if (isEntity==1){
            int isPub=Integer.parseInt(viewMap.get("view_status").toString());
            MapSqlParameterSource sqlParams = new MapSqlParameterSource();
            if (isPub==1){
                //删除视图
                try {
                    namedParameterJdbcTemplate.update(String.format("drop view view_ext_%s",code),sqlParams);
                }catch (Exception ex){
                    log.info("删除实体视图时失败："+ex.getMessage());
                }
            }
            String driverId=viewMap.get("driver_id").toString();
            namedParameterJdbcTemplate.update(
                    String.format(" create view view_ext_%s as %s",code,scriptService.GetScriptSql(driverId)),
                    sqlParams
            );
        }
        //发布状态
        namedParameterJdbcTemplate.update(
                "update NODE_EXT_VIEW set view_status=1 where view_code=:code",
                Map.of(
                        "code", code
                )
        );
        //TODO 渲染
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_VIEW, "发布视图", params, null);
        return code;
    }

    /**
     * @description 启用/停用视图
     * <AUTHOR>
     */
    public Object deactivate(List<String> viewCodes, Map<String, Object> params) {
        if (Objects.isNull(params.get("status")) || StrUtil.isBlank(params.get("status").toString())) {
            throw new RuntimeException("状态不可为空");
        }
        String status = params.get("status").toString();
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, Object> condition = new HashMap<>();
        JSONObject obj = JSONUtil.createObj();
        if (CollectionUtil.isNotEmpty(viewCodes)) {
            int total = 0;
            if (Objects.equals(status, ENABLE)) {
                for (String viewCode : viewCodes) {
                    dataMap.put("data_state", obj.set("status", ENABLE).toString());
                    condition.put("view_code", viewCode);
                    int update = genericMapper.update(TABLE_VIEW, dataMap, condition);
                    total += update;
                    logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_VIEW, "启用视图", dataMap, viewCode);
                }
                return total;
            }
            if (Objects.equals(status, DISABLE)) {
                String now = DateUtil.now();
                for (String viewCode : viewCodes) {
                    dataMap.put("data_state", obj
                            .set("status", DISABLE)
                            .set("deleted_time", now)
                            .toString());
                    condition.put("view_code", viewCode);
                    int update = genericMapper.update(TABLE_VIEW, dataMap, condition);
                    total += update;
                    logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_VIEW, "停用视图", dataMap, viewCode);
                }
                return total;
            }
            throw new RuntimeException("状态错误");
        }
        return 0;
    }

    /**
     * @description 视图详情
     * <AUTHOR>
     */
    public Object getView(String viewCode) {
        return namedParameterJdbcTemplate.queryForMap(
                "select * from node_ext_view where view_code = :view_code",
                Map.of("view_code", viewCode));
    }

    /**
     * @description 复制视图
     * <AUTHOR>
     */
    public String copyView(Map<String, Object> params) {
        // 生成新的ID
        String newViewCode = IdUtil.getSnowflakeNextIdStr();
        String username = SecurityUtils.getUsername();
        String viewCode = params.get("node_id").toString();
        String nodePid = params.get("node_pid").toString();

        try {
            // 查找原视图
            Map<String, Object> originView = namedParameterJdbcTemplate.queryForMap(
                    "select * from NODE_EXT_VIEW where view_code = :code",
                    Map.of("code", viewCode));

            // 查找原视图关联的驱动
            List<Map<String, Object>> driverList = namedParameterJdbcTemplate.queryForList(
                    "select * from pso_script_driver where driver_id in (select driver_id from node_ext_view where view_code = :code)",
                    Map.of("code", viewCode));

            // 创建新视图
            Map<String, Object> newView = new HashMap<>();
            newView.put("app_id", originView.get("app_id"));
            newView.put("view_code", newViewCode);
            newView.put("view_name", originView.get("view_name") + "【复制】");
            newView.put("view_sname", originView.get("view_sname") + "【复制】");
            newView.put("view_status", 0);
            newView.put("view_design", originView.get("view_design"));
            newView.put("driver_id", newViewCode);
            newView.put("creator", username);
            newView.put("create_time", DateUtil.now());
            newView.put("is_share", 0);
            newView.put("is_entity", originView.get("is_entity"));
            newView.put("data_state", originView.get("data_state").toString());
            newView.put("company_id", originView.getOrDefault("company_id", ""));

            // 插入新视图
            genericMapper.create(TABLE_VIEW, newView);

            // 处理节点绑定
            Map<String, Object> nodeData = new HashMap<>();
            nodeData.put("node_id", nodePid);
            nodeData.put("node_data_tag", TABLE_VIEW);
            nodeData.put("node_data_id", newViewCode);
            nodeData.put("data_order", 255);
            nodeData.put("struct_type", "tree");
            nodeData.put("node_data_top", 0);
            nodeData.put("node_data_relate", 0);
            genericMapper.create("PSO_NODE_DATA", nodeData);

            // 如果原视图有关联的驱动，也复制驱动
            if (!driverList.isEmpty()) {
                Map<String, Object> originalDriver = driverList.get(0);
                Map<String, Object> newDriver = new HashMap<>();
                newDriver.put("driver_id", newViewCode);
                newDriver.put("driver_name", originalDriver.get("driver_name") + "【复制】");
                newDriver.put("driver_status", 0);
                newDriver.put("driver_config", originalDriver.get("driver_config"));
                newDriver.put("creator", username);
                newDriver.put("create_time", DateUtil.now());
                newDriver.put("app_id", originalDriver.get("app_id"));
                newDriver.put("source_type", 3);
                newDriver.put("source_code", newViewCode);

                genericMapper.create("pso_script_driver", newDriver);

                // 绑定驱动到view_config节点
                Map<String, Object> bindData = new HashMap<>();
                bindData.put("node_id", "view_config");
                bindData.put("node_data_tag", "pso_script_driver");
                bindData.put("node_data_id", newViewCode);
                bindData.put("data_order", 255);
                bindData.put("struct_type", "nav");
                genericMapper.create("pso_node_data", bindData);
            }

            // 记录日志
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_VIEW, "复制视图", newView, viewCode);
            return newViewCode;
        } catch (Exception e) {
            log.error("复制视图失败: {}", e.getMessage());
            throw new RuntimeException("复制视图失败", e);
        }
    }
}
