package com.ruoyi.core.service;

import cn.hutool.core.util.IdUtil;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class RuleService {
    public static final String TABLE_CONFIG = "data_rule_config";
    private final GenericMapper genericMapper;

    public RuleService(GenericMapper genericMapper) {
        this.genericMapper = genericMapper;
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param configCode 配置编码
     */
    public void removeRuleConfig(String configCode) {
        List<String[]> condition = List.<String[]>of(
                new String[]{"equal", "auto_no", configCode}
        );
        genericMapper.delete(TABLE_CONFIG, condition);
    }

    /**
     * todo 记录log
     *
     * @param configCode 配置编码
     */
    public void copyRuleConfig(String configCode) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.<String[]>of(new String[]{"equal", "auto_no", configCode}),
                null
        );
        if (configList.isEmpty()) {
            throw new RuntimeException("规则不存在");
        }
        Map<String, Object> data = configList.get(0);
        data.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        data.put("rule_name", data.get("rule_name").toString() + "_副本");
        genericMapper.create(TABLE_CONFIG, data);
    }

    /**
     * todo 记录log
     *
     * @param data 数据
     */
    public void updateRuleConfig(Map<String, Object> data) {
        Map<String, Object> condition = Map.of("auto_no", data.get("auto_no").toString());
        data.remove("auto_no");
        genericMapper.update(TABLE_CONFIG, data, condition);
    }

    public Map<String, Object> getRuleConfig(String code, String configCode) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.of(
                        new String[]{"equal", "source_code", code},
                        new String[]{"equal", "auto_no", configCode}),
                "limit 1"
        );
        if (configList.isEmpty()) {
            return Map.of();
        }
        return configList.get(0);
    }

    public List<Map<String, Object>> getRuleConfigList(String ruleCode) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.of(
                        new String[]{"equal", "source_code", ruleCode},
                        new String[]{"equal", "rule_type", "1"}),
                ""
        );
        if (configList.isEmpty()) {
            return List.of();
        }
        return configList;
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param data 数据
     */
    public void createRuleConfig(Map<String, Object> data) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.of(
                        new String[]{"equal", "source_code", data.get("source_code").toString()},
                        new String[]{"equal", "rule_name", data.get("rule_name").toString()},
                        new String[]{"equal", "rule_type", data.get("rule_type").toString()}),
                null
        );
        if (!configList.isEmpty()) {
            throw new RuntimeException("规则已存在");
        }
        data.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        genericMapper.create(TABLE_CONFIG, data);
    }
}
