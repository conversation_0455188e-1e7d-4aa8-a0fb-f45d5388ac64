package com.ruoyi.core.service;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;

@Service
public class DynamicPdfTableGenerator {

    private static final float MARGIN = 50;
    private static final float PAGE_HEIGHT = PDRectangle.A4.getHeight();
    private static final float CONTENT_START_Y = PAGE_HEIGHT - MARGIN;
    private static final float BOTTOM_MARGIN = MARGIN;

    public void generatePdf(String outputPath, List<String[]> data, String signatureImagePath,
                            float[] columnWidths, String[] tableHeaders) throws IOException {
        try (PDDocument document = new PDDocument()) {
            PDType0Font font = PDType0Font.load(document, new File("fonts/simsun.ttf"));
            PDImageXObject signatureImage = PDImageXObject.createFromFile(signatureImagePath, document);

            PDPage currentPage = addNewPage(document);
            float yPosition = CONTENT_START_Y;

            // 绘制表头
            float headerHeight = 30;
            drawTableHeader(document, currentPage, font, yPosition, headerHeight, columnWidths, tableHeaders);
            yPosition -= headerHeight;

            // 遍历数据行
            for (String[] rowData : data) {
                // 计算当前行高
                float rowHeight = calculateRowHeight(rowData, font, columnWidths);

                // 判断是否需要分页
                if (yPosition - rowHeight < BOTTOM_MARGIN) {
                    // 创建新页并绘制表头
                    currentPage = addNewPage(document);
                    yPosition = CONTENT_START_Y;
                    drawTableHeader(document, currentPage, font, yPosition, headerHeight, columnWidths, tableHeaders);
                    yPosition -= headerHeight;
                }

                // 绘制数据行
                drawTableRow(document, currentPage, font, rowData, yPosition, rowHeight, signatureImage, columnWidths);
                yPosition -= rowHeight;
            }

            document.save(outputPath);
        }
    }

    // 为了兼容现有代码，保留原来的方法，但在内部调用新方法
    public void generatePdf(String outputPath, List<String[]> data, String signatureImagePath) throws IOException {
        float[] defaultColumnWidths = {100, 200, 150, 100};
        String[] defaultTableHeaders = {"列1", "列2", "列3", "签名"};
        generatePdf(outputPath, data, signatureImagePath, defaultColumnWidths, defaultTableHeaders);
    }

    private PDPage addNewPage(PDDocument document) {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        return page;
    }

    // 计算行高（考虑文本内容、字体大小等因素）
    private float calculateRowHeight(String[] rowData, PDType0Font font, float[] columnWidths) throws IOException {
        // 基础行高
        float baseHeight = 20;

        // 根据内容计算实际所需行高
        float maxContentHeight = baseHeight;

        for (int i = 0; i < rowData.length; i++) {
            if (i == rowData.length - 1) continue; // 跳过最后一列（签名列）

            String content = rowData[i];
            float colWidth = columnWidths[i] - 10; // 减去内边距
            float textWidth = font.getStringWidth(content) / 1000 * 10; // 10号字体

            // 如果文本宽度超过列宽，计算需要的行数
            int lines = (int) Math.ceil(textWidth / colWidth);
            float cellHeight = lines * 12 + 8; // 行高12 + 上下边距

            maxContentHeight = Math.max(maxContentHeight, cellHeight);
        }

        return maxContentHeight;
    }

    private void drawTableHeader(PDDocument document, PDPage page, PDType0Font font,
                                 float y, float height, float[] columnWidths, String[] tableHeaders) throws IOException {
        try (PDPageContentStream contentStream = new PDPageContentStream(
                document, page, PDPageContentStream.AppendMode.APPEND, true)) {

            // 绘制表头背景和文本
            float x = MARGIN;
            contentStream.setNonStrokingColor(230, 230, 230);
            float tableWidth = 0;
            for (float width : columnWidths) tableWidth += width;

            contentStream.addRect(x, y - height, tableWidth, height);
            contentStream.fill();
            contentStream.setNonStrokingColor(0, 0, 0);

            // 绘制表头文本
            contentStream.beginText();
            contentStream.setFont(font, 12);
            for (int i = 0; i < tableHeaders.length; i++) {
                // 文本水平垂直居中
                contentStream.newLineAtOffset(x + 5, y - height / 2 + 4);
                contentStream.showText(tableHeaders[i]);
                contentStream.endText();

                if (i < tableHeaders.length - 1) {
                    x += columnWidths[i];
                    contentStream.beginText();
                }
            }

            // 绘制表格边框线
            drawTableGridLines(contentStream, MARGIN, y, columnWidths, height);
        }
    }

    private void drawTableRow(PDDocument document, PDPage page, PDType0Font font,
                              String[] rowData, float y, float height,
                              PDImageXObject signatureImage, float[] columnWidths) throws IOException {
        try (PDPageContentStream contentStream = new PDPageContentStream(
                document, page, PDPageContentStream.AppendMode.APPEND, true)) {

            float x = MARGIN;

            // 绘制单元格文本
            contentStream.beginText();
            contentStream.setFont(font, 10);

            for (int i = 0; i < rowData.length - 1; i++) {
                contentStream.newLineAtOffset(x + 5, y - height / 2 + 4);
                contentStream.showText(rowData[i]);
                contentStream.endText();

                if (i < rowData.length - 2) {
                    x += columnWidths[i];
                    contentStream.beginText();
                }
            }

            // 绘制签名图片
            float signatureX = MARGIN;
            for (int i = 0; i < columnWidths.length - 1; i++) {
                signatureX += columnWidths[i];
            }
            signatureX += 10; // 签名图片位置偏移

            float signatureY = y - height + 4;
            float signatureWidth = 80;
            float signatureHeight = Math.min(16, height - 8);
            contentStream.drawImage(signatureImage, signatureX, signatureY, signatureWidth, signatureHeight);

            // 绘制表格边框线
            drawTableGridLines(contentStream, MARGIN, y, columnWidths, height);
        }
    }

    private void drawTableGridLines(PDPageContentStream contentStream, float x, float y,
                                    float[] columnWidths, float height) throws IOException {
        contentStream.setLineWidth(0.5f);

        // 绘制水平线
        float xEnd = x;
        for (float width : columnWidths) xEnd += width;

        contentStream.moveTo(x, y);
        contentStream.lineTo(xEnd, y);
        contentStream.stroke();

        contentStream.moveTo(x, y - height);
        contentStream.lineTo(xEnd, y - height);
        contentStream.stroke();

        // 绘制垂直线
        float xPos = x;
        contentStream.moveTo(xPos, y);
        contentStream.lineTo(xPos, y - height);
        contentStream.stroke();

        for (float width : columnWidths) {
            xPos += width;
            contentStream.moveTo(xPos, y);
            contentStream.lineTo(xPos, y - height);
            contentStream.stroke();
        }
    }
}

