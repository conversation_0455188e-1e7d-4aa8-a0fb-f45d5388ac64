package com.ruoyi.core.service;

import cn.hutool.core.util.IdUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ActionService {
    public final static String TABLE_ACTION_CONFIG = "data_button_config";
    private final GenericMapper genericMapper;
    private final LogService logService;

    public ActionService(GenericMapper genericMapper, LogService logService) {
        this.genericMapper = genericMapper;
        this.logService = logService;
    }

    public Map<String, Object> getActionConfigLogList(String actionCode, int take, long skip) {
        final String TABLE_LOG = "pso_btn_log";
        List<String[]> condition = List.<String[]>of(
                new String[]{"equal", "btn_id", actionCode}
        );
        List<Map<String, Object>> logList = this.genericMapper.getList(
                TABLE_LOG,
                "*",
                condition,
                "order by log_time desc limit " + skip + "," + take
        );
        if (logList.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        List<Map<String, Object>> result = this.genericMapper.getList(
                TABLE_LOG,
                "count(*) as total",
                condition,
                "limit 1"
        );
        return Map.of("rows", logList, "total", Integer.valueOf(result.get(0).get("total").toString()));
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param actionCode action编码
     * @param configCode 配置编码
     */
    public void removeActionConfig(String actionCode, String configCode) {
        final String TABLE_SCRIPT_SYNTAX = "pso_script_syntax";
        final String TABLE_SCRIPT_PARAMS = "pso_script_params";
        final String TABLE_SCRIPT_DRIVER = "pso_script_driver";
        List<String[]> condition = List.<String[]>of(
                new String[]{"equal", "auto_no", configCode}
        );
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_ACTION_CONFIG,
                "*",
                condition,
                "limit 1"
        );
        if (configList.isEmpty()) {
            throw new RuntimeException("配置不存在");
        }
        condition = List.<String[]>of(
                new String[]{"equal", "auto_no", configCode}
        );
        genericMapper.delete(TABLE_ACTION_CONFIG, condition);

        condition = List.<String[]>of(
                new String[]{"equal", "driver_id", configList.get(0).get("driver_id").toString()}
        );
        genericMapper.delete(TABLE_SCRIPT_SYNTAX, condition);

        condition = List.<String[]>of(
                new String[]{"equal", "driver_id", configList.get(0).get("driver_id").toString()}
        );
        genericMapper.delete(TABLE_SCRIPT_PARAMS, condition);

        condition = List.<String[]>of(
                new String[]{"equal", "driver_id", configList.get(0).get("driver_id").toString()}
        );
        genericMapper.delete(TABLE_SCRIPT_DRIVER, condition);

//        condition = List.<String[]>of(
//                new String[]{"equal", "driver_id", configList.get(0).get("driver_id").toString()}
//        );
//        genericMapper.delete("pso_node_data", condition);

        condition = List.<String[]>of(
                new String[]{"equal", "bind_id", configList.get(0).get("driver_id").toString()}
        );
        genericMapper.delete("matter_driver_condition", condition);

        condition = List.<String[]>of(
                new String[]{"equal", "driver_id", configList.get(0).get("driver_id").toString()}
        );
        this.genericMapper.delete("matter_driver_list", condition);
        this.logService.saveLog(SecurityUtils.getLoginUser().getUserId().toString(), LogService.MODULE_FORM, "删除动作配置", null, configList.get(0));
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param configCode 配置编码
     */
    public void copyActionConfig(String configCode) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_ACTION_CONFIG,
                "*",
                List.<String[]>of(
                        new String[]{"equal", "auto_no", configCode}
                ),
                "limit 1"
        );
        if (configList.isEmpty()) {
            throw new RuntimeException("配置不存在");
        }
        Map<String, Object> config = configList.get(0);
        config.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        config.put("btn_name", config.get("btn_name") + "_复制");
        this.genericMapper.create(TABLE_ACTION_CONFIG, config);
        this.logService.saveLog(SecurityUtils.getLoginUser().getUserId().toString(), LogService.MODULE_FORM, "复制动作配置", config, configList.get(0));
    }

    public void updateActionConfig(Map<String, Object> data) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_ACTION_CONFIG,
                "*",
                List.<String[]>of(
                        new String[]{"equal", "auto_no", data.get("auto_no").toString()}
                ),
                "limit 1"
        );
        if (configList.isEmpty()) {
            throw new Error("配置不存在");
        }
        Map<String, Object> condition = Map.of("auto_no", data.get("auto_no").toString());
        data.remove("auto_no");
        this.genericMapper.update(TABLE_ACTION_CONFIG, data, condition);
        this.logService.saveLog(SecurityUtils.getLoginUser().getUserId().toString(), LogService.MODULE_FORM, "编辑动作配置", data, configList.get(0));
    }

    public Map<String, Object> getActionConfig(String actionCode, String configCode) {
        List<Map<String, Object>> configList = this.genericMapper.getList(
                TABLE_ACTION_CONFIG,
                "*",
                List.of(
                        new String[]{"equal", "auto_no", configCode},
                        new String[]{"equal", "source_code", actionCode}
                ),
                "limit 1"
        );
        if (configList.isEmpty()) {
            return Map.of();
        }
        return configList.get(0);
    }

    public List<Map<String, Object>> getActionConfigList(String actionCode) {
        List<Map<String, Object>> configList = this.genericMapper.getList(
                TABLE_ACTION_CONFIG,
                "*",
                List.<String[]>of(new String[]{"equal", "source_code", actionCode}),
                "order by btn_order"
        );
        if (configList.isEmpty()) {
            return List.of();
        }
        return configList;
    }

    public void createActionConfig(Map<String, Object> data) {
        data.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        this.genericMapper.create(TABLE_ACTION_CONFIG, data);
        this.logService.saveLog(SecurityUtils.getLoginUser().getUserId().toString(), LogService.MODULE_FORM, "新增动作配置", data, null);
    }
}
