package com.ruoyi.core.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.enums.AuthEnum;
import com.ruoyi.core.enums.DataStatusEnum;
import com.ruoyi.core.mapper.FormMapper;
import com.ruoyi.core.mapper.GenericMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.ConnectionCallback;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.*;

import static com.ruoyi.core.constants.PsoConstant.*;
import static com.ruoyi.core.service.LogService.MODULE_WORKFLOW;

@Slf4j
@Service
public class WorkflowService {
    public static final String NODE_EXT_WF = "node_ext_wf";
    private final GenericMapper genericMapper;
    private final FormMapper formMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final LogService logService;

    public WorkflowService(GenericMapper genericMapper, FormMapper formMapper, NamedParameterJdbcTemplate namedParameterJdbcTemplate, LogService logService) {
        this.genericMapper = genericMapper;
        this.formMapper = formMapper;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.logService = logService;
    }

    public Map<String, Object> getWorkflow(String workflowCode) {
        final String TABLE_WORKFLOW = "node_ext_data";
        final String TALBE_EXT_DATA = "node_ext_wf";

        List<Map<String, Object>> workflowList = genericMapper.getList(
                TABLE_WORKFLOW,
                "*",
                List.of(
                        new String[]{"equal", "data_code", workflowCode},
                        new String[]{"equal", "data_type", "1"}
                ),
                "limit 1"
        );
        if (workflowList.isEmpty()) {
            return Map.of();
        }

        List<Map<String, Object>> extendData = genericMapper.getList(
                TALBE_EXT_DATA,
                "*",
                List.<String[]>of(new String[]{"equal", "map_data_code", workflowCode}),
                "limit 1"
        );

        return Map.of(
                "workflow", workflowList.get(0),
                "workflow_ext", extendData.isEmpty() ? Map.of() : extendData.get(0)
        );
    }

    /**
     * @description 流程右侧分页列表
     * <AUTHOR>
     */
    public List<Map<String, Object>> flowList(String wfCode, String wfSName, String state) {
        return formMapper.flowList(APP_ID, wfCode, wfSName, state);
    }

    /**
     * @description 创建流程
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object add(Map<String, Object> params) {
        String username = SecurityUtils.getUsername();
        Map<String, Object> wfDataMap = new HashMap<>();
        if (Objects.isNull(params.get("wf_code")) || StrUtil.isBlank(params.get("wf_code").toString())) {
            wfDataMap.put("wf_code", IdUtil.getSnowflakeNextIdStr());
        } else {
            Map<String, Object> map = namedParameterJdbcTemplate.queryForMap("select * from node_ext_wf where wf_code = :wf_code", Map.of("wf_code", params.get("wf_code")));
            if (MapUtil.isNotEmpty(map)) {
                throw new RuntimeException("流程编码已存在");
            }
            if (!Validator.isGeneral(params.get("data_code").toString())) {
                throw new RuntimeException("流程编码格式不正确");
            }
            wfDataMap.put("wf_code", params.get("wf_code"));
        }
        String wfCode = wfDataMap.get("wf_code").toString();
        wfDataHandle(params, wfDataMap, username, wfCode);
        genericMapper.create(NODE_EXT_WF, wfDataMap);
        genericMapper.create("node_ext_data", nodeExtDataHandle(params, username, wfCode));
        Map<String, Object> dataStatusMap = new HashMap<>();
        for (DataStatusEnum dataStatusEnum : DataStatusEnum.values()) {
            genericMapper.create("DATA_STATUS_CONFIG", dataStatusHandle(dataStatusMap, dataStatusEnum.getKey(), dataStatusEnum.getValue(), wfCode));
        }
        genericMapper.create("PSO_NODE_DATA", FormService.nodeDataHandle(params.get("node_id").toString(), wfCode, NODE_EXT_WF));
        genericMapper.create("role_auth_show", FormService.roleAuthHandle(wfCode, AuthEnum.FlowAuth.getType()));
//        logService.asyncSavePsoLog(new PsoOperLog()
//                .setLogType(INSERT_FLOW_LOG_TYPE)
//                .setLogUser(username)
//                .setLogContent(
//                        StrUtil.format("添加流程:{}", params.get("data_name"))
//                )
//                .setDataCode(wfCode)
//                .setLeafId(wfCode), params);
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_WORKFLOW, "新增流程", wfDataMap, null);
        return wfCode;
    }

    @Transactional(rollbackFor = Exception.class)
    public Object publish(Map<String, Object> params) throws Exception {
        String username = SecurityUtils.getUsername();
        String code=params.get("wf_code").toString();
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put("wf_code",code);
        params.put("upuser",username);
        params.put("input_time",DateUtil.now());
        genericMapper.update(NODE_EXT_WF, params,conditionMap);
        //数据删除
        List<String> sqlStatements = new ArrayList<>();
        MapSqlParameterSource sqlParams = new MapSqlParameterSource();
        sqlParams.addValue("wf_code", code);
        // 更新pso_node_data表
        sqlStatements.add("delete from WF_TERM_INFO where wf_code=:wf_code");
        sqlStatements.add("delete from WF_STEP_AUTH where wf_code=:wf_code");
        sqlStatements.add("delete from WF_STEP_COPY where wf_code=:wf_code");
        sqlStatements.add("delete from WF_STEP_INFO where wf_code=:wf_code");
        // 执行所有SQL语句
        for (String sql : sqlStatements) {
            try {
                namedParameterJdbcTemplate.update(sql, sqlParams);
            } catch (Exception e) {
                // 记录错误但继续执行其他语句
                log.error("执行SQL失败: {}, 错误: {}", sql, e.getMessage());
            }
        }
        //流程解析
        JSONArray array= JSONUtil.parseArray(params.get("wf_map_tp"));
        if (array == null || array.isEmpty()) return "";
        CompileJson(array,code);
        namedParameterJdbcTemplate.update(
                "update node_ext_wf set is_pub=1 where wf_code= :code",
                Map.of(
                        "code", code
                )
        );
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_WORKFLOW, "流程发布", params, null);
        return code;
    }

    //region 流程发布私有方法
    int StepId = 1;

    private void CompileJson(JSONArray array,String code) throws Exception {
        // 获取开始节点信息
        JSONObject startObj = array.getJSONObject(0);
        if (!startObj.getStr("tid").equalsIgnoreCase("start"))
            throw new Exception("流程必须设置开始节点");
        CompileLeaf(code,startObj, StepId, 0, "0", "start");

        for (int i = 1; i < array.size(); i++) {
            JSONObject obj = array.getJSONObject(i);
            switch (obj.getStr("tid")) {
                case "review":
                case "start":
                    CompileLeaf(code, obj, StepId, 0, "0", String.format("%d", i));
                    break;
                case "autoprocess":
                    CompileAutoLeaf(code, obj, StepId, 0, "0", String.format("%d", i));
                    break;
                case "branch":
                    CompileBranch(code, obj, StepId, 0, "0", String.format("%d", i));
                    break;
                case "branchitem":
                    CompileCondition(code, obj, StepId, 0, "0", String.format("%d", i));
                    break;
                default:
                    throw new Exception("JSON数组中必须包括tid的配置信息");
            }
        }
    }

    private String CombineCondition(JSONArray conditonArr) throws Exception {
        List<String> conditionList=new ArrayList<>();
        for (int num=0;num<conditonArr.size();num++){
            List<String> andListr=new ArrayList<>();
            JSONArray andArr=conditonArr.getJSONArray(num);
            for (int k=0;k<andArr.size();k++){
                String _con=ConcatConditon(andArr.getJSONObject(k));
                if (!_con.isEmpty()){
                    andListr.add(_con);
                }
            }
            if (!andArr.isEmpty()){
                conditionList.add(String.format("【%s】",String.join(" AND ",andListr)));
            }
        }
        if (!conditionList.isEmpty()){
            return String.format("【%s】",String.join(" OR ",conditionList));
        }
        return "";
    }

    private String ConcatConditon(JSONObject obj) throws Exception {
        switch (obj.get("type").toString()){
            case "0"://不等于
                return String.format("[%s] !=[#%s]",obj.getStr("id"),obj.getStr("value"));
            case "1"://等于
                return String.format("[%s] =[#%s]",obj.getStr("id"),obj.getStr("value"));
            case "2"://模糊查询
                return String.format("[%s] LIKE([#%s])",obj.getStr("id"),obj.getStr("value"));
            case "3"://不包含
                return String.format("[%s] NOT LIKE([#%s])",obj.getStr("id"),obj.getStr("value"));
            case "4"://in
                List<String> valueList=new ArrayList<>();
                for (String str :obj.getStr("value").split(",")){
                    valueList.add(String.format("[#%s]",str));
                }
                return String.format(" [%s] IN(%s)",obj.getStr("id"),
                        String.join(",",valueList));
            case "5"://not in
                List<String> notvalueList=new ArrayList<>();
                for (String str :obj.getStr("value").split(",")){
                    notvalueList.add(String.format("[#%s]",str));
                }
                return String.format(" [%s] NOT IN(%s)",obj.getStr("id"),
                        String.join(",",notvalueList));
            case "8"://等于
                return  String.format("[%s] =[# ]",obj.getStr("id"));
            case "9"://不等于
                return String.format("[%s] !=[# ]",obj.getStr("id"));
            case "20": // >
                return String.format("[%s] >[#%s]",obj.getStr("id"),obj.getStr("value"));
            case "21": // >=
                return String.format("[%s] >=[#%s]",obj.getStr("id"),obj.getStr("value"));
            case "22": //<
                return String.format("[%s] <[#%s]",obj.getStr("id"),obj.getStr("value"));
            case "23": //<=
                return String.format("[%s] <=[#%s]",obj.getStr("id"),obj.getStr("value"));
            default:
                return "";
        }
    }

    private void CompileCondition(String wfcode, JSONObject obj,
                                  int _stepId, int _pid, String _path, String _tag) throws Exception {
        String stepCode = obj.getStr("nid");

        // 步骤流程表信息
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("step_code", stepCode);
        dataMap.put("step_name", obj.getStr("name"));
        dataMap.put("wf_code", wfcode);
        dataMap.put("step_no", _stepId);
        dataMap.put("step_pid", _pid);
        dataMap.put("step_path", _path);
        dataMap.put("node_type", 1);
        dataMap.put("node_tag", _tag);
        dataMap.put("auto_copy", 0);
        dataMap.put("step_script", "{}");
        dataMap.put("mult_type", 0);
        dataMap.put("open_sign", 0);
        dataMap.put("open_agent",0);
        dataMap.put("dept_agent", 0);
        dataMap.put("approve_type", 0);
        dataMap.put("step_config", obj.toString());
        dataMap.put("next_review",0);
        dataMap.put("limit_type", 0);
        dataMap.put("limit_time", 0);
        genericMapper.create("WF_STEP_INFO",dataMap);
        StepId++;
        // 处理条件配置信息
        JSONArray conditonArr=obj.getJSONArray("condition");
        Boolean hasCondition = !conditonArr.isEmpty();
        Map<String, Object> termMap = new HashMap<>();
        termMap.put("term_code",  IdUtil.getSnowflakeNextIdStr());
        termMap.put("step_code", stepCode);
        termMap.put("wf_code", wfcode);
        termMap.put("has_condition", hasCondition ? 1 : 0);
        boolean isOther= obj.containsKey("isOther")?obj.getBool("isOther"):false;
        if (isOther){ //其他节点
            termMap.put("next_step_code", "1");  //条件节点--其他
            termMap.put("term_expression", "");
        }else{
            String condType=obj.getOrDefault("condType","0").toString();
            if (condType.equals("0")){
                termMap.put("next_step_code", isOther?"1":"0");  //条件节点--其他
                termMap.put("term_expression", hasCondition?CombineCondition(conditonArr):"");
            }else{ //脚本条件
                termMap.put("next_step_code", "2");  //脚本条件
                termMap.put("term_expression", obj.getStr("driverId"));
            }
        }
        genericMapper.create("WF_TERM_INFO",termMap);
        // 处理子节点信息
        if (obj.containsKey("children")){
            JSONArray conditionChildren = obj.getJSONArray("children");
            if (conditionChildren == null || conditionChildren.isEmpty()) return;

            for (int i = 0; i < conditionChildren.size(); i++) {
                JSONObject conditionObj = conditionChildren.getJSONObject(i);
                switch (conditionObj.getStr("tid")) {
                    case "review":
                    case "start":
                        CompileLeaf(wfcode, conditionObj, StepId, _stepId, String.format("%s-%s", _path, _stepId), String.format("%d", i));
                        break;
                    case "autoprocess":
                        CompileAutoLeaf(wfcode, conditionObj, StepId, _stepId, String.format("%s-%s", _path, _stepId), String.format("%d", i));
                        break;
                    case "branch":
                        CompileBranch(wfcode, conditionObj, StepId, _stepId, String.format("%s-%s", _path, _stepId), String.format("%d", i));
                        break;
                    case "branchitem"://condition
                        CompileCondition(wfcode, conditionObj, StepId, _stepId, String.format("%s-%s", _path, _stepId), String.format("%d", i));
                        break;
                    default:
                        throw new Exception("JSON数组中必须包括tid的配置信息");
                }
            }
        }

    }

    private void CompileBranch(String wfcode, JSONObject obj,
                               int _stepId, int _pid, String _path, String _tag) throws Exception {
        // 步骤流程表信息
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("step_code", obj.getStr("nid"));
        dataMap.put("step_name", obj.getStr("name"));
        dataMap.put("wf_code", wfcode);
        dataMap.put("step_no", _stepId);
        dataMap.put("step_pid", _pid);
        dataMap.put("step_path", _path);
        dataMap.put("node_type", 0);
        dataMap.put("node_tag", _tag);
        dataMap.put("auto_copy", 0);
        dataMap.put("step_script", "{}");
        dataMap.put("mult_type", 0);
        dataMap.put("open_sign", 0);
        dataMap.put("open_agent",0);
        dataMap.put("dept_agent", 0);
        dataMap.put("approve_type", 0);
        dataMap.put("step_config", obj.toString());
        dataMap.put("next_review",0);
        dataMap.put("limit_type", 0);
        dataMap.put("limit_time", 0);
        genericMapper.create("WF_STEP_INFO",dataMap);
        StepId++;
        // 处理子节点信息
        JSONArray branchChildren = obj.getJSONArray("children");
        if (branchChildren == null || branchChildren.isEmpty()) return;

        for (int i = 0; i < branchChildren.size(); i++) {
            JSONObject conditionObj = branchChildren.getJSONObject(i);
            switch (conditionObj.getStr("tid")) {
                case "review":
                case "start":
                    CompileLeaf(wfcode, conditionObj, StepId, _stepId, String.format("%s-%s", _path, _stepId), String.format("%d", i));
                    break;
                case "autoprocess":
                    CompileAutoLeaf(wfcode, conditionObj, StepId, _stepId, String.format("%s-%s", _path, _stepId), String.format("%d", i));
                    break;
                case "branch"://分支
                    CompileBranch(wfcode, conditionObj, StepId, _stepId, String.format("%s-%s", _path, _stepId), String.format("%d", i));
                    break;
                case "branchitem": //condition
                    CompileCondition(wfcode, conditionObj, StepId, _stepId, String.format("%s-%s", _path, _stepId), String.format("%d", i));
                    break;
                default:
                    throw new Exception("JSON数组中必须包括tid的配置信息");
            }
        }
    }

    private void CompileAutoLeaf(String wfcode, JSONObject obj,
                                 int _stepId, int _pid, String _path,String _tag) throws Exception {
        String stepCode = obj.getStr("nid");
        int autocopy=obj.containsKey("copyable")?obj.getInt("copyable"):0;
        String script=obj.containsKey("op")?obj.getStr("op"):"{}";
        int opensign=obj.containsKey("signable")?obj.getInt("signable"):0;
        int mule=obj.containsKey("handType")?obj.getInt("handType"):1;
        // 步骤流程表信息
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("step_code", stepCode);
        dataMap.put("step_name", obj.getStr("name"));
        dataMap.put("wf_code", wfcode);
        dataMap.put("step_no", _stepId);
        dataMap.put("step_pid", _pid);
        dataMap.put("step_path", _path);
        dataMap.put("node_type", 8);
        dataMap.put("node_tag", _tag);
        dataMap.put("auto_copy", autocopy);
        dataMap.put("step_script", script);
        dataMap.put("mult_type", mule);
        dataMap.put("open_sign", opensign);
        dataMap.put("open_agent",obj.containsKey("useAgent")?0:obj.get("useAgent"));
        dataMap.put("dept_agent", obj.containsKey("useDeptAgent")?0:obj.get("useDeptAgent"));
        dataMap.put("approve_type", obj.containsKey("approveType")?0:obj.get("approveType"));
        dataMap.put("step_config", obj.toString());
        dataMap.put("next_review",obj.containsKey("nextReviewType")?0:obj.get("nextReviewType"));
        dataMap.put("limit_type", 0);
        dataMap.put("limit_time", 0);
        genericMapper.create("WF_STEP_INFO",dataMap);

        Map<String, Object> authMap = new HashMap<>();
        authMap.put("auth_id", IdUtil.getSnowflakeNextIdStr());
        authMap.put("step_code", stepCode);
        authMap.put("wf_code", wfcode);
        authMap.put("access", 1);
        authMap.put("is_bind", 0);
        authMap.put("from_self", 0);
        authMap.put("map_id", "sysadmin");
        authMap.put("bind_field", "");
        authMap.put("order", 0);
        authMap.put("bind_tag", "");
        genericMapper.create("wf_step_auth",authMap);
        // 步骤抄送表 开启抄送
        if (autocopy==1){
            JSONArray authArr=obj.getJSONArray("copyto");
            CompileCopyInfo(TransActor(authArr),stepCode,wfcode);
        }
        StepId++;
    }

    private void CompileLeaf(String wfcode,JSONObject obj,
                             int _stepId, int _pid, String _path, String _tag) throws Exception {
        String stepCode = obj.getStr("nid");
        int autocopy=obj.containsKey("copyable")?obj.getInt("copyable"):0;
        String script=obj.containsKey("op")?obj.getStr("op"):"{}";
        int opensign=obj.containsKey("signable")?obj.getInt("signable"):0;
        int mule=obj.containsKey("handType")?obj.getInt("handType"):1;
        // 步骤流程表信息
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("step_code", stepCode);
        dataMap.put("step_name", obj.getStr("name"));
        dataMap.put("wf_code", wfcode);
        dataMap.put("step_no", _stepId);
        dataMap.put("step_pid", _pid);
        dataMap.put("step_path", _path);
        dataMap.put("node_type", 9);
        dataMap.put("node_tag", _tag);
        dataMap.put("auto_copy", autocopy);
        dataMap.put("step_script", script);
        dataMap.put("mult_type", mule);
        dataMap.put("open_sign", opensign);
        dataMap.put("open_agent",obj.containsKey("useAgent")?0:obj.get("useAgent"));
        dataMap.put("dept_agent", obj.containsKey("useDeptAgent")?0:obj.get("useDeptAgent"));
        dataMap.put("approve_type", obj.containsKey("approveType")?0:obj.get("approveType"));
        dataMap.put("step_config", obj.toString());
        dataMap.put("next_review",obj.containsKey("nextReviewType")?0:obj.get("nextReviewType"));
        dataMap.put("limit_type", 0);
        dataMap.put("limit_time", 0);
        genericMapper.create("WF_STEP_INFO",dataMap);
        // 步骤授权表
        if(obj.containsKey("approver")){
            JSONArray authArr=obj.getJSONArray("approver");
            CompileAuthInfo(TransActor(authArr),stepCode,wfcode);
        }
        // 步骤抄送表 开启抄送
        if (autocopy==1){
            JSONArray authArr=obj.getJSONArray("copyto");
            CompileCopyInfo(TransActor(authArr),stepCode,wfcode);
        }
        //开启加签
//        if (opensign==1){
//            JSONArray signArray = obj.getJSONArray("consign");
//            if (signArray != null && !signArray.isEmpty()) {
//                for (int num=0;num<signArray.size();num++){
//                    JSONObject signObj=signArray.getJSONObject(num);
//                    CompileSign(lst,signObj,Integer.parseInt(_tag),_stepId,_path,_tag,stepCode,obj.getStr("name")+"-会签-"+(num+1));
//                }
//            }
//        }
        StepId++;
    }

    private void CompileCopyInfo(JSONArray authArr, String code, String wfcode) throws Exception {
        for (int num=0;num<authArr.size();num++){
            JSONArray configArr=authArr.getJSONArray(num);
            for (int k=0;k<configArr.size();k++){
                JSONObject configObj=configArr.getJSONObject(k);
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("copy_id", IdUtil.getSnowflakeNextIdStr());
                dataMap.put("step_code", code);
                dataMap.put("wf_code", wfcode);
                dataMap.put("access_type", configObj.get("type"));
                if (configObj.getOrDefault("subType", "").toString().isEmpty()){
                    dataMap.put("from_self", 0);
                }else{
                    dataMap.put("from_self",Integer.parseInt(configObj.getOrDefault("subType", "0").toString()));
                }
                dataMap.put("map_id",configObj.getOrDefault("value",""));
                dataMap.put("bind_field",configObj.getOrDefault("value",""));
                dataMap.put("act_order", num);//valueList.size()==1?num:p
                dataMap.put("bind_tag", configObj.get("tag"));
                if (configObj.getBool("enable")){
                    String values= configObj.getOrDefault("value","").toString();
                    String source= configObj.getOrDefault("valueSource","0").toString(); //0 指定  2 申请人所在  1 动态
                    dataMap.put("is_bind", source);
                    switch (source){
                        case "2":  //申请人所在
                        {
                            dataMap.put("is_bind", 2);
                            dataMap.put("bind_field","");
                            dataMap.put("map_id","");
                            break;
                        }
                        case "1":   //绑定字段
                        {
                            dataMap.put("is_bind", 1);
                        }
                        break;
                        case "0":  //指定
                        default:
                        {
                            dataMap.put("is_bind", 0);
                            dataMap.put("bind_field","");
                        }
                        break;
                    }
                }
                genericMapper.create("wf_step_copy",dataMap);
            }
        }
    }

    private void CompileAuthInfo(JSONArray authArr, String code, String wfcode) throws Exception {
        for (int num=0;num<authArr.size();num++){
            JSONArray configArr=authArr.getJSONArray(num);
            for (int k=0;k<configArr.size();k++){
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("auth_id", IdUtil.getSnowflakeNextIdStr());
                dataMap.put("step_code", code);
                dataMap.put("wf_code", wfcode);
                JSONObject configObj=configArr.getJSONObject(k);
//                if (configObj.getBool("enable"))
                {
                    String values=configObj.containsKey("value")? configObj.get("value").toString() :"";
                    String source=configObj.containsKey("valueSource")? configObj.get("valueSource").toString() :"1"; //0 指定  2 申请人所在  1 动态
                    dataMap.put("access_type", configObj.get("type"));
                    dataMap.put("is_bind", source);
                    if (configObj.getOrDefault("subType", "").toString().isEmpty()){
                        dataMap.put("from_self", 0);
                    }else{
                        dataMap.put("from_self",Integer.parseInt(configObj.getOrDefault("subType", "0").toString()));
                    }
                    dataMap.put("map_id",configObj.getOrDefault("value",""));
                    dataMap.put("bind_field",configObj.getOrDefault("value",""));
                    dataMap.put("act_order", num);//valueList.size()==1?num:p
                    dataMap.put("bind_tag", configObj.get("tag"));
                    switch (source){
                        case "2":  //申请人所在
                        {
                            dataMap.put("is_bind", 2);
                            dataMap.put("bind_field","");
                            dataMap.put("map_id","");
                            break;
                        }
                        case "1":   //绑定字段
                        {
                            dataMap.put("is_bind", 1);
                        }
                        break;
                        case "0":  //指定
                        default:
                        {
                            dataMap.put("is_bind", 0);
                            dataMap.put("bind_field","");
                        }
                        break;
                    }
                }
                genericMapper.create("wf_step_auth",dataMap);
            }
        }
    }

    private JSONArray TransActor(JSONArray authArr) throws Exception {
        JSONArray returnArr=new JSONArray();
        JSONArray sigleArr=new JSONArray();
        for (int num=0;num<authArr.size();num++){
            JSONObject configObj=authArr.getJSONObject(num);
            sigleArr.add(configObj);
            if (configObj.getStr("mthd").equals("2") && num!=authArr.size()-1){ //或
                returnArr.add(sigleArr);
                sigleArr=new JSONArray();
            }
        }
        returnArr.add(sigleArr);
        return returnArr;
    }

    //endregion

    /**
     * @description 编辑流程/重命名流程
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object rename(String dataCode, Map<String, Object> params) {
        String changeCode = StrUtil.nullToEmpty(params.getOrDefault("change_code", dataCode).toString());
        String dataName = StrUtil.nullToEmpty(params.getOrDefault("wf_name", "").toString());
        String dataSname = StrUtil.nullToEmpty(params.getOrDefault("wf_sname", "").toString());

        // 获取当前流程信息
        Map<String, Object> currentFlow;
        try {
            currentFlow = namedParameterJdbcTemplate.queryForMap(
                    "select * from node_ext_wf where wf_code = :code",
                    Map.of("code", dataCode)
            );
        } catch (Exception e) {
            throw new RuntimeException("获取流程信息失败: " + e.getMessage());
        }

        // 如果只是更新名称，不更改code
        if (dataCode.equals(changeCode)) {
            if (dataName.equals(currentFlow.get("wf_name")) && dataSname.equals(currentFlow.get("wf_sname"))) {
                return 1; // 没有变化，直接返回
            }

            // 更新表单名称
            namedParameterJdbcTemplate.update(
                    "update node_ext_data set data_name = :data_name, data_sname = :data_sname where data_code = :code",
                    Map.of(
                            "data_name", dataName,
                            "data_sname", dataSname,
                            "code", dataCode
                    )
            );

            // 更新流程名称
            namedParameterJdbcTemplate.update(
                    "update node_ext_wf set wf_name = :wf_name, wf_sname = :wf_sname where wf_code = :code",
                    Map.of(
                            "wf_name", dataName,
                            "wf_sname", dataSname,
                            "code", dataCode
                    )
            );

            // 记录日志
//            logService.asyncSavePsoLog(new PsoOperLog()
//                            .setLogType(RENAME_WF_LOG_TYPE)
//                            .setLogUser(SecurityUtils.getUsername())
//                            .setLogContent(StrUtil.format("更新流程名称:{}", dataName))
//                            .setDataCode(dataCode)
//                            .setLeafId(dataCode),
//                    params
//            );
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_WORKFLOW, "变更名称", changeCode, dataCode);
            return 1;
        }

        // 如果要更改code，检查新code是否已存在
        List<Map<String, Object>> existingFlow = namedParameterJdbcTemplate.queryForList(
                "select * from node_ext_wf where wf_code = :code",
                Map.of("code", changeCode)
        );
        if (!existingFlow.isEmpty()) {
            throw new RuntimeException("流程编码已存在");
        }

        // 检查是否有脚本引用
        try {
            List<Map<String, Object>> scriptReferences = namedParameterJdbcTemplate.queryForList(
                    "select count(1) as count from pso_script_driver where driver_config like :pattern",
                    Map.of("pattern", "%" + dataCode + "%")
            );
            if (!scriptReferences.isEmpty() && Integer.parseInt(scriptReferences.get(0).get("count").toString()) > 0) {
                throw new RuntimeException("该流程已被脚本引用，无法更名");
            }
        } catch (Exception e) {
            if (!(e instanceof RuntimeException)) {
                throw new RuntimeException("检查脚本引用失败: " + e.getMessage());
            }
            throw e;
        }

        // 执行更名操作
        // 1. 更新表单数据
        namedParameterJdbcTemplate.update(
                "update node_ext_data set data_name = :data_name, data_sname = :data_sname, data_code = :change where data_code = :code",
                Map.of(
                        "data_name", dataName,
                        "data_sname", dataSname,
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 2. 更新节点数据
        namedParameterJdbcTemplate.update(
                "update pso_node_data set node_data_id = :change where node_data_id = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 3. 更新角色权限
        namedParameterJdbcTemplate.update(
                "update role_auth_show set map_id = :change where map_id = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 4. 更新字典信息
        namedParameterJdbcTemplate.update(
                "update pso_data_dict set data_code = :change where data_code = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );


        String tableName = "data_ext_" + dataCode;
        Boolean exist = namedParameterJdbcTemplate.getJdbcTemplate().execute((ConnectionCallback<Boolean>) connection -> {
            DatabaseMetaData metaData = connection.getMetaData();
            try (ResultSet tables = metaData.getTables(null, null, tableName, null)) {
                return tables.next();
            }
        });
        if (Boolean.TRUE.equals(exist)) {
            // 5. 更新历史表
            namedParameterJdbcTemplate.update(
                    "update " + tableName + " set data_code = :change",
                    Map.of("change", changeCode)
            );
            // 6. 更新表名
            String newTableName = "data_ext_" + changeCode;
            namedParameterJdbcTemplate.update(
                    "EXEC sp_rename :oldName, :newName",
                    Map.of(
                            "oldName", tableName,
                            "newName", newTableName
                    )
            );
        }

        // 7. 更新相关配置
        String[] configTables = {
                "DATA_VIEW_CONFIG", "DATA_BUTTON_CONFIG", "DATA_PRINT_CONFIG",
                "DATA_RULE_CONFIG", "DATA_SERVICE_CONFIG", "PSO_PUB_INFO",
                "DATA_DETAIL_CONFIG", "PSO_SCRIPT_DRIVER", "DATA_STATUS_CONFIG"
        };

        for (String table : configTables) {
            String column = table.equals("PSO_PUB_INFO") ? "bind_id" : "source_code";
            namedParameterJdbcTemplate.update(
                    "update " + table + " set " + column + " = :change where " + column + " = :code",
                    Map.of(
                            "change", changeCode,
                            "code", dataCode
                    )
            );
        }

        // 8. 更新数据映射
        namedParameterJdbcTemplate.update(
                "update pso_data_mapping set m_data_code = :change where m_data_code = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        namedParameterJdbcTemplate.update(
                "update pso_data_mapping set c_data_code = :change where c_data_code = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 9. 更新用户服务信息
        String[] serviceInfoTables = {"user_service_info", "res_service_info", "tag_service_info"};
        for (String table : serviceInfoTables) {
            namedParameterJdbcTemplate.update(
                    "update " + table + " set data_code = :change where data_code = :code",
                    Map.of(
                            "change", changeCode,
                            "code", dataCode
                    )
            );
        }

        // 10. 更新流程相关信息
        // 流程主表
        namedParameterJdbcTemplate.update(
                "update node_ext_wf set wf_name = :wf_name, wf_sname = :wf_sname, wf_code = :change, map_data_code = :change where wf_code = :code",
                Map.of(
                        "wf_name", dataName,
                        "wf_sname", dataSname,
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 流程步骤
        namedParameterJdbcTemplate.update(
                "update wf_step_info set wf_code = :change where wf_code = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 流程条件
        namedParameterJdbcTemplate.update(
                "update wf_term_info set wf_code = :change where wf_code = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 审核人员
        namedParameterJdbcTemplate.update(
                "update wf_step_auth set wf_code = :change where wf_code = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 消息
        namedParameterJdbcTemplate.update(
                "update wf_instance_msg set wf_code = :change where wf_code = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        namedParameterJdbcTemplate.update(
                "update wf_instance_msg set main_wf = :change where main_wf = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 流程实例
        namedParameterJdbcTemplate.update(
                "update wf_instance_info set wf_code = :change where wf_code = :code",
                Map.of(
                        "change", changeCode,
                        "code", dataCode
                )
        );

        // 记录日志
//        logService.asyncSavePsoLog(new PsoOperLog()
//                        .setLogType(RENAME_WF_LOG_TYPE)
//                        .setLogUser(SecurityUtils.getUsername())
//                        .setLogContent(StrUtil.format("更名流程:{}-{}", dataCode, changeCode))
//                        .setDataCode(dataCode)
//                        .setLeafId(dataCode),
//                params
//        );

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_WORKFLOW, "编辑流程", params, existingFlow);
        return 1;
    }

    private void wfDataHandle(Map<String, Object> params, Map<String, Object> wfDataMap, String username, String wfCode) {
        List<Map<String, Object>> wfName = genericMapper.getList(NODE_EXT_WF,
                "*",
                List.<String[]>of(new String[]{"equal", "wf_name", params.get("wf_name").toString()}),
                StrUtil.EMPTY);
        if (CollectionUtil.isNotEmpty(wfName)) {
            throw new RuntimeException("已存在名为【" + params.get("wf_name").toString() + "】的流程");
        }
        wfDataMap.put("wf_code", wfCode);
        wfDataMap.put("is_pub", 0);
        wfDataMap.put("wf_order", 255);
        wfDataMap.put("create_time", DateUtil.now());
        wfDataMap.put("creator", username);
        wfDataMap.put("upuser", "");
        wfDataMap.put("input_time", "");
        wfDataMap.put("map_data_code", wfCode);
        wfDataMap.put("wf_auth_type", 0);
        wfDataMap.put("wf_matter_able", 0);
        wfDataMap.put("wf_urge_able", 0);
        wfDataMap.put("wf_comment_able", 0);
        wfDataMap.put("wf_src_able", 0);
        wfDataMap.put("is_share", 0);
        wfDataMap.put("open_preju", 0);
        wfDataMap.put("wf_type", 0);
        wfDataMap.put("open_start", 0);
        wfDataMap.put("company_id", "");
        wfDataMap.put("wf_ext", "{}");
        wfDataMap.put("wf_name", params.get("wf_name"));
        wfDataMap.put("empty_type", StrUtil.EMPTY);
        wfDataMap.put("wf_tags", StrUtil.EMPTY);
        wfDataMap.put("data_state", JSONUtil.createObj().set("status", "启用").toString());
        wfDataMap.put("wf_map_tp", params.get("wf_map_tp").toString());
        if (Objects.nonNull(params.get("wf_sname")) && StrUtil.isNotBlank(params.get("wf_sname").toString())) {
            wfDataMap.put("wf_sname", params.get("wf_sname"));
        } else {
            wfDataMap.put("wf_sname", params.get("wf_name"));
        }

    }

    private Map<String, Object> nodeExtDataHandle(Map<String, Object> params, String username, String dataCode) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("data_code", dataCode);
        dataMap.put("create_time", DateUtil.now());
        dataMap.put("data_status", 0);
        dataMap.put("open_discuss", 0);
        dataMap.put("data_type", 1);
        dataMap.put("data_sold", 0);
        dataMap.put("is_sys", 0);
        dataMap.put("data_name", params.get("wf_name"));
        if (Objects.isNull(params.get("wf_sname")) || StrUtil.isBlank(params.get("wf_sname").toString())) {
            dataMap.put("data_sname", params.get("wf_name").toString());
        } else {
            dataMap.put("data_sname", params.get("wf_sname").toString());
        }
        dataMap.put("data_design", params.get("data_design"));
        dataMap.put("ext_config", JSONUtil.toJsonStr(params.get("ext_config")));
        dataMap.put("is_anony", 0);
        dataMap.put("is_share", 0);
        dataMap.put("share_auto_approve", 0);
        dataMap.put("company_id", StrUtil.EMPTY);
        dataMap.put("creator", username);
        dataMap.put("upuser", "");
        dataMap.put("input_time", "");
        dataMap.put("open_invent", 0);
        dataMap.put("data_state", JSONUtil.createObj().set("status", "启用").toString());
        return dataMap;
    }

    private Map<String, Object> dataStatusHandle(Map<String, Object> dataStatusMap, Integer key, String value, String wfCode) {
        dataStatusMap.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        dataStatusMap.put("source_code", wfCode);
        dataStatusMap.put("source_type", 1);
        dataStatusMap.put("status_type", "审批");
        dataStatusMap.put("status_name", value);
        dataStatusMap.put("status_value", key);
        return dataStatusMap;
    }

    public Object deactivate(List<String> wfCodes, Map<String, Object> params) {
        if (Objects.isNull(params.get("status")) || StrUtil.isBlank(params.get("status").toString())) {
            throw new RuntimeException("状态不可为空");
        }
        String status = params.get("status").toString();
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, Object> condition = new HashMap<>();
        JSONObject obj = JSONUtil.createObj();
        if (CollectionUtil.isNotEmpty(wfCodes)) {
            int total = 0;
            if (Objects.equals(status, ENABLE)) {
                for (String wfCode : wfCodes) {
                    dataMap.put("data_state", obj.set("status", ENABLE).toString());
                    condition.put("wf_code", wfCode);
                    int update = genericMapper.update(NODE_EXT_WF, dataMap, condition);
                    total += update;
                    logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_WORKFLOW, "启用流程", dataMap, wfCode);
                }
                return total;
            }
            if (Objects.equals(status, DISABLE)) {
                String now = DateUtil.now();
                for (String wfCode : wfCodes) {
                    dataMap.put("data_state", obj
                            .set("status", DISABLE)
                            .set("deleted_time", now)
                            .toString());
                    condition.put("wf_code", wfCode);
                    int update = genericMapper.update(NODE_EXT_WF, dataMap, condition);
                    total += update;
                    logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_WORKFLOW, "停用流程", dataMap, wfCode);
                }
                return total;
            }
            throw new RuntimeException("状态错误");
        }
        return 0;
    }

    /**
     * 将工作流格式转换为表单
     *
     * @param params 包含wf_code和node_id的参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Object formatTrans(Map<String, Object> params) {
        // 参数验证
        if (!params.containsKey("wf_code") || !params.containsKey("node_id")) {
            throw new RuntimeException("缺少必要参数: wf_code或node_id");
        }

        String wfCode = params.get("wf_code").toString();
        String nodeId = params.get("node_id").toString();
        String dataCode = wfCode;

        try {
            // 更新表单类型
            namedParameterJdbcTemplate.update(
                    "update node_ext_data set data_type = 0 where data_code = :wf_code",
                    Map.of("wf_code", wfCode)
            );

            // 更新节点数据关系
            namedParameterJdbcTemplate.update(
                    "update pso_node_data set node_id = :node_id, node_data_tag = :tag where node_data_id = :data_id",
                    Map.of(
                            "node_id", nodeId,
                            "tag", "node_ext_data",
                            "data_id", wfCode
                    )
            );

            // 更新各种配置表中的source_type为0
            String[] configTables = {
                    "DATA_VIEW_CONFIG", "DATA_BUTTON_CONFIG", "DATA_PRINT_CONFIG",
                    "DATA_RULE_CONFIG", "DATA_SERVICE_CONFIG", "DATA_DETAIL_CONFIG",
                    "DATA_STATUS_CONFIG"
            };

            for (String table : configTables) {
                namedParameterJdbcTemplate.update(
                        "update " + table + " set source_type = 0 where source_code = :code",
                        Map.of("code", wfCode)
                );
            }

            // 更新权限
            namedParameterJdbcTemplate.update(
                    "update role_auth_show set auth_type = 2 where map_id = :code and auth_type = 3",
                    Map.of("code", dataCode)
            );

            namedParameterJdbcTemplate.update(
                    "update role_auth_op set auth_type = 2 where tp_id = :code and auth_type = 3",
                    Map.of("code", dataCode)
            );

            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_WORKFLOW, "转换为表单", null, wfCode);
            return 1;
        } catch (Exception e) {
            throw new RuntimeException("转换流程为表单失败: " + e.getMessage(), e);
        }
    }

    public Object flowInfo(String wfCode) {
        return namedParameterJdbcTemplate.queryForMap(
                "select * from node_ext_wf where wf_code = :wf_code",
                Map.of("wf_code", wfCode));
    }

    /**
     * 获取流程详细信息（包括表单数据、流程数据、状态数据等）
     *
     * @return 流程详细信息
     */
    public Map<String, Object> info(String dataCode) {
        Map<String, Object> result = new HashMap<>();
        if (StrUtil.isNotBlank(dataCode)) {
            List<String> codeList = new ArrayList<>();
            List<String> flowList = new ArrayList<>();

            codeList.add(dataCode);
            flowList.add(dataCode);

            // 获取表单信息
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("codes", codeList);
            String sql = "select * from node_ext_data where data_code in (:codes)";
            List<Map<String, Object>> dataList = namedParameterJdbcTemplate.queryForList(sql, paramMap);
            Map<String, Object> dataMap = dataList.get(0);

            // 获取流程信息
            if (!flowList.isEmpty()) {
                paramMap = new HashMap<>();
                paramMap.put("codes", flowList);
                sql = "select * from node_ext_wf where wf_code in (:codes)";
                List<Map<String, Object>> flowData = namedParameterJdbcTemplate.queryForList(sql, paramMap);
                result.put("flow", flowData);
            }

            // 获取状态卡片数据
            paramMap = new HashMap<>();
            paramMap.put("sourceCodes", codeList);
            sql = "select ta.*, tb.data_name, tb.data_sname from DATA_STATUS_CONFIG ta " +
                    "left join (select data_code, data_name, data_sname from node_ext_data " +
                    "union all select view_code, view_name, view_sname from node_ext_view) tb " +
                    "on ta.source_code = tb.data_code " +
                    "where ta.source_code in (:sourceCodes) " +
                    "order by ta.status_type, ta.status_value";
            List<Map<String, Object>> statusData = namedParameterJdbcTemplate.queryForList(sql, paramMap);
            dataMap.put("status", statusData);

            // 获取规则和服务数据
            if (!codeList.isEmpty()) {
                // 获取规则数据
                sql = "select ta.*, tb.data_name, tb.data_sname from DATA_RULE_CONFIG ta " +
                        "left join (select data_code, data_name, data_sname from node_ext_data " +
                        "union all select view_code, view_name, view_sname from node_ext_view) tb " +
                        "on ta.source_code = tb.data_code " +
                        "where ta.source_code in (:sourceCodes)";
                List<Map<String, Object>> ruleData = namedParameterJdbcTemplate.queryForList(sql, paramMap);
                dataMap.put("rule", ruleData);

                // 获取服务数据
                sql = "select ta.*, tb.data_name, tb.data_sname from DATA_SERVICE_CONFIG ta " +
                        "left join node_ext_data tb on ta.source_code = tb.data_code " +
                        "where ta.source_code in (:sourceCodes) and service_status = 1 " +
                        "and (service_func in ('base', 'packet', 'ai') or (func_type = 0 and service_func = 'method')) " +
                        "order by source_code";
                List<Map<String, Object>> serviceData = namedParameterJdbcTemplate.queryForList(sql, paramMap);
                dataMap.put("service", serviceData);
            }
            result.put("data", dataList);
        }
        return result;
    }

    /**
     * @description 复制流程
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object copy(Map<String, Object> params) {
        if (Objects.isNull(params.get("node_id"))) {
            throw new RuntimeException("流程编码不能为空");
        }

        String sourceCode = params.get("node_id").toString();
        String username = SecurityUtils.getUsername();
        String newCode = IdUtil.getSnowflakeNextIdStr();

        try {
            // 查询原流程信息
            String wfSql = "SELECT * FROM NODE_EXT_WF WHERE wf_code = :code";
            Map<String, Object> wfInfo = namedParameterJdbcTemplate.queryForMap(wfSql, Map.of("code", sourceCode));

            // 查询原数据信息
            String dataSql = "SELECT * FROM NODE_EXT_DATA WHERE data_code = :code";
            Map<String, Object> dataInfo = namedParameterJdbcTemplate.queryForMap(dataSql, Map.of("code", sourceCode));

            // 创建新流程信息
            Map<String, Object> newWfInfo = new HashMap<>();
            newWfInfo.put("app_id", APP_ID);
            newWfInfo.put("wf_code", newCode);
            newWfInfo.put("wf_name", wfInfo.get("wf_name") + "【复制】");
            newWfInfo.put("wf_sname", wfInfo.get("wf_sname") + "【复制】");
            newWfInfo.put("map_data_code", wfInfo.get("map_data_code").equals(sourceCode) ? newCode : wfInfo.get("map_data_code"));
            newWfInfo.put("wf_map_tp", wfInfo.get("wf_map_tp"));
            newWfInfo.put("wf_auth_type", wfInfo.get("wf_auth_type"));
            newWfInfo.put("empty_type", wfInfo.get("empty_type"));
            newWfInfo.put("wf_order", 255);
            newWfInfo.put("wf_matter_able", wfInfo.get("wf_matter_able"));
            newWfInfo.put("wf_urge_able", wfInfo.get("wf_urge_able"));
            newWfInfo.put("wf_comment_able", wfInfo.get("wf_comment_able"));
            newWfInfo.put("wf_src_able", wfInfo.get("wf_src_able"));
            newWfInfo.put("wf_tags", wfInfo.get("wf_tags"));
            newWfInfo.put("create_time", DateUtil.now());
            newWfInfo.put("creator", username);
            newWfInfo.put("is_pub", 0);
            newWfInfo.put("is_share", 0);
            newWfInfo.put("company_id", StrUtil.nullToEmpty(params.getOrDefault("company_id", "").toString()));
            newWfInfo.put("open_preju", wfInfo.getOrDefault("open_preju", 0));
            newWfInfo.put("data_state", wfInfo.get("data_state").toString());

            // 插入新流程
            genericMapper.create("NODE_EXT_WF", newWfInfo);

            // 如果原流程的map_data_code等于原流程编码，则同时复制数据表
            if (wfInfo.get("map_data_code").equals(sourceCode)) {
                // 创建新数据信息
                Map<String, Object> newDataInfo = new HashMap<>();
                newDataInfo.put("app_id", APP_ID);
                newDataInfo.put("data_code", newCode);
                newDataInfo.put("data_name", dataInfo.get("data_name") + "【复制】");
                newDataInfo.put("data_sname", dataInfo.get("data_sname") + "【复制】");
                newDataInfo.put("data_type", dataInfo.get("data_type"));
                newDataInfo.put("data_design", dataInfo.get("data_design"));
                newDataInfo.put("open_discuss", dataInfo.get("open_discuss"));
                newDataInfo.put("is_anony", dataInfo.get("is_anony"));
                newDataInfo.put("create_time", DateUtil.now());
                newDataInfo.put("is_share", 0);
                newDataInfo.put("open_invent", 0);
                newDataInfo.put("creator", username);
                newDataInfo.put("data_status", 0);
                newDataInfo.put("ext_config", dataInfo.get("ext_config"));
                newDataInfo.put("data_state", JSONUtil.createObj().set("status", "启用").toString());
                // 插入新数据
                genericMapper.create("NODE_EXT_DATA", newDataInfo);
            }

            // 添加节点数据关联
            if (params.containsKey("node_pid") && StrUtil.isNotBlank(params.get("node_pid").toString())) {
                Map<String, Object> nodeData = new HashMap<>();
                nodeData.put("node_id", params.get("node_pid"));
                nodeData.put("node_data_id", newCode);
                nodeData.put("node_data_tag", "node_ext_wf");
                nodeData.put("data_order", DEFAULT_ORDER_VALUE);
                genericMapper.create("PSO_NODE_DATA", nodeData);
            }

            // 记录日志
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_WORKFLOW, "复制流程", newWfInfo, null);
            return newCode;
        } catch (Exception e) {
            throw new RuntimeException("复制流程失败: " + e.getMessage(), e);
        }
    }
}
