package com.ruoyi.core.service;

import com.ruoyi.core.repository.EnumRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class EnumService {
    private final EnumRepository enumRepository;

    public EnumService(EnumRepository enumRepository) {
        this.enumRepository = enumRepository;
    }

    /**
     * 表单枚举
     *
     * @param enumCode 枚举code
     */
    public List<Map<String, Object>> getEnumListByIn(List<String> enumCode) {
        return enumRepository.getEnumListByIn(enumCode);
    }
}
