package com.ruoyi.core.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.mapper.GenericMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ruoyi.core.service.LogService.MODULE_OTHER_MEDICAL_ORDER;

@Slf4j
@Service
@RequiredArgsConstructor
public class OutStopMedicineService {

    private final GenericMapper genericMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final LogService logService;
    private final MedicineOrderService medicineOrderService;

    /**
     * @description 批量新增外出停药记录
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object batchAdd(List<Map<String, Object>> paramsList) {
        if (CollectionUtil.isEmpty(paramsList)) {
            return AjaxResult.error("批量新增数据不能为空");
        }

        String username = SecurityUtils.getUsername();
        List<String> successIds = new ArrayList<>();
        List<String> failedItems = new ArrayList<>();

        for (int i = 0; i < paramsList.size(); i++) {
            try {
                Map<String, Object> params = paramsList.get(i);
                String id = IdUtil.getSnowflakeNextIdStr();
                Map<String, Object> map = new HashMap<>(params);
                map.put("id", id);
                map.put("created_by", username);
                map.put("status", 1);
                map.put("stopped_date", DateUtil.today());

                // 新增外出停药记录
                genericMapper.create("out_stop_medicine", map);
                successIds.add(id);

                // 记录操作日志
                logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_OTHER_MEDICAL_ORDER,
                        "批量新增外出停药记录-第" + (i + 1) + "条", map, null);

            } catch (Exception e) {
                failedItems.add("第" + (i + 1) + "条记录: " + e.getMessage());
            }
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", paramsList.size());
        result.put("success", successIds.size());
        result.put("failed", failedItems.size());
        result.put("successIds", successIds);

        if (!failedItems.isEmpty()) {
            result.put("failedDetails", failedItems);
        }

        if (failedItems.isEmpty()) {
            return AjaxResult.success("批量添加成功，共添加 " + successIds.size() + " 条记录", result);
        } else if (successIds.isEmpty()) {
            return AjaxResult.error("批量添加失败，所有记录都添加失败", result);
        } else {
            return AjaxResult.warn("批量添加部分成功，成功 " + successIds.size() + " 条，失败 " + failedItems.size() + " 条", result);
        }
    }


    /**
     * @description 删除外出停药记录
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object delete(String id) {
        String username = SecurityUtils.getUsername();
        Map<String, Object> updateValues = new HashMap<>();
        updateValues.put("is_deleted", 1);
        updateValues.put("updated_by", username);

        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        condition.put("is_deleted", 0);

        int result = genericMapper.update("out_stop_medicine", updateValues, condition);
        if (result > 0) {
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_OTHER_MEDICAL_ORDER, "删除外出停药记录", Map.of("id", id), null);
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.error("删除失败，记录不存在或已被删除");
        }
    }

    /**
     * @description 根据ID获取外出停药记录详情
     * <AUTHOR>
     */
    public Object getDetailById(String id) {
        if (StrUtil.isBlank(id)) {
            return AjaxResult.error("ID不能为空");
        }

        String sql = selectAllSql() + " WHERE id = :id AND is_deleted = 0";
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);

        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);

            // 添加状态标签
            Object statusObj = result.get("status");
            if (statusObj != null) {
                int status = Integer.parseInt(statusObj.toString());
                result.put("status_label", status == 1 ? "停药" : "回院");
            } else {
                result.put("status_label", "未知状态");
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("未找到对应的外出停药记录");
        }
    }

    /**
     * @description 分页查询外出停药记录列表
     * <AUTHOR>
     */
    public Object getPageList(Integer pageNum, Integer pageSize, String patientName, String hospitalizationId, String bedNumber) {
        StringBuilder sql = new StringBuilder(selectAllSql());
        sql.append(" WHERE is_deleted = 0");

        Map<String, Object> params = new HashMap<>();

        // 添加查询条件
        if (StrUtil.isNotBlank(patientName)) {
            sql.append(" AND patient_name LIKE :patientName");
            params.put("patientName", "%" + patientName + "%");
        }
        if (StrUtil.isNotBlank(hospitalizationId)) {
            sql.append(" AND hospitalization_id LIKE :hospitalizationId");
            params.put("hospitalizationId", "%" + hospitalizationId + "%");
        }
        if (StrUtil.isNotBlank(bedNumber)) {
            sql.append(" AND bed_number LIKE :bedNumber");
            params.put("bedNumber", "%" + bedNumber + "%");
        }

        // 计算总数
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") AS count_table";
        Integer total = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);

        // 添加排序和分页
        sql.append(" ORDER BY stopped_date DESC, id DESC");
        int offset = (pageNum - 1) * pageSize;
        sql.append(" LIMIT :offset, :pageSize");
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql.toString(), params);

        // 处理状态标签
        if (CollectionUtil.isNotEmpty(list)) {
            for (Map<String, Object> item : list) {
                Object statusObj = item.get("status");
                if (statusObj != null) {
                    int status = Integer.parseInt(statusObj.toString());
                    item.put("status_label", status == 1 ? "停药" : "回院");
                } else {
                    item.put("status_label", "未知状态");
                }
            }
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "查询成功");
        result.put("total", total != null ? total : 0);
        result.put("rows", list);

        return result;
    }

    /**
     * @description 根据住院号查询病人详情
     * <AUTHOR>
     */
    public Object getPatientDetailByHospitalizationId(String hospitalizationId) {
        if (StrUtil.isBlank(hospitalizationId)) {
            return AjaxResult.error("住院号不能为空");
        }

        String sql = """
                SELECT
                    leaf_id,
                    old_name AS patient_name,
                    old_num AS hospitalization_id,
                    old_age AS age,
                    old_sex AS gender,
                    old_bed AS bed_number,
                    bed_name,
                    class_name AS ward_name,
                    area_name,
                    enter_time,
                    nurse_level,
                    food_sort,
                    nurse_special,
                    old_diagnose AS diagnosis,
                    room_num,
                    attend_man AS attending_doctor,
                    doctor_man AS doctor,
                    nurse_man AS nurse,
                    old_birth AS birth_date,
                    old_photo AS photo_url,
                    '' as yibaolx
                FROM data_ext_p000001321020140
                WHERE old_num = :hospitalizationId
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("hospitalizationId", hospitalizationId);

        try {
            List<Map<String, Object>> results = namedParameterJdbcTemplate.queryForList(sql, params);

            if (results.isEmpty()) {
                return AjaxResult.error("未找到住院号为 " + hospitalizationId + " 的病人信息");
            }

            // 如果有多条记录，返回第一条（通常住院号应该是唯一的）
            Map<String, Object> patientInfo = results.get(0);

            // 处理性别显示
            Object genderObj = patientInfo.get("gender");
            if (genderObj != null) {
                String gender = genderObj.toString();
                patientInfo.put("gender_label", "男".equals(gender) ? "男" : "女".equals(gender) ? "女" : "未知");
            } else {
                patientInfo.put("gender_label", "未知");
            }

            // 处理年龄显示
            Object ageObj = patientInfo.get("age");
            if (ageObj != null) {
                patientInfo.put("age_label", ageObj.toString() + "岁");
            } else {
                patientInfo.put("age_label", "未知");
            }

            return AjaxResult.success(patientInfo);

        } catch (Exception e) {
            return AjaxResult.error("查询病人信息时发生错误：" + e.getMessage());
        }
    }

    /**
     * @description 根据住院号查询长期医嘱（用药医嘱和外配药医嘱）
     * <AUTHOR>
     */
    public Object getMedicationOrdersByHospitalizationId(String hospitalizationId) {
        if (StrUtil.isBlank(hospitalizationId)) {
            return AjaxResult.error("住院号不能为空");
        }

        try {
            // 查询用药医嘱
            List<Map<String, Object>> medicationOrders = getMedicationOrdersFromDB(hospitalizationId);

            // 查询外配药医嘱
            List<Map<String, Object>> externalRxOrders = getExternalRxOrdersFromDB(hospitalizationId);

            // 合并结果
            List<Map<String, Object>> allOrders = new ArrayList<>();
            allOrders.addAll(medicationOrders);
            allOrders.addAll(externalRxOrders);

            return AjaxResult.success(allOrders);

        } catch (Exception e) {
            return AjaxResult.error("查询医嘱信息时发生错误：" + e.getMessage());
        }
    }

    /**
     * @description 从数据库查询用药医嘱（排除已停药的医嘱）
     * <AUTHOR>
     */
    private List<Map<String, Object>> getMedicationOrdersFromDB(String hospitalizationId) {
        String sql = """
                SELECT
                    mo.id,
                    mo.drug_name,
                    mo.drug_spec,
                    mo.drug_unit,
                    mo.administration_route,
                    mo.administration_method,
                    mo.single_dose,
                    mo.order_date,
                    '用药医嘱' as type
                FROM medication_order mo
                INNER JOIN doctor_order do ON mo.id = do.medication_order_id
                LEFT JOIN out_stop_medicine osm ON mo.id = osm.medication_order_id
                    AND osm.hospitalization_id = :hospitalizationId
                    AND osm.status = 1
                    AND osm.is_deleted = 0
                WHERE do.hospitalization_id = :hospitalizationId
                AND do.order_type = '长期'
                AND do.status = 2
                AND do.order_name = '用药医嘱'
                AND mo.is_deleted = 0
                AND do.is_deleted = 0
                AND osm.id IS NULL
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("hospitalizationId", hospitalizationId);

        return namedParameterJdbcTemplate.queryForList(sql, params);
    }

    /**
     * @description 从数据库查询外配药医嘱（排除已停药的医嘱）
     * <AUTHOR>
     */
    private List<Map<String, Object>> getExternalRxOrdersFromDB(String hospitalizationId) {
        String sql = """
                SELECT
                    er.id,
                    er.drug_name,
                    er.drug_spec,
                    er.drug_unit,
                    er.administration_route,
                    er.administration_method,
                    er.single_dose,
                    er.order_date,
                    '外配药医嘱' as type
                FROM external_rx er
                INNER JOIN doctor_order do ON er.id = do.medication_order_id
                LEFT JOIN out_stop_medicine osm ON er.id = osm.medication_order_id
                    AND osm.hospitalization_id = :hospitalizationId
                    AND osm.status = 1
                    AND osm.is_deleted = 0
                WHERE do.hospitalization_id = :hospitalizationId
                AND do.order_type = '长期'
                AND do.status = 2
                AND do.order_name = '外配药医嘱'
                AND er.is_deleted = 0
                AND do.is_deleted = 0
                AND osm.id IS NULL
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("hospitalizationId", hospitalizationId);

        return namedParameterJdbcTemplate.queryForList(sql, params);
    }

    /**
     * @description 查询所有字段的SQL
     * <AUTHOR>
     */
    private String selectAllSql() {
        return """
                SELECT
                    id,
                    medication_order_id,
                    area,
                    bed_number,
                    hospitalization_id,
                    patient_name,
                    status,
                    drug_name,
                    drug_spec,
                    single_dose,
                    order_name,
                    daily_usage,
                    stopped_date,
                    recover_date,
                    is_deleted,
                    created_by,
                    created_time,
                    updated_by,
                    updated_time
                FROM out_stop_medicine
                """;
    }

    /**
     * @description 回院接口 - 根据主键id处理回院逻辑
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object returnHospital(String id) {
        try {
            // 1. 根据主键id查询out_stop_medicine表中的order_name和medication_order_id
            String queryOrderNameSql = """
                    SELECT order_name, medication_order_id, stopped_date
                    FROM out_stop_medicine
                    WHERE id = :id AND is_deleted = 0
                    """;

            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("id", id);

            Map<String, Object> orderInfo;
            try {
                orderInfo = namedParameterJdbcTemplate.queryForMap(queryOrderNameSql, queryParams);
            } catch (Exception e) {
                return AjaxResult.error("未找到对应的外出停药记录");
            }

            String orderName = (String) orderInfo.get("order_name");
            String medicationOrderId = (String) orderInfo.get("medication_order_id");
            Object stoppedDateObj = orderInfo.get("stopped_date");

            if (orderName == null) {
                return AjaxResult.error("医嘱名称为空");
            }

            if (medicationOrderId == null) {
                return AjaxResult.error("医嘱ID为空");
            }

            // 2. 更新out_stop_medicine表的recover_date为当天，status为2（回院状态）
            Map<String, Object> updateParams = new HashMap<>();
            updateParams.put("id", id);
            updateParams.put("updated_by", SecurityUtils.getUsername());
            String updateRecoverDateSql = """
                    UPDATE out_stop_medicine
                    SET recover_date = CURDATE(),
                        status = 2,
                        updated_by = :updated_by,
                        updated_time = NOW()
                    WHERE id = :id AND is_deleted = 0
                    """;

            int recoverUpdateCount = namedParameterJdbcTemplate.update(updateRecoverDateSql, updateParams);
            if (recoverUpdateCount == 0) {
                return AjaxResult.error("更新外出停药记录失败");
            }

            // 3. 如果是用药医嘱，需要基于剩余药量重新计算预计到期日期
            if ("用药医嘱".equals(orderName.trim())) {
                try {
                    if (stoppedDateObj != null) {
                        String stoppedDate = stoppedDateObj.toString();
                        String todayDate = DateUtil.today();

                        // 调用MedicineOrderService中的方法基于剩余药量重新计算预计到期日期
                        medicineOrderService.recalculateEstimatedExpirationDateAfterReturn(
                            Long.valueOf(medicationOrderId), stoppedDate, todayDate);
                    } else {
                        log.warn("未找到停药日期，无法重新计算预计到期日期，medication_order_id: {}", medicationOrderId);
                    }
                } catch (Exception e) {
                    // 如果重新计算预计到期日期失败，记录日志但不影响主流程
                    log.warn("重新计算预计到期日期失败: {}", e.getMessage());
                }
            }

            // 记录日志
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_OTHER_MEDICAL_ORDER, "医嘱回院", Map.of("id", id), null);

            return AjaxResult.success("回院处理成功");

        } catch (Exception e) {
            log.error("回院处理失败: {}", e.getMessage(), e);
            return AjaxResult.error("回院处理失败: " + e.getMessage());
        }
    }
}
