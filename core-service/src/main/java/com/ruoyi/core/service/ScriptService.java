package com.ruoyi.core.service;

import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ScriptService {
    private final GenericMapper genericMapper;

    public ScriptService(GenericMapper genericMapper) {
        this.genericMapper = genericMapper;
    }

    public Map<String, Object> getScriptByDriver(String driverID) {
        List<Map<String, Object>> driverList = genericMapper.getList(
                "pso_script_driver",
                "*",
                List.<String[]>of(new String[]{"equal", "driver_id", driverID}),
                ""
        );
        if (driverList.isEmpty()) {
            driverList = List.of();
        }

        List<Map<String, Object>> syntaxList = genericMapper.getList(
                "pso_script_syntax",
                "*",
                List.<String[]>of(new String[]{"equal", "driver_id", driverID}),
                ""
        );
        if (syntaxList.isEmpty()) {
            syntaxList = List.of();
        }

        List<Map<String, Object>> paramsList = genericMapper.getList(
                "pso_script_params",
                "*",
                List.<String[]>of(new String[]{"equal", "driver_id", driverID}),
                ""
        );
        if (paramsList.isEmpty()) {
            paramsList = List.of();
        }

        return Map.of(
                "driver", driverList,
                "syntax", syntaxList,
                "params", paramsList
        );
    }

    public String GetScriptSql(String driverID){
        List<Map<String, Object>> syntaxList = genericMapper.getList(
                "pso_script_syntax",
                "*",
                List.<String[]>of(new String[]{"equal", "driver_id", driverID}),
                ""
        );
        List<Map<String, Object>> viewSyntax=syntaxList.stream().filter(t->t.get("sp_type").toString().equals("action") && t.get("action_type").toString().equals("select"))
                .toList();
        if (viewSyntax.isEmpty()) return "";
        return viewSyntax.get(0).get("script_config").toString();
    }
}
