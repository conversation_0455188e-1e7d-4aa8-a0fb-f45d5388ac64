package com.ruoyi.core.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.mapper.GenericMapper;
import com.ruoyi.core.mapper.PatientMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.core.constants.PsoConstant.APP_ID;
import static com.ruoyi.core.service.LogService.*;
import static com.ruoyi.core.sqltemplate.SqlTemplate.initMedicalRecordSql;


@Slf4j
@Service
public class PatientService {
    public static final String TABLE_MENZBL = "data_ext_menzbl";
    public static final String TALBE_HUAYJGXX = "data_ext_huayjgxx";
    private final GenericMapper genericMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final PatientMapper patientMapper;
    private final LogService logService;
    // 门诊病历表名
    private static final String TABLE_NAME = "data_ext_menzbl";

    public PatientService(GenericMapper genericMapper, NamedParameterJdbcTemplate namedParameterJdbcTemplate,
                          PatientMapper patientMapper, LogService logService) {
        this.genericMapper = genericMapper;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.patientMapper = patientMapper;
        this.logService = logService;
    }

    /**
     * @description 门诊病历生成
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object add(Map<String, Object> params) {
        String username = SecurityUtils.getUsername();
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> map = new HashMap<>(params);
        Long recordId = IdUtil.getSnowflakeNextId();
        map.put("leaf_id", recordId);
        map.put("creator", userId);
        map.put("x_creator", username);
        map.put("c_time", DateUtil.now());
        map.put("app_id", APP_ID);
        map.put("data_code", "MenZBL");
        map.put("d_status", 0);
        map.put("d_stage", 0);
        map.put("d_audit", 0);
        map.put("sync_status", 0);
        map.put("is_chain", 0);
        map.put("pub_status", 0);
        map.put("share_status", 0);
        map.put("zhenl_no", DateUtil.today() + "-" + IdUtil.fastSimpleUUID());
        genericMapper.create(TABLE_NAME, map);
        logService.saveLog(userId.toString(), MODULE_PATIENT_RECORD, "添加门诊简历", map, null);
        return recordId;
    }

    /**
     * @description 获取所有部门为“卫生所”的人员，作为带教医生的选项
     * <AUTHOR>
     */
    public Object getSupervisors() {
        Long deptId;
        try {
            String deptSql = "select dept_id from sys_dept where dept_name = :dept_name";
            deptId = namedParameterJdbcTemplate.queryForObject(deptSql,
                    Map.of("dept_name", "卫生所"), Long.class);
        } catch (Exception e) {
            return Collections.EMPTY_LIST;
        }
        String sql = "select CAST(user_id AS CHAR) as user_id,user_name from sys_user where dept_id = :dept_id";
        List<Map<String, Object>> result = namedParameterJdbcTemplate.queryForList(sql, Map.of("dept_id", deptId));
        if (CollectionUtil.isEmpty(result)) {
            return List.of();
        }
        return result;
    }

    /**
     * @description 分页列表
     * <AUTHOR>
     */
    public List<Map<String, Object>> getPatientList(String zhuyuanh, String xingming, String chuangwNo) {
        return patientMapper.getPatientList(zhuyuanh, xingming, chuangwNo);
    }

    /**
     * @description 编辑门诊病历
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object update(String id, Map<String, Object> params) {
        Map<String, Object> map = new HashMap<>(params);
        Long userId = SecurityUtils.getUserId();
        map.put("upuser", userId);
        map.put("x_upuser", SecurityUtils.getUsername());
        map.put("input_time", DateUtil.now());
        genericMapper.update(TABLE_NAME, map, Map.of("leaf_id", id));
        logService.saveLog(userId.toString(), MODULE_PATIENT_RECORD, "修改门诊简历", params, id);
        return id;
    }

    /**
     * @description 门诊病历详情
     * <AUTHOR>
     */
    public Object getPatientById(String id) {
        String sql = "select * from " + TABLE_NAME + " where leaf_id = :leaf_id";
        Map<String, Object> params = new HashMap<>();
        params.put("leaf_id", id);
        Map<String, Object> resultMap = namedParameterJdbcTemplate.queryForMap(sql, params);
        String oldSql = """
                SELECT
                leaf_id
                FROM
                data_ext_p000001321020140
                where old_name = :old_name and old_num = :old_num
                """;
        Map<String, Object> oldParams = new HashMap<>();
        oldParams.put("old_name", resultMap.get("xingming").toString());
        oldParams.put("old_num", resultMap.get("zhuyuanh").toString());
        Map<String, Object> oldMap = namedParameterJdbcTemplate.queryForMap(oldSql, oldParams);
        resultMap.put("oldId", oldMap.get("leaf_id").toString());
        return resultMap;
    }

    /**
     * @description 删除门诊病历
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<String> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            String sql = " DELETE FROM " + TABLE_NAME + " WHERE leaf_id IN (:ids)";
            Map<String, Object> params = new HashMap<>();
            params.put("ids", ids);
            namedParameterJdbcTemplate.update(sql, params);
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_PATIENT_RECORD, "删除门诊简历", params, ids);
        }
    }

    /**
     * 创建默认的分页返回结果
     *
     * @return 包含默认值的Map对象
     */
    private Map<String, Object> createDefaultPageResult() {
        Map<String, Object> countMap = new HashMap<>();
        countMap.put("code", 200);
        countMap.put("rows", Collections.EMPTY_LIST);
        countMap.put("total", 0);
        return countMap;
    }

    /**
     * @description 获取所有患者信息
     * <AUTHOR>
     */
    public Object getPatientList(Integer pageNum, Integer pageSize, String query) {
        StringBuilder sql = new StringBuilder("""
                SELECT
                    leaf_id,
                    CONCAT(old_name, '|', old_bed, '|', old_num) AS patientInfo
                FROM
                    data_ext_p000001321020140
                where 1=1
                """);
        Map<String, Object> params = new HashMap<>();
        if (StrUtil.isNotBlank(query)) {
            sql.append(" and CONCAT(old_name, old_bed, old_num) LIKE :query");
            params.put("query", "%" + query + "%");
        }
        String countSql = "select count(*) from (" + sql + ") as count_table";
        sql.append(" order by enter_time desc,leaf_id desc");
        int offset = (pageNum - 1) * pageSize;
        sql.append(" limit " + offset + ", " + pageSize);
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql.toString(), params);
        if (CollectionUtil.isEmpty(list)) {
            return createDefaultPageResult();
        }
        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        Map<String, Object> countMap = createDefaultPageResult();
        countMap.replace("rows", list);
        countMap.replace("total", count);
        return countMap;
    }

    /**
     * @description 根据老人id获取老人信息
     * <AUTHOR>
     */
    public Object getPatientInfo(String id) {
        String sql = """
                select leaf_id,old_name as xingmin, old_bed as chuangw_no, old_num as zhuyuanh, old_age as nianling,
                old_sex as xingbie, class_name as quy,'' as yibaolx from data_ext_p000001321020140
                where leaf_id = :id
                """;
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        Map<String, Object> map = namedParameterJdbcTemplate.queryForMap(sql, params);
        String selectSql = """
                select jiws from data_ext_menzbl where zhuyuanh = :zhuyuanh and xingming = :xingming
                order by c_time desc,leaf_id desc
                """;
        Map<String, Object> selectMap = new HashMap<>();
        selectMap.put("zhuyuanh", map.get("zhuyuanh").toString());
        selectMap.put("xingming", map.get("xingmin").toString());
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(selectSql, selectMap);
        if (CollectionUtil.isNotEmpty(list)) {
            map.put("jiws", list.get(0).get("jiws").toString());
        } else {
            String getSql = """
                    select jwshb from data_ext_bingssc where zhuyuanh = :zhuyuanh and xingming = :xingming
                    order by c_time desc,leaf_id desc
                    """;
            List<Map<String, Object>> getList = namedParameterJdbcTemplate.queryForList(getSql, selectMap);
            if (CollectionUtil.isNotEmpty(getList)) {
                map.put("jiws", getList.get(0).get("jwshb").toString());
            } else {
                map.put("jiws", StrUtil.EMPTY);
            }
        }
        String oldId = map.get("leaf_id").toString();
        String bloodCountSql = """
                select count(*) from data_ext_p000000078081698 where oldman = :id
                """;
        try {
            Integer count = namedParameterJdbcTemplate.queryForObject(bloodCountSql, Map.of("id", oldId), Integer.class);
            map.put("bloodPressureFlag", count > 0);
        } catch (EmptyResultDataAccessException e) {
            map.put("bloodPressureFlag", false);
        }
        String temperatureSql = """
                select count(*) from data_ext_tiwjllb where oldname = :oldName and zyno = :zyno
                """;
        try {
            Integer count = namedParameterJdbcTemplate.queryForObject(temperatureSql, Map.of("oldName", map.get("xingmin").toString(), "zyno", map.get("zhuyuanh").toString()), Integer.class);
            map.put("temperatureFlag", count > 0);
        } catch (EmptyResultDataAccessException e) {
            map.put("temperatureFlag", false);
        }
        return map;
    }

    /**
     * @description 根据姓名和住院号获取患者的化验结果
     * <AUTHOR>
     */
    public Object getLabResults(Integer pageNum, Integer pageSize, String xingming, String zhuyuanh, Integer dateRange, String query) {
        if (StrUtil.isBlank(xingming) || StrUtil.isBlank(zhuyuanh)) {
            return createDefaultPageResult();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("zhuyh", zhuyuanh);
        params.put("xingm", xingming);
        int offset = (pageNum - 1) * pageSize;
        StringBuilder sql = new StringBuilder("""
                select leaf_id,xiangmmc,jieg,danw,jiancsj from data_ext_huayjgxx where zhuyh = :zhuyh and xingm = :xingm
                """);
        if (StrUtil.isNotBlank(query)) {
            sql.append(" and (xiangmmc like :query)");
            params.put("query", "%" + query + "%");
        }
        if (dateRange != 0) {
            sql.append(" and jiancsj >= CURDATE() - INTERVAL :dateRange DAY");
            params.put("dateRange", dateRange);
        }
        sql.append(" order by jiancsj desc,leaf_id desc");
        String countSql = "select count(*) from (" + sql + ") as count_table";
        sql.append(" limit " + offset + ", " + pageSize);
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql.toString(), params);
        if (CollectionUtil.isEmpty(list)) {
            return createDefaultPageResult();
        }
        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        Map<String, Object> map = createDefaultPageResult();
        map.replace("rows", list);
        map.replace("total", count);
        return map;
    }

    /**
     * @description 拼接化验结果
     * <AUTHOR>
     */
    public Object spliceLabResults(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return StrUtil.EMPTY;
        }
        String sql = """
                select xiangmmc,jieg,danw from data_ext_huayjgxx where leaf_id in (:ids)
                """;
        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);
        if (CollectionUtil.isEmpty(list)) {
            return StrUtil.EMPTY;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            stringBuilder.append(list.get(i).get("xiangmmc"))
                    .append(":")
                    .append(list.get(i).get("jieg"))
                    .append(StrUtil.toStringOrNull(list.get(i).get("danw")));
            if (i != list.size() - 1) {
                stringBuilder.append("，");
            } else {
                stringBuilder.append("。");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * @description 获取疾病诊断信息
     * <AUTHOR>
     */
    public Object getClinicalDiagnosis() {
        String sql = """
                select leaf_id,zdmc from data_ext_linczd order by c_time desc,leaf_id desc
                """;
        return namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
    }

    /**
     * @description 根据模板名称获取模板
     * <AUTHOR>
     */
    public Object getPatientTemplate(Integer pageNum, Integer pageSize, String query) {
        StringBuilder sql = new StringBuilder("select leaf_id,mubmc from data_ext_binlmb where 1=1");
        Map<String, Object> params = new HashMap<>();
        if (StrUtil.isNotBlank(query)) {
            sql.append(" and mubmc like :query");
            params.put("query", "%" + query + "%");
        }
        String countSql = "select count(*) from (" + sql + ") as count_table";
        sql.append(" order by c_time desc,leaf_id desc");
        int offset = (pageNum - 1) * pageSize;
        sql.append(" limit " + offset + ", " + pageSize);
        Map<String, Object> countMap = createDefaultPageResult();
        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql.toString(), params);
        if (CollectionUtil.isEmpty(list)) {
            return countMap;
        }
        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        countMap.put("rows", list);
        countMap.put("total", count);
        return countMap;
    }

    /**
     * @description 根据模板ID获取病历模板
     * <AUTHOR>
     */
    public Object getTemplateById(String id) {
        String sql = """
                select xianbs,tijqk,zhusu from data_ext_binlmb where leaf_id = :id
                """;
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        return namedParameterJdbcTemplate.queryForMap(sql, params);
    }

    /**
     * @description 生成病程录
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object saveMedicalRecord(Map<String, Object> params) {
        Long userId = SecurityUtils.getUserId();
        String username = SecurityUtils.getUsername();
        Map<String, Object> map = new HashMap<>(params);
        StringBuilder stringBuilder = new StringBuilder();
        if (StrUtil.isNotBlank(map.get("xianbs").toString())) {
            stringBuilder.append(map.get("xianbs").toString());
        }
        if (StrUtil.isNotBlank(map.get("tijqk").toString())) {
            stringBuilder.append(map.get("tijqk").toString());
        }
        map.put("bingclhb", stringBuilder.isEmpty() ? StrUtil.EMPTY : stringBuilder.toString());
        map.remove("isYear");
        // 新增
        String id = IdUtil.getSnowflakeNextIdStr();
        map.put("leaf_id", id);
        map.put("data_code", "BinCLSC");
        map.put("app_id", APP_ID);
        map.put("d_status", 0);
        map.put("d_stage", 0);
        map.put("d_audit", 0);
        map.put("sync_status", 0);
        map.put("is_chain", 0);
        map.put("pub_status", 0);
        map.put("creator", username);
        map.put("c_time", DateUtil.now());
        map.put("share_status", 0);
        String delSql = """
                delete from data_ext_binclsc where guanlmzbl = :guanlmzbl
                """;
        namedParameterJdbcTemplate.update(delSql, Map.of("guanlmzbl", params.get("guanlmzbl").toString()));
        genericMapper.create("data_ext_binclsc", map);
        if (Objects.nonNull(params.get("isYear")) && params.get("isYear").equals("true")) {
            //TODO 插入年度小结表（需求暂未明确）
        }
        logService.saveLog(userId.toString(), MODULE_MEDICAL_RECORD, "添加病程录", map, null);
        return id;
    }

    /**
     * @description 获取病程录模板信息
     * <AUTHOR>
     */
    public Object initMedicalRecord(String id) {
        String sql = initMedicalRecordSql();
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        Map<String, Object> map = namedParameterJdbcTemplate.queryForMap(sql, params);
        StringBuilder stringBuilder = new StringBuilder();
        if (Objects.nonNull(map.get("tijqk")) && StrUtil.isNotBlank(map.get("tijqk").toString())) {
            stringBuilder.append(map.get("tijqk").toString());
        }
        if (Objects.nonNull(map.get("fuzjc")) && StrUtil.isNotBlank(map.get("fuzjc").toString())) {
            stringBuilder.append("实验室检查：").append(map.get("fuzjc"));
        }
        if (Objects.nonNull(map.get("chulyj")) && StrUtil.isNotBlank(map.get("chulyj").toString())) {
            stringBuilder.append(map.get("chulyj").toString()).append("。");
        }
        if (Objects.nonNull(map.get("jibzd")) && StrUtil.isNotBlank(map.get("jibzd").toString())) {
            stringBuilder.append("诊断：").append(map.get("jibzd").toString()).append("。");
        }
        map.replace("tijqk", stringBuilder.isEmpty() ? StrUtil.EMPTY : stringBuilder.toString());
        return map;
    }

    /**
     * @description 根据姓名和住院号获取病人历史病程录
     * <AUTHOR>
     */
    public Object medicalRecordList(String zhuyuanh, String xingming) {
        String sql = """
                select creator,c_time,bingclhb from data_ext_binclsc where zhuyuanh = :zhuyuanh and xingming = :xingming
                order by c_time desc,leaf_id desc
                """;
        Map<String, Object> params = new HashMap<>();
        params.put("zhuyuanh", zhuyuanh);
        params.put("xingming", xingming);
        return namedParameterJdbcTemplate.queryForList(sql, params);
    }

    /**
     * @description 生成就医记录
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object saveDoctorRecord(Map<String, Object> params) {
        String delSql = """
                delete from data_ext_jiuyjlsc where guanlmzbl = :guanlmzbl
                """;
        namedParameterJdbcTemplate.update(delSql, Map.of("guanlmzbl", params.get("id").toString()));
        Map<String, Object> map = new HashMap<>();
        String id = IdUtil.getSnowflakeNextIdStr();
        String username = SecurityUtils.getUsername();
        Long userId = SecurityUtils.getUserId();
        map.put("data_code", "jiuyjlsc");
        map.put("leaf_id", id);
        map.put("creator", userId);
        map.put("x_creator", username);
        map.put("c_time", DateUtil.now());
        map.put("app_id", APP_ID);
        map.put("d_status", 0);
        map.put("d_stage", 0);
        map.put("d_audit", 0);
        map.put("sync_status", 0);
        map.put("is_chain", 0);
        map.put("pub_status", 0);
        map.put("share_status", 0);
        map.put("guanlmzbl", params.get("id").toString());
        map.put("zhuyh", params.get("zhuyuanh").toString());
        map.put("xingm", params.get("xingming").toString());
        map.put("quy", params.get("quy").toString());
        map.put("chuangwh", params.get("chuangw_no").toString());
        map.put("nianl", params.get("nianling").toString());
        map.put("xingb", params.get("xingbie").toString());
        map.put("yiblx", params.getOrDefault("yibaolx", StrUtil.EMPTY));
        map.put("riq", params.get("zhendrq").toString());
        map.put("shij", params.get("zhendsj").toString());
        map.put("keb", params.get("keb").toString());
        map.put("daijys", params.getOrDefault("daijys", StrUtil.EMPTY));
        map.put("zhus", params.get("zhus").toString());
        map.put("xianbs", params.get("xianbs").toString());
        map.put("jiws", params.get("jiws").toString());
        map.put("xuey_up", params.getOrDefault("xuey_up", ""));
        map.put("xuey_down", params.getOrDefault("xuey_down", ""));
        map.put("sheng", params.getOrDefault("sheng", ""));
        map.put("tiz", params.getOrDefault("tiz", ""));
        map.put("BMI", params.getOrDefault("bmi", ""));
        map.put("tijqk", params.getOrDefault("tijqk", ""));
        map.put("fuzjc", params.getOrDefault("fuzjc", ""));
        map.put("chulyj", params.getOrDefault("chulyj", ""));
        map.put("zhend", params.get("linczd1").toString());
        map.put("zhend2", params.getOrDefault("linczd2", StrUtil.EMPTY));
        map.put("zhend3", params.getOrDefault("linczd3", StrUtil.EMPTY));
        map.put("zhend4", params.getOrDefault("linczd4", StrUtil.EMPTY));
        genericMapper.create("data_ext_jiuyjlsc", map);
        logService.saveLog(userId.toString(), MODULE_DOCTOR_RECORD, "新增就医记录", map, null);
        return id;
    }

    /**
     * @description 根据老人id获取血压数据
     * <AUTHOR>
     */
    public Object getBloodPressure(String id, Integer pageNum, Integer pageSize) {
        StringBuilder sql = new StringBuilder("""
                select xy_down,xy_up,recorddate from data_ext_p000000078081698 where oldman = :id
                """);
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        String countSql = "select count(*) from (" + sql + ") as count_table";
        Integer total = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        Map<String, Object> map = createDefaultPageResult();
        map.replace("total", total);
        int offset = (pageNum - 1) * pageSize;
        sql.append(" order by c_time desc,leaf_id desc");
        sql.append(" limit :offset , :pageSize");
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        List<Map<String, Object>> result = namedParameterJdbcTemplate.queryForList(sql.toString(), params);
        map.replace("rows", result);
        return map;
    }

    /**
     * @description 根据老人姓名和住院号获取血压数据
     * <AUTHOR>
     */
    public Object temperature(String oldName, String zyno, Integer pageNum, Integer pageSize) {
        StringBuilder sql = new StringBuilder("""
                select tiwen,jltime,twhour from data_ext_tiwjllb where oldname = :oldName and zyno = :zyno
                """);
        Map<String, Object> params = new HashMap<>();
        params.put("oldName", oldName);
        params.put("zyno", zyno);
        String countSql = "select count(*) from (" + sql + ") as count_table";
        log.info(countSql);
        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        Map<String, Object> map = createDefaultPageResult();
        map.replace("total", count);
        int offset = (pageNum - 1) * pageSize;
        sql.append(" order by c_time desc,leaf_id desc");
        sql.append(" limit :offset , :pageSize");
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        log.info(sql.toString());
        List<Map<String, Object>> result = namedParameterJdbcTemplate.queryForList(sql.toString(), params);
        result.forEach(item -> {
            if (Objects.nonNull(item.get("twhour")) && StrUtil.isNotBlank(item.get("twhour").toString())) {
                item.replace("twhour", item.get("twhour").toString() + "时");
            }
        });
        List<Map<String, Object>> list = new ArrayList<>();
        Map<Object, List<Map<String, Object>>> collect = result.stream().collect(Collectors.groupingBy(
                x -> x.get("jltime")
        ));
        collect.forEach((k, v) -> {
            Map<String, Object> objectMap = v.get(0);
            if (Objects.nonNull(objectMap.get("twhour")) && StrUtil.isNotBlank(objectMap.get("twhour").toString())) {
                objectMap.replace("twhour", objectMap.get("jltime").toString() + " " + objectMap.get("twhour").toString());
            }
            list.addAll(v);
        });
        list.sort(Comparator.comparing(x -> x.get("jltime").toString(), Comparator.reverseOrder()));
        map.replace("rows", list);
        return map;
    }

    /**
     * @description 根据门诊病历id获取老人详细信息
     * <AUTHOR>
     */
    public Object getPatientInfoById(String id) {
        String sql = """
                select zhuyuanh,xingming,quy,chuangw_no,nianling,xingbie,yibaolx
                     ,c1.zdmc as zdmc1,c2.zdmc as zdmc2,c3.zdmc as zdmc3,c4.zdmc as zdmc4
                from data_ext_menzbl m
                LEFT JOIN data_ext_linczd c1 ON m.linczd1 = c1.leaf_id
                LEFT JOIN data_ext_linczd c2 ON m.linczd2 = c2.leaf_id
                LEFT JOIN data_ext_linczd c3 ON m.linczd3 = c3.leaf_id
                LEFT JOIN data_ext_linczd c4 ON m.linczd4 = c4.leaf_id
                where m.leaf_id = :id
                """;
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        Map<String, Object> map = namedParameterJdbcTemplate.queryForMap(sql, params);
        String bedSql = """
                select bed_name from data_ext_p000001321020140 where old_name = :old_name and old_num = :old_num
                """;
        Map<String, Object> bedParams = new HashMap<>();
        bedParams.put("old_name", map.get("xingming").toString());
        bedParams.put("old_num", map.get("zhuyuanh").toString());
        String bedName = namedParameterJdbcTemplate.queryForObject(bedSql, bedParams, String.class);
        map.put("bed_name", bedName);
        return map;
    }

    /**
     * @description 保存疾病诊断
     * <AUTHOR>
     */
    public AjaxResult saveLinczd(Map<String, Object> params) {
        if (params.isEmpty() || Objects.isNull(params.get("zdmc")) || StrUtil.isBlank(params.get("zdmc").toString())) {
            return AjaxResult.error("疾病诊断不能为空");
        }
        String id = IdUtil.getSnowflakeNextIdStr();
        String username = SecurityUtils.getUsername();
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> map = new HashMap<>();
        map.put("data_code", "LinCZD");
        map.put("leaf_id", id);
        map.put("creator", userId);
        map.put("x_creator", username);
        map.put("c_time", DateUtil.now());
        map.put("app_id", APP_ID);
        map.put("d_status", 0);
        map.put("d_stage", 0);
        map.put("d_audit", 0);
        map.put("sync_status", 0);
        map.put("is_chain", 0);
        map.put("pub_status", 0);
        map.put("share_status", 0);
        map.put("zdmc", params.get("zdmc").toString());
        String zdpy = PinyinUtil.getFirstLetter(params.get("zdmc").toString(), "");
        map.put("zdpy", zdpy);
        genericMapper.create("data_ext_linczd", map);
        return AjaxResult.success(Map.of("leaf_id", id, "zdmc", params.get("zdmc").toString()));
    }
}
