package com.ruoyi.core.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.core.constants.PsoConstant;
import com.ruoyi.core.entity.PsoOperLog;
import com.ruoyi.core.mapper.PsoOperLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@EnableAsync
public class LogService {
    public static final String MODULE_FORM = "表单";
    public static final String MODULE_WORKFLOW = "流程";
    public static final String MODULE_VIEW = "视图";
    public static final String MODULE_NODE = "节点";
    public static final String MODULE_PATIENT_RECORD = "门诊简历";
    public static final String MODULE_MEDICAL_RECORD = "病程录";
    public static final String MODULE_DOCTOR_RECORD = "就医记录";
    public static final String MODULE_MEDICINE_ORDER = "用药医嘱";
    public static final String MODULE_EXTERNAL_RX = "外配药医嘱";
    public static final String MODULE_CLINICAL_ORDER = "诊疗医嘱";
    public static final String MODULE_OTHER_MEDICAL_ORDER = "其他医嘱";
    private final PsoOperLogMapper psoOperLogMapper;

    @Value("${logging.path:./logs}")
    private String loggingPath;

    public LogService(PsoOperLogMapper psoOperLogMapper) {
        this.psoOperLogMapper = psoOperLogMapper;
    }

    @Async
    public void asyncSavePsoLog(PsoOperLog psoOperLog, Map<String, Object> params) {
        try {
            psoOperLog
                    .setAutoNo(IdUtil.getSnowflakeNextIdStr())
                    .setAppId(PsoConstant.APP_ID)
                    .setLogTime(DateUtil.now())
                    .setLogData(JSONUtil.toJsonStr(params));
            psoOperLogMapper.insert(psoOperLog);
        } catch (Exception e) {
            log.error("日志记录失败，错误内容: {}", psoOperLog);
        }
    }

    /**
     * 根据日期读取业务日志内容
     *
     * @param date 日期 格式yyyy-MM-dd
     * @return 日志内容列表
     */
    public List<Map<String, Object>> getBusinessLogs(String date) throws IOException {
        // 解析日期
        String yearMonth = date.substring(0, 7); // 提取yyyy-MM部分

        // 构建日志文件路径
        String logFilePath = this.loggingPath + "/" + yearMonth + "/json-log." + date + ".log";

        List<Map<String, Object>> logs = new ArrayList<>();
        Path path = Paths.get(logFilePath);

        // 检查文件是否存在
        if (!Files.exists(path)) {
            log.warn("日志文件不存在: {}", logFilePath);
            return logs;
        }

        // 读取文件内容
        try (BufferedReader reader = Files.newBufferedReader(path)) {
            ObjectMapper mapper = new ObjectMapper();
            String line;
            while ((line = reader.readLine()) != null) {
                // 解析每行JSON
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> logEntry = mapper.readValue(line, Map.class);
                    logs.add(logEntry);
                } catch (Exception e) {
                    log.warn("解析日志行失败: {}", line, e);
                }
            }
        }

        return logs;
    }

    @Async
    public void saveLog(String relationID, String module, String operation, Object data, Object originalData) {
        saveLogWithBizName(relationID, "管理端", module, operation, data, originalData);
    }

    @Async
    public void saveLogWithBizName(String relationID, String bizName, String module, String operation, Object data, Object originalData) {
        try {
            org.slf4j.MDC.put("logType", "businessLog");
            ObjectMapper objectMapper = new ObjectMapper();

            Map<String, Object> logContent = new HashMap<>();
            logContent.put("bizName", bizName);
            logContent.put("module", module);
            logContent.put("operation", operation);
            if (relationID.isBlank()) {
                logContent.put("relationID", "");
            } else {
                logContent.put("relationID", relationID);
            }
            if (data != null) {
                logContent.put("data", data);
            }
            if (originalData != null) {
                logContent.put("originalData", originalData);
            }

            String jsonLog = objectMapper.writeValueAsString(logContent);
            log.info(jsonLog);
        } catch (Exception e) {
            log.error("记录业务日志失败 {}", e.getMessage());
        } finally {
            org.slf4j.MDC.remove("logType");
        }
    }
}
