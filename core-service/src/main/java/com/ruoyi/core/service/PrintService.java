package com.ruoyi.core.service;

import cn.hutool.core.util.IdUtil;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class PrintService {
    public static final String TABLE_CONFIG = "data_print_config";
    private final JdbcTemplate jdbcTemplate;
    private final GenericMapper genericMapper;

    public PrintService(JdbcTemplate jdbcTemplate, GenericMapper genericMapper) {
        this.jdbcTemplate = jdbcTemplate;
        this.genericMapper = genericMapper;
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param configCode 配置编码
     */
    public void removePrintConfig(String configCode) {
        List<String[]> condition = List.<String[]>of(
                new String[]{"equal", "auto_no", configCode}
        );
        genericMapper.delete(TABLE_CONFIG, condition);
    }

    public void copyPrintConfig(String configCode) {
        List<Map<String, Object>> configList = genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.<String[]>of(
                        new String[]{"equal", "auto_no", configCode}
                ),
                "limit 1"
        );
        if (configList.isEmpty()) {
            throw new RuntimeException("配置不存在");
        }
        Map<String, Object> config = configList.get(0);
        config.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        config.put("print_name", config.get("print_name") + "_复制");
        config.put("is_default", "0");
        genericMapper.create(TABLE_CONFIG, config);
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param data 变更数据
     */
    public void updatePrintConfig(Map<String, Object> data) {
        Map<String, Object> condition = Map.of("auto_no", data.get("auto_no").toString());
        data.remove("auto_no");
        this.genericMapper.update(TABLE_CONFIG, data, condition);
    }

    public Map<String, Object> getPrintConfig(String code, String configCode) {
        List<Map<String, Object>> configList = this.genericMapper.getList(
                TABLE_CONFIG,
                "*",
                List.of(
                        new String[]{"equal", "auto_no", configCode},
                        new String[]{"equal", "source_code", code}
                ),
                "limit 1"
        );
        if (configList.isEmpty()) {
            return Map.of();
        }
        return configList.get(0);
    }

    public List<Map<String, Object>> getPrintConfigList(String printCode) {
        String sql = """
                SELECT a.*, b.data_sname
                FROM DATA_PRINT_CONFIG a
                LEFT JOIN node_ext_data b ON a.source_code = b.data_code
                WHERE 1=1
                AND a.source_code = ?
                ORDER BY b.create_time DESC
                """;
        return jdbcTemplate.queryForList(sql, printCode);
    }

    /**
     * todo 记录log
     * todo 更新redis
     *
     * @param data 数据
     */
    public void createPrintConfig(Map<String, Object> data) {
        data.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        if (Objects.isNull(data.get("app_id"))) {
            data.put("app_id", "");
        }
        this.genericMapper.create(TABLE_CONFIG, data);
    }
}
