package com.ruoyi.core.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.entity.PsoDataDict;
import com.ruoyi.core.entity.SysTableField;
import com.ruoyi.core.entity.TableField;
import com.ruoyi.core.enums.AuthEnum;
import com.ruoyi.core.enums.DataStatusEnum;
import com.ruoyi.core.mapper.FormMapper;
import com.ruoyi.core.mapper.GenericMapper;
import com.ruoyi.core.mapper.PsoDataDictMapper;
import com.ruoyi.core.repository.EnumRepository;
import com.ruoyi.core.repository.FormRepository;
import com.ruoyi.core.sqltemplate.actuator.BaseTableActuator;
import com.ruoyi.core.sqltemplate.actuator.TableActuatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.ruoyi.core.constants.PsoConstant.*;
import static com.ruoyi.core.service.LogService.MODULE_FORM;

@Slf4j
@Service
public class FormService {
    public final static String NODE_EXT_DATA = "node_ext_data";
    public final static String TABLE_DATA_DICT = "pso_data_dict";
    public final static String TABLE_PREFIX = "data_ext_";
    public final static String TABLE_FORM_CONFIG = "data_design_config";

    private final FormMapper formMapper;
    private final PsoDataDictMapper psoDataDictMapper;
    private final FormRepository formRepository;
    private final GenericMapper genericMapper;
    private final EnumRepository enumRepository;
    private final ViewService viewService;
    private final LogService logService;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Value("${spring.datasource.driverClassName}")
    private String driverClassName;

    public FormService(
            FormMapper formMapper,
            FormRepository formRepository,
            GenericMapper genericMapper,
            EnumRepository enumRepository,
            ViewService viewService,
            LogService logService,
            PsoDataDictMapper psoDataDictMapper,
            NamedParameterJdbcTemplate namedParameterJdbcTemplate
    ) {
        this.formMapper = formMapper;
        this.formRepository = formRepository;
        this.genericMapper = genericMapper;
        this.enumRepository = enumRepository;
        this.viewService = viewService;
        this.logService = logService;
        this.psoDataDictMapper = psoDataDictMapper;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
    }

    public List<Map<String, Object>> getDataDictList(String dataCode) {
        List<Map<String, Object>> dataDict = this.genericMapper.getList(
                TABLE_DATA_DICT,
                "*",
                List.<String[]>of(new String[]{"equal", "data_code", dataCode}),
                ""
        );
        if (dataDict.isEmpty()) {
            return List.of();
        }
        return dataDict;
    }

    /**
     * @description 表单右侧分页列表
     * <AUTHOR>
     */
    public List<Map<String, Object>> formList(String dataCode, String dataSName, String state) {
        List<Map<String, Object>> maps = formMapper.formList(dataCode, APP_ID, dataSName, state);
        return maps;
    }

    public Map<String, Object> getDetailList(String dataCode, String orderBy, String take, String skip) {
        Map<String, Object> result = new HashMap<>();
        List<String> orderByList = new ArrayList<>(List.of("d_status asc"));
        if (!orderBy.isEmpty()) {
            orderByList.add(orderBy);
        }
        List<Map<String, Object>> detailList = this.genericMapper.getList(
                TABLE_PREFIX + dataCode,
                "*",
                List.of(),
                "order by " + String.join(",", orderByList) + " limit " + take + " offset " + skip
        );
        if (detailList.isEmpty()) {
            result.put("detail_list", Map.of());
        }
        result.put("detail_list", detailList);

        List<Map<String, Object>> total = this.genericMapper.getList(
                TABLE_PREFIX + dataCode,
                "count(*) as qty",
                List.of(),
                ""
        );
        if (total.isEmpty()) {
            result.put("detail_total", 0);
        }
        result.put("detail_total", total.get(0).get("qty").toString());
        return result;
    }

    /**
     * @description 表单详情
     * <AUTHOR>
     */
    public Map<String, Object> formInfo(String dataCode) {
        final String TABLE_DATA_DICT = "pso_data_dict";
        Map<String, Object> result = formMapper.formInfo(dataCode);
        if (MapUtil.isEmpty(result)) {
            return Map.of();
        }

        List<Map<String, Object>> dataDict = this.genericMapper.getList(
                TABLE_DATA_DICT,
                "*",
                List.<String[]>of(new String[]{"equal", "data_code", dataCode}),
                ""
        );
        if (dataDict.isEmpty()) {
            result.put("data_dict", Map.of());
        }
        result.put("data_dict", dataDict);

        List<Map<String, Object>> enumList = this.enumRepository.getEnumListByIn(List.of(dataCode));
        if (enumList.isEmpty()) {
            result.put("enum_list", Map.of());
        }
        result.put("enum_list", enumList);

        List<Map<String, Object>> viewList = this.viewService.getViewConfigListByView(dataCode);
        if (viewList.isEmpty()) {
            result.put("view_list", Map.of());
        }
        result.put("view_list", viewList);

        return result;
    }

    /**
     * todo 查询动态表
     * todo 可能有关联数据
     * todo 记录log
     *
     * @param dataCode 表单code
     * @param viewCode 视图id/code
     * @param keyword  查询关键词
     * @param take     每页条数
     * @param skip     跳过条数
     * @return Map
     */
    public Map<String, Object> getForm(String dataCode, String viewCode, String keyword, int take, long skip) {
        Map<String, Object> form = formRepository.get(dataCode);
        List<Map<String, Object>> colDefList = formRepository.getColDefList(dataCode);
        List<Map<String, Object>> viewList = formRepository.getViewList(viewCode, dataCode);
        Map<String, Object> content = formRepository.getContentList(dataCode, keyword, take, skip);
        return Map.of("form", form, "colDefList", colDefList, "viewList", viewList, "content", content);
    }

    /**
     * @description 创建表单
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object add(Map<String, Object> params) {
        // 创建表单
        String username = SecurityUtils.getUsername();
        Map<String, Object> dataMap = dataMapHandle(params, username);
        genericMapper.create(NODE_EXT_DATA, dataMap);
        // 创建表单 节点关系
        String dataCode = dataMap.get("data_code").toString();
        String nodeId = params.get("node_id").toString();
        genericMapper.create("PSO_NODE_DATA", nodeDataHandle(nodeId, dataCode, NODE_EXT_DATA));
        // 创建角色权限
        genericMapper.create("role_auth_show", roleAuthHandle(dataCode, AuthEnum.FormAuth.getType()));
        // 记录日志
//        logService.asyncSavePsoLog(new PsoOperLog()
//                .setLogType(INSERT_FORM_LOG_TYPE)
//                .setLogUser(username)
//                .setLogContent(
//                        StrUtil.format("添加表单:{}", params.get("data_name"))
//                )
//                .setDataCode(dataCode)
//                .setLeafId(dataCode), params);
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "添加表单", dataMap, null);
        return dataCode;
    }

    //region 表单设计器内容

    /**
     * @description 表单设计器详情
     * <AUTHOR>
     */
    public Map<String, Object> designInfo(String id) {
        final String TABLE_DATA_DICT = "data_design_config";
        List<Map<String, Object>> dataDict = this.genericMapper.getList(
                TABLE_DATA_DICT,
                "*",
                List.<String[]>of(new String[]{"equal", "id", id}),
                ""
        );
        Map<String, Object> result = new HashMap<>();
        if (!dataDict.isEmpty()) {
            return dataDict.get(0);
        }
        return result;
    }

    /**
     * @description 表单设计器内容配置
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object designConfig(Map<String, Object> params) {
        // 创建表单
        String code = params.get("id").toString();
        List<Map<String, Object>> rows = this.genericMapper.getList(
                TABLE_FORM_CONFIG,
                "id,data_state",
                List.<String[]>of(new String[]{"equal", "id", code}),
                "limit 1"
        );
        if (Objects.isNull(rows) || rows.isEmpty()) {
            Map<String, Object> dataState = Map.of("created_at", DateUtil.formatDateTime(new Date()));
            params.put("data_state", JSONUtil.toJsonStr(dataState));
            params.replace("design_json", JSONUtil.toJsonStr(params.get("design_json")));
            params.replace("publish_json", JSONUtil.toJsonStr(params.get("publish_json")));
            genericMapper.create("data_design_config", params);
//            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "修改表单内容", params, null);
        } else {
            //更新
            Map<String, Object> condition = new HashMap<>();
            condition.put("id", code);
            Map<String, Object> row = rows.get(0);
            String dataState = (String) row.get("data_state");
            Map<String, Object> dataStateMap = JSONUtil.parseObj(dataState);
            dataStateMap.put("updated_at", DateUtil.formatDateTime(new Date()));
            params.replace("data_state", JSONUtil.toJsonStr(dataStateMap));
            params.replace("design_json", JSONUtil.toJsonStr(params.get("design_json")));
            params.replace("publish_json", JSONUtil.toJsonStr(params.get("publish_json")));
            genericMapper.update(TABLE_FORM_CONFIG, params, condition);
//            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "添加表单内容", params, null);
        }
        return code;
    }

    //endregion

    /**
     * @description 发布表单
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object publish(Map<String, Object> params) throws Exception {
        // 更新表单
        String username = SecurityUtils.getUsername();
        String code = params.get("data_code").toString();
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put("data_code", params.get("data_code"));
        params.put("upuser", username);
        params.put("input_time", DateUtil.now());
        genericMapper.update(NODE_EXT_DATA, params, conditionMap);
        int status = (int) params.get("data_status");
        JSONObject designObj = JSONUtil.parseObj(params.get("data_design"));
        JSONArray dictArr = designObj.getJSONArray("data");
        if (status == 0) {   //未发布
            List<TableField> tableFields = SysTableField.GetDataSysFields();
            // 插入系统字典
            for (TableField field : tableFields) {
                PsoDataDict sysEntity = new PsoDataDict(1);
                sysEntity.setCode(code).setField(field.Field).setAutoNo(field.getField()).setFieldType(field.Type).setFieldLen(field.Len).setFieldDisplay(field.FieldDisplay);
                psoDataDictMapper.insert(sysEntity);
            }
            //处理动态字段
            for (int num = 0; num < dictArr.size(); num++) {
                JSONObject dictObj = dictArr.getJSONObject(num);
                PsoDataDict dict = new PsoDataDict(dictObj);
                dict.setCode(code);
                tableFields.add(new TableField(dict.Field, dict.FieldType, dict.FieldLen, dict.FieldDisplay));
                psoDataDictMapper.insert(dict);
                switch (dict.FieldFormat.toLowerCase()) {
                    case "kv": {
                        PsoDataDict x_entity = new PsoDataDict(-1);
                        x_entity.setCode(code).setField("x_" + dict.Field).setAutoNo("x_" + dict.getAutoNo()).setFieldFormat("common")
                                .setFieldType(dict.FieldType).setFieldLen(dict.FieldLen).setFieldDisplay(dict.FieldDisplay + "名称");
                        psoDataDictMapper.insert(x_entity);
                        tableFields.add(new TableField("x_" + dict.Field, dict.FieldType, dict.FieldLen, dict.FieldDisplay + "名称"));
                        break;
                    }
                    case "path": {
                        PsoDataDict v_entity = new PsoDataDict(-1);
                        v_entity.setCode(code).setField("v_" + dict.Field).setAutoNo("v_" + dict.getAutoNo()).setFieldFormat("common").setControlType("text")
                                .setFieldType(dict.FieldType).setFieldLen(50).setFieldDisplay(dict.FieldDisplay + "栏目");
                        psoDataDictMapper.insert(v_entity);
                        tableFields.add(new TableField("v_" + dict.Field, dict.FieldType, 50, dict.FieldDisplay + "栏目"));
                        PsoDataDict path_entity = new PsoDataDict(-1);
                        path_entity.setCode(code).setField("path_" + dict.Field).setAutoNo("path_" + dict.getAutoNo()).setFieldFormat("common").setControlType("text")
                                .setFieldType(dict.FieldType).setFieldLen(200).setFieldDisplay(dict.FieldDisplay + "路径");
                        psoDataDictMapper.insert(path_entity);
                        tableFields.add(new TableField("path_" + dict.Field, dict.FieldType, 200, dict.FieldDisplay + "路径"));
                        break;
                    }
                    case "account": {
                        PsoDataDict u_entity = new PsoDataDict(-1);
                        u_entity.setCode(code).setField("u_" + dict.Field).setAutoNo("v_" + dict.getAutoNo()).setFieldFormat("common").setControlType("text")
                                .setFieldType("string").setFieldLen(20).setFieldDisplay(dict.FieldDisplay + "单位");
                        psoDataDictMapper.insert(u_entity);
                        tableFields.add(new TableField("u_" + dict.Field, "string", 20, dict.FieldDisplay + "单位"));
                        PsoDataDict bs_entity = new PsoDataDict(-1);
                        bs_entity.setCode(code).setField("bs_" + dict.Field).setAutoNo("bs_" + dict.getAutoNo()).setFieldFormat("common").setControlType("text")
                                .setFieldType("string").setFieldLen(20).setFieldDisplay(dict.FieldDisplay + "基本单位");
                        psoDataDictMapper.insert(bs_entity);
                        tableFields.add(new TableField("bs_" + dict.Field, "string", 20, dict.FieldDisplay + "基本单位"));
                        PsoDataDict bv_entity = new PsoDataDict(-1);
                        bv_entity.setCode(code).setField("bv_" + dict.Field).setAutoNo("bv_" + dict.getAutoNo()).setFieldFormat("common").setControlType("text")
                                .setFieldType("string").setFieldLen(20).setFieldDisplay(dict.FieldDisplay + "转换值");
                        psoDataDictMapper.insert(bv_entity);
                        tableFields.add(new TableField("bv_" + dict.Field, "string", 20, dict.FieldDisplay + "转换值"));
                        PsoDataDict br_entity = new PsoDataDict(-1);
                        br_entity.setCode(code).setField("br" + dict.Field).setAutoNo("br" + dict.getAutoNo()).setFieldFormat("common").setControlType("text")
                                .setFieldType("string").setFieldLen(20).setFieldDisplay(dict.FieldDisplay + "倍数");
                        psoDataDictMapper.insert(br_entity);
                        tableFields.add(new TableField("br" + dict.Field, "string", 20, dict.FieldDisplay + "倍数"));
                        break;
                    }
                    default:
                        break;
                }
            }
            //创建表
            MapSqlParameterSource sqlParams = new MapSqlParameterSource();
            namedParameterJdbcTemplate.update(CreateTable(tableFields, code), sqlParams);
            //TODO 发布默认渲染
            if (params.containsKey("render")) {

            }
            //发布状态
            Map<String, Object> statusMap = new HashMap<>();
            statusMap.put("data_status", 1);
            genericMapper.update(NODE_EXT_DATA, statusMap, conditionMap);
        } else {
            List<TableField> addFields = new ArrayList<>();
            List<TableField> modifyFields = new ArrayList<>();
            List<TableField> deleteFields = new ArrayList<>();
            List<String> dealFields = new ArrayList<>();
            List<String> addStringList = new ArrayList<>();
            List<String> modifyStringList = new ArrayList<>();
            List<String> deleteStringList = new ArrayList<>();
            List<PsoDataDict> CurrentDict = psoDataDictMapper.selectDictList(code);
            for (int num = 0; num < dictArr.size(); num++) {
                JSONObject dictObj = dictArr.getJSONObject(num);
                PsoDataDict newF = new PsoDataDict(dictObj);
                if (CurrentDict != null && CurrentDict.contains(newF)) continue;
                boolean isVirtual = IsVirtualField(newF);
                if (CurrentDict.stream().anyMatch(f -> f.AutoNo.equals(newF.AutoNo))) {
                    PsoDataDict _sourceDict = CurrentDict.stream().filter(f -> f.AutoNo.equals(newF.AutoNo)).toList().get(0);
                    psoDataDictMapper.updateExistDict(newF);
                    if (_sourceDict.Field.equals(newF.Field) && _sourceDict.FieldType.equals(newF.FieldType) && _sourceDict.FieldLen == newF.FieldLen) {
                        UpdateFields(CurrentDict, _sourceDict, newF, modifyFields, addFields, dealFields);
                    } else {
                        TableField _field = new TableField(_sourceDict.Field, newF.FieldType, newF.FieldLen);
                        _field.NewField = newF.Field;
                        modifyFields.add(_field);
                        UpdateFields(CurrentDict, _sourceDict, newF, modifyFields, addFields, dealFields);
                    }
                }
                if (CurrentDict.stream().anyMatch(f -> f.Field.equals(newF.Field))) {
                    PsoDataDict _sourceDict = CurrentDict.stream().filter(f -> f.Field.equals(newF.Field)).toList().get(0);
                    psoDataDictMapper.updateExistDict(newF);
                    //数据库字段信息
                    if (_sourceDict.Field.equals(newF.Field) && _sourceDict.FieldType.equals(newF.FieldType) && _sourceDict.FieldLen == newF.FieldLen) {
                        UpdateFields(CurrentDict, _sourceDict, newF, modifyFields, addFields, dealFields);
                    } else {
                        TableField _field = new TableField(_sourceDict.Field, newF.FieldType, newF.FieldLen);
                        _field.NewField = newF.Field;
                        modifyFields.add(_field);
                        UpdateFields(CurrentDict, _sourceDict, newF, modifyFields, addFields, dealFields);
                    }
                    continue;
                }
                if (isVirtual) {
                    newF.setIsSys(2);
                } else {
                    newF.setIsSys(0);
                }
                addFields.add(new TableField(newF.Field, newF.FieldType, newF.FieldLen));
                psoDataDictMapper.insert(newF);
            }
            for (PsoDataDict oldF : CurrentDict) {
                if (!dealFields.contains(oldF.AutoNo) && (oldF.IsSys == 0 || oldF.IsSys == 2)) {
                    psoDataDictMapper.deleteField(code, oldF.Field);
                    deleteFields.add(new TableField(oldF.Field));
                    DeleteFields(oldF, deleteFields, deleteStringList);
                }
            }
            // 追加字段
            if (!addFields.isEmpty())
                AddTableField(addFields, code);
            // 更新字段
            if (!modifyFields.isEmpty()) {
                ModifyTableField(modifyFields, code);
            }
            // 移除字段
            if (!deleteFields.isEmpty())
                DeleteTableField(deleteFields, code);
        }
        // 记录日志
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "发布表单", params, null);
        return params.get("data_code");
    }

    //region 发布表单私有方法
    public void UpdateFields(List<PsoDataDict> CurrentDict, PsoDataDict oldF, PsoDataDict newF, List<TableField> modifyFields, List<TableField> addFields, List<String> dealFields) {
        switch (newF.FieldFormat.toLowerCase()) {
            case "kv":
                String field = String.format("x_,string,%d,名称", newF.FieldLen);
                String[] kvFields = new String[]{field};
                if (CurrentDict.stream().anyMatch(t -> t.AutoNo.equals("x_" + newF.AutoNo))) { //oldF.AutoNo.equals(newF.AutoNo)
                    UpdateFieldDisplay(oldF, newF, modifyFields, dealFields, kvFields);
                    if (!oldF.Field.equals(newF.Field)) {
                        //更改扩展字段名称
                        UpdateExtField(oldF, newF, modifyFields, dealFields, kvFields);
                    }
                } else {
                    AddExtField(oldF, addFields, kvFields);
                }
                break;
            case "path":
                String[] pathFields = new String[]{"v_,String,20,栏目", "path_,String,200,路径"};
                if (CurrentDict.stream().anyMatch(t -> t.AutoNo.equals("v_" + newF.AutoNo))) {   //oldF.AutoNo.equals(newF.AutoNo)
                    UpdateFieldDisplay(oldF, newF, modifyFields, dealFields, pathFields);
                    if (!oldF.Field.equals(newF.Field)) {
                        //更改扩展字段名称
                        UpdateExtField(oldF, newF, modifyFields, dealFields, pathFields);
                    }
                } else {
                    AddExtField(oldF, addFields, pathFields);
                }
                break;
            case "account":
                String[] calFields = new String[]{"u_,String,20,单位", "bs_,String,20,基本单位", "bv_,String,20,转换值", "br_,String,20,倍数"};
                if (CurrentDict.stream().anyMatch(t -> t.AutoNo.equals("u_" + newF.AutoNo))) {   //oldF.AutoNo.equals(newF.AutoNo)
                    UpdateFieldDisplay(oldF, newF, modifyFields, dealFields, calFields);
                    if (!oldF.Field.equals(newF.Field)) {
                        //更改扩展字段名称
                        UpdateExtField(oldF, newF, modifyFields, dealFields, calFields);
                    }
                } else {
                    AddExtField(oldF, addFields, calFields);
                }
                break;
            default:
                break;
        }
    }

    public static void UpdateExtField(PsoDataDict oldF, PsoDataDict newF, List<TableField> modifyFields, List<String> dealList,
                                      String[] extFields) {
        for (int num = 0; num < extFields.length; num++) {
            String[] configs = extFields[num].split(",");
            TableField _field = new TableField(String.format("%s%s", configs[0], oldF.Field), newF.FieldType, Integer.parseInt(configs[2]));
            _field.NewField = String.format("%s%s", configs[0], newF.Field);
            modifyFields.add(_field);
            dealList.add(String.format("%s%s", configs[0], oldF.AutoNo));
        }
    }

    public void UpdateFieldDisplay(PsoDataDict oldF, PsoDataDict newF, List<TableField> modifyFields, List<String> dealList,
                                   String[] extFields) {
        for (int num = 0; num < extFields.length; num++) {
            Map<String, Object> dataMap = new HashMap<>();
            Map<String, Object> conditionMap = new HashMap<>();
            String[] configs = extFields[num].split(",");
            dataMap.put("field", String.format("%s%s", configs[0], newF.Field));
            dataMap.put("display", String.format("%s%s", newF.FieldDisplay, configs[3]));
            conditionMap.put("data_code", newF.Code);
            conditionMap.put("auto_no", String.format("%s%s", configs[0], newF.AutoNo));
            genericMapper.update("PSO_DATA_DICT", dataMap, conditionMap);
            dealList.add(String.format("%s%s", configs[0], oldF.AutoNo));
        }
    }

    public void AddExtField(PsoDataDict newF, List<TableField> dealFields,
                            String[] extFields) {
        for (String extField : extFields) {
            //(code,type,len,name)
            String[] configs = extField.split(",");
            PsoDataDict entity = new PsoDataDict(-1);
            entity.setField(String.format("%s%s", configs[0], newF.Field)).setAutoNo(String.format("%s%s", configs[0], newF.AutoNo)).setFieldType(configs[1])
                    .setFieldLen(Integer.parseInt(configs[2])).setFieldFormat("common").setOutFormat("").setFieldDisplay(String.format("%s%s", newF.FieldDisplay, configs[3]))
                    .setCode(newF.Code);
            psoDataDictMapper.insert(entity);
            dealFields.add(new TableField(String.format("%s%s", configs[0], newF.Field), configs[1], Integer.parseInt(configs[2])));
        }
    }

    public void DeleteFields(PsoDataDict oldF, List<TableField> dealFields, List<String> logFields) {
        switch (oldF.FieldFormat.toLowerCase()) {
            case "kv":
                String[] kvFields = new String[]{"x_"};
                DeleteExtField(oldF, dealFields, logFields, kvFields);
                break;
            case "path":
                String[] extFields = new String[]{"v_", "path_"};
                DeleteExtField(oldF, dealFields, logFields, extFields);
                break;
            case "account":
                String[] calFields = new String[]{"u_", "bs_", "bv_", "br_"};
                DeleteExtField(oldF, dealFields, logFields, calFields);
                break;
            default:
                break;
        }
    }

    public void DeleteExtField(PsoDataDict oldF, List<TableField> dealFields, List<String> logFields, String[] extFields) {
        for (String extField : extFields) {
            psoDataDictMapper.deleteField(oldF.Code, String.format("%s%s", extField, oldF.Field));
            dealFields.add(new TableField(String.format("%s%s", extField, oldF.Field)));
            logFields.add(String.format("%s%s", extField, oldF.Field));
        }
    }

    //endregion

    /**
     * @description 编辑表单/表单重命名
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object rename(String dataCode, Map<String, Object> params) {
        String dataName = params.get("data_name").toString();
        String dataSname = params.get("data_sname").toString();
        String changeCode = params.get("data_code").toString();
        String username = SecurityUtils.getUsername();

        Map<String, Object> currentForm = formRepository.get(dataCode);
        if (MapUtil.isEmpty(currentForm)) {
            throw new RuntimeException("表单不存在");
        }

        // 只更改名称和简称，不更改编码
        if (dataCode.equals(changeCode)) {
            if (dataName.equals(currentForm.get("data_name")) && dataSname.equals(currentForm.get("data_sname"))) {
                return dataCode; // 没有变化，直接返回
            }

            // 构建更新条件
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put("data_name", dataName);
            updateMap.put("data_sname", dataSname);

            // 构建条件
            Map<String, Object> condition = new HashMap<>();
            condition.put("data_code", dataCode);

            // 执行更新
            genericMapper.update(NODE_EXT_DATA, updateMap, condition);
            //updateDataLog(username, dataCode, changeCode, params);
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "重命名表单", updateMap, null);
            return dataCode;
        }

        // 如果需要更改表单编码
        // 检查新编码是否已存在
        Map<String, Object> existForm = formRepository.get(changeCode);
        if (MapUtil.isNotEmpty(existForm)) {
            throw new RuntimeException("新表单编码已存在");
        }

        // 检查是否有脚本引用
        if (checkScriptReference(dataCode)) {
            throw new RuntimeException("表单已被脚本引用，无法更名");
        }

        // 使用NamedParameterJdbcTemplate执行多个SQL更新
        MapSqlParameterSource sqlParams = new MapSqlParameterSource();
        sqlParams.addValue("changeCode", changeCode);
        sqlParams.addValue("dataCode", dataCode);
        sqlParams.addValue("dataName", dataName);
        sqlParams.addValue("dataSname", dataSname);
        sqlParams.addValue("upuser", username);
        sqlParams.addValue("inputTime", DateUtil.now());

        List<String> sqlStatements = new ArrayList<>();

        // 更新pso_node_data表
        sqlStatements.add("UPDATE pso_node_data SET node_data_id = :changeCode WHERE node_data_id = :dataCode");

        // 更新表单数据
        sqlStatements.add("UPDATE node_ext_data SET data_code = :changeCode, data_name = :dataName, data_sname = :dataSname, upuser = :upuser, input_time = :inputTime WHERE data_code = :dataCode");

        // 更新表单权限
        sqlStatements.add("UPDATE role_auth_show SET map_id = :changeCode WHERE map_id = :dataCode");

        // 如果表单已发布(data_status=1)，需要更新更多表
        Integer dataStatus = (Integer) currentForm.get("data_status");
        if (dataStatus != null && dataStatus != 0) {
            // 更新字典表
            sqlStatements.add("UPDATE pso_data_dict SET data_code = :changeCode WHERE data_code = :dataCode");

            // 更新数据表中的data_code字段
            String tableName = "data_ext_" + dataCode;
            sqlStatements.add("UPDATE " + tableName + " SET data_code = :changeCode");

            // 更新表名 - 这个需要特殊处理
            String changeTableName = "data_ext_" + changeCode;
            sqlStatements.add("RENAME TABLE " + tableName + " TO " + changeTableName);

            // 更新相关配置
            sqlStatements.add("UPDATE DATA_VIEW_CONFIG SET source_code = :changeCode WHERE source_code = :dataCode");
            sqlStatements.add("UPDATE DATA_BUTTON_CONFIG SET source_code = :changeCode WHERE source_code = :dataCode");
            sqlStatements.add("UPDATE DATA_PRINT_CONFIG SET source_code = :changeCode WHERE source_code = :dataCode");
            sqlStatements.add("UPDATE DATA_RULE_CONFIG SET source_code = :changeCode WHERE source_code = :dataCode");
            sqlStatements.add("UPDATE DATA_SERVICE_CONFIG SET source_code = :changeCode WHERE source_code = :dataCode");
            sqlStatements.add("UPDATE PSO_PUB_INFO SET bind_id = :changeCode WHERE bind_id = :dataCode");
            sqlStatements.add("UPDATE DATA_DETAIL_CONFIG SET source_code = :changeCode WHERE source_code = :dataCode");
            sqlStatements.add("UPDATE PSO_SCRIPT_DRIVER SET source_code = :changeCode WHERE source_code = :dataCode");
            sqlStatements.add("UPDATE DATA_STATUS_CONFIG SET source_code = :changeCode WHERE source_code = :dataCode");

            // 更新映射表
            sqlStatements.add("UPDATE pso_data_mapping SET m_data_code = :changeCode WHERE m_data_code = :dataCode");
            sqlStatements.add("UPDATE pso_data_mapping SET c_data_code = :changeCode WHERE c_data_code = :dataCode");

            // 更新服务信息表
            sqlStatements.add("UPDATE user_service_info SET data_code = :changeCode WHERE data_code = :dataCode");
            sqlStatements.add("UPDATE res_service_info SET data_code = :changeCode WHERE data_code = :dataCode");
            sqlStatements.add("UPDATE tag_service_info SET data_code = :changeCode WHERE data_code = :dataCode");
        }

        // 执行所有SQL语句
        for (String sql : sqlStatements) {
            try {
                namedParameterJdbcTemplate.update(sql, sqlParams);
            } catch (Exception e) {
                // 记录错误但继续执行其他语句
                log.error("执行SQL失败: {}, 错误: {}", sql, e.getMessage());
            }
        }
        //updateDataLog(username, dataCode, changeCode, params);
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "编辑表单编码", changeCode, dataCode);
        return changeCode;
    }

    /**
     * 检查表单是否被脚本引用
     */
    private boolean checkScriptReference(String dataCode) {
        String sql = "SELECT COUNT(1) FROM pso_script_driver WHERE driver_config LIKE :pattern";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("pattern", "%" + dataCode + "%");
        Integer count = namedParameterJdbcTemplate.queryForObject(sql, params, Integer.class);
        return count != null && count > 0;
    }

    /**
     * 检查表单是否已有设计器内容
     */
    private boolean checkDataDesign(String dataCode) {
        String sql = "SELECT COUNT(1) FROM data_design_config WHERE id = :code";
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("code", dataCode);
        Integer count = namedParameterJdbcTemplate.queryForObject(sql, params, Integer.class);
        return count != null && count > 0;
    }

    /**
     * 记录编辑日志
     */
//    private void updateDataLog(String username, String dataCode, String changeCode, Map<String, Object> params) {
//        // 记录日志
//        logService.asyncSavePsoLog(new PsoOperLog()
//                .setLogType(RENAME_FORM_LOG_TYPE)
//                .setLogUser(username)
//                .setLogContent(
//                        StrUtil.format("更名表单:{}-{}", dataCode, changeCode)
//                )
//                .setDataCode(dataCode)
//                .setLeafId(changeCode), params);
//    }
    private Map<String, Object> dataMapHandle(Map<String, Object> params, String username) {
        List<Map<String, Object>> dataName = genericMapper.getList(NODE_EXT_DATA,
                "*",
                List.<String[]>of(new String[]{"equal", "data_name", params.get("data_name").toString()}),
                StrUtil.EMPTY);
        if (CollectionUtil.isNotEmpty(dataName)) {
            throw new RuntimeException("已存在名为【" + params.get("data_name").toString() + "】的表单");
        }
        Map<String, Object> dataMap = new HashMap<>();
        if (Objects.isNull(params.get("data_code")) || StrUtil.isBlank(params.get("data_code").toString())) {
            dataMap.put("data_code", IdUtil.getSnowflakeNextIdStr());
        } else {
            Map<String, Object> map = formRepository.get(params.get("data_code").toString());
            if (MapUtil.isNotEmpty(map)) {
                throw new RuntimeException("表单编码已存在");
            }
            if (!Validator.isGeneral(params.get("data_code").toString())) {
                throw new RuntimeException("表单编码格式不正确");
            }
            dataMap.put("data_code", params.get("data_code"));
        }
        //dataMap.put("app_id", APP_ID);
        dataMap.put("create_time", DateUtil.now());
        dataMap.put("data_status", 0);
        dataMap.put("open_discuss", 0);
        dataMap.put("creator", username);
        dataMap.put("data_sold", 0);
        dataMap.put("is_anony", 0);
        dataMap.put("is_sys", 0);
        dataMap.put("is_share", 0);
        dataMap.put("share_auto_approve", 0);
        dataMap.put("upuser", StrUtil.EMPTY);
        dataMap.put("input_time", StrUtil.EMPTY);
        dataMap.put("open_invent", 0);
        dataMap.put("data_type", 0);
        dataMap.put("ext_config", JSONUtil.toJsonStr(params.get("ext_config")));
        dataMap.put("data_name", params.get("data_name"));
        dataMap.put("data_state", JSONUtil.createObj().set("status", "启用").toString());
        dataMap.put("data_design", params.get("data_design"));
        if (Objects.nonNull(params.get("data_sname")) && StrUtil.isBlank(params.get("data_sname").toString())) {
            dataMap.put("data_sname", params.get("data_name"));
        } else {
            dataMap.put("data_sname", params.get("data_sname").toString());
        }
        return dataMap;
    }

    public static Map<String, Object> nodeDataHandle(String nodeId, String dataCode, String nodeDataTag) {
        Map<String, Object> nodeData = new HashMap<>();
        nodeData.put("node_id", nodeId);
        nodeData.put("node_data_tag", nodeDataTag);
        nodeData.put("node_data_id", dataCode);
        nodeData.put("data_order", DEFAULT_ORDER_VALUE);
        nodeData.put("struct_type", "tree");
        nodeData.put("node_data_top", 0);
        nodeData.put("node_data_relate", 0);
        return nodeData;
    }

    public static Map<String, Object> roleAuthHandle(String dataCode, Integer authType) {
        Map<String, Object> roleAuth = new HashMap<>();
        roleAuth.put("auto_no", IdUtil.getSnowflakeNextIdStr());
        roleAuth.put("auth_type", authType);
        roleAuth.put("role_id", "up" + APP_SYS_ROLE);
        roleAuth.put("map_id", dataCode);
        return roleAuth;
    }

    /**
     * @description 启用/停用表单
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object deactivate(List<String> dataCodes, Map<String, Object> map) {
        if (Objects.isNull(map.get("status")) || StrUtil.isBlank(map.get("status").toString())) {
            throw new RuntimeException("状态不可为空");
        }
        String status = map.get("status").toString();
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, Object> condition = new HashMap<>();
        JSONObject obj = JSONUtil.createObj();
        if (CollectionUtil.isNotEmpty(dataCodes)) {
            int total = 0;
            if (Objects.equals(status, ENABLE)) {
                for (String dataCode : dataCodes) {
                    dataMap.put("data_state", obj.set("status", ENABLE).toString());
                    condition.put("data_code", dataCode);
                    int update = genericMapper.update(NODE_EXT_DATA, dataMap, condition);
                    total += update;
                    logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "启用表单", dataMap, dataCodes);
                }
                return total;
            }
            if (Objects.equals(status, DISABLE)) {
                String now = DateUtil.now();
                for (String dataCode : dataCodes) {
                    dataMap.put("data_state", obj
                            .set("status", DISABLE)
                            .set("deleted_time", now)
                            .toString());
                    condition.put("data_code", dataCode);
                    int update = genericMapper.update(NODE_EXT_DATA, dataMap, condition);
                    total += update;
                    logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "停用表单", dataMap, dataCodes);
                }
                return total;
            }
            throw new RuntimeException("状态错误");
        }
        return 0;
    }

    /**
     * @description 暂存
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object staging(String dataCode, Map<String, Object> params) {
        String username = SecurityUtils.getUsername();

        // 确保表单存在
        Map<String, Object> currentForm = formRepository.get(dataCode);
        if (MapUtil.isEmpty(currentForm)) {
            throw new RuntimeException("表单不存在");
        }

        // 构建更新数据和条件
        Map<String, Object> updateMap = new HashMap<>();
        // 需要更新的字段
        String[] updateFields = {"data_name", "data_sname", "data_design", "open_discuss",
                "open_message", "is_anony", "is_share", "open_invent", "ext_config",
                "share_auto_approve", "data_sold", "data_state"};

        for (String field : updateFields) {
            if (params.containsKey(field)) {
                if (field.equals("ext_config") && params.get(field) instanceof Map) {
                    updateMap.put(field, JSONUtil.toJsonStr(params.get(field)));
                } else {
                    updateMap.put(field, params.get(field));
                }
            }
        }

        // 添加更新时间和更新人
        updateMap.put("input_time", DateUtil.now());
        updateMap.put("upuser", username);

        // 构建条件
        Map<String, Object> condition = new HashMap<>();
        condition.put("data_code", dataCode);

        // 执行更新
        genericMapper.update(NODE_EXT_DATA, updateMap, condition);

        // 记录操作日志
//        logService.asyncSavePsoLog(new PsoOperLog()
//                .setLogType(UPDATE_FORM_LOG_TYPE)
//                .setLogUser(username)
//                .setLogContent(StrUtil.format("更新表单：{}", dataCode))
//                .setDataCode(NODE_EXT_DATA)
//                .setLeafId(dataCode), params);

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "更新表单", updateMap, currentForm);
        return dataCode;
    }

    /**
     * @description 复制表单     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object copy(Map<String, Object> params) {
        String nodeId = params.get("node_id").toString();
        String nodePid = params.get("node_pid").toString();
        String username = SecurityUtils.getUsername();
        Map<String, Object> nodeInfo = formRepository.get(nodeId);
        String dataCode = IdUtil.getSnowflakeNextIdStr();
        Map<String, Object> newRecord = copyDataHandle(dataCode, nodeInfo, username);
        genericMapper.create(NODE_EXT_DATA, newRecord);
        genericMapper.create("PSO_NODE_DATA", nodeDataHandle(nodePid, dataCode, NODE_EXT_DATA));
        genericMapper.create("role_auth_show", roleAuthHandle(dataCode, 2));
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "复制表单", newRecord, params);
        return 1;
    }

    /**
     * @description 将表单转换为流程
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object formatTrans(Map<String, Object> params) {
        String dataCode = params.get("data_code").toString();
        String nodeId = params.get("node_id").toString();
        String username = SecurityUtils.getUsername();
        try {
            // 检查工作流是否已存在
            MapSqlParameterSource checkParams = new MapSqlParameterSource();
            checkParams.addValue("wf_code", dataCode);
            Integer count = namedParameterJdbcTemplate.queryForObject(
                    "select count(1) from node_ext_wf where wf_code = :wf_code",
                    checkParams, Integer.class);

            // 更新表单为工作流类型
            MapSqlParameterSource updateParams = new MapSqlParameterSource();
            updateParams.addValue("data_code", dataCode);
            namedParameterJdbcTemplate.update(
                    "update node_ext_data set data_type=1 where data_code = :data_code",
                    updateParams);

            // 如果工作流不存在，则创建一个
            if (count != null && count == 0) {
                Map<String, Object> formInfo = formRepository.get(dataCode);
                if (MapUtil.isEmpty(formInfo)) {
                    throw new RuntimeException("表单不存在: " + dataCode);
                }

                Map<String, Object> wfData = new HashMap<>();
                wfData.put("wf_code", dataCode);
                wfData.put("wf_name", formInfo.get("data_sname"));
                wfData.put("wf_sname", formInfo.get("data_name"));
                wfData.put("map_data_code", dataCode);
                wfData.put("app_id", formInfo.getOrDefault("app_id", APP_ID));
                wfData.put("is_pub", 0);
                wfData.put("wf_matter_able", 0);
                wfData.put("wf_urge_able", 0);
                wfData.put("wf_comment_able", 0);
                wfData.put("wf_src_able", 0);
                wfData.put("is_share", 0);
                wfData.put("wf_auth_type", 0);
                wfData.put("wf_order", 255);
                wfData.put("create_time", DateUtil.now());
                wfData.put("creator", username);
                wfData.put("upuser", "");
                wfData.put("input_time", "");
                wfData.put("wf_tags", "");
                wfData.put("wf_map_tp", 0);
                wfData.put("empty_type", 0);
                wfData.put("wf_state", formInfo.get("data_state").toString());
                genericMapper.create("NODE_EXT_WF", wfData);
            }

            // 删除原有审批状态配置
            MapSqlParameterSource deleteParams = new MapSqlParameterSource();
            deleteParams.addValue("code", dataCode);
            namedParameterJdbcTemplate.update(
                    "delete from data_status_config where source_code = :code and status_type='审批'",
                    deleteParams);

            // 添加系统审批状态
            addSysAudit(dataCode);

            // 更新节点数据标签
            MapSqlParameterSource nodeParams = new MapSqlParameterSource();
            nodeParams.addValue("tag", "node_ext_wf");
            nodeParams.addValue("data_id", dataCode);
            nodeParams.addValue("node_id", nodeId);
            namedParameterJdbcTemplate.update(
                    "update pso_node_data set node_id = :node_id, node_data_tag = :tag where node_data_id = :data_id",
                    nodeParams);

            // 更新各种配置的 source_type 为 1
            MapSqlParameterSource configParams = new MapSqlParameterSource();
            configParams.addValue("code", dataCode);
            namedParameterJdbcTemplate.update(
                    "update DATA_VIEW_CONFIG set source_type=1 where source_code = :code",
                    configParams);
            namedParameterJdbcTemplate.update(
                    "update DATA_BUTTON_CONFIG set source_type=1 where source_code = :code",
                    configParams);
            namedParameterJdbcTemplate.update(
                    "update DATA_PRINT_CONFIG set source_type=1 where source_code = :code",
                    configParams);
            namedParameterJdbcTemplate.update(
                    "update DATA_RULE_CONFIG set source_type=1 where source_code = :code",
                    configParams);
            namedParameterJdbcTemplate.update(
                    "update DATA_SERVICE_CONFIG set source_type=1 where source_code = :code",
                    configParams);
            namedParameterJdbcTemplate.update(
                    "update DATA_DETAIL_CONFIG set source_type=1 where source_code = :code",
                    configParams);
            namedParameterJdbcTemplate.update(
                    "update DATA_STATUS_CONFIG set source_type=1 where source_code = :code",
                    configParams);

            // 更新权限类型
            namedParameterJdbcTemplate.update(
                    "update role_auth_show set auth_type=3 where map_id = :code and auth_type=2",
                    configParams);
            namedParameterJdbcTemplate.update(
                    "update role_auth_op set auth_type=3 where tp_id = :code and auth_type=2",
                    configParams);
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_FORM, "表单转换为流程", params, dataCode);
            return 1;
        } catch (Exception e) {
            log.error("转换表单为工作流失败: {}", e.getMessage(), e);
            return Map.of("message", "转换失败: " + e.getMessage());
        }
    }

    private Map<String, Object> copyDataHandle(String dataCode, Map<String, Object> nodeInfo, String username) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("data_code", dataCode);
        dataMap.put("data_name", nodeInfo.get("data_name") + "【复制】");
        dataMap.put("data_sname", nodeInfo.get("data_sname") + "【复制】");
        dataMap.put("data_type", nodeInfo.get("data_type"));
        dataMap.put("data_design", nodeInfo.get("data_design"));
        dataMap.put("create_time", DateUtil.now());
        dataMap.put("creator", username);
        dataMap.put("data_status", 0);
        dataMap.put("is_share", 0);
        dataMap.put("open_discuss", 0);
        dataMap.put("is_anony", 0);
        dataMap.put("open_invent", nodeInfo.get("open_invent") == null ? 0 : nodeInfo.get("open_invent"));
        dataMap.put("ext_config", nodeInfo.get("ext_config"));
        dataMap.put("company_id", "");
        dataMap.put("data_state", JSONUtil.createObj().set("status", "启用").toString());
        return dataMap;
    }

    /**
     * 添加系统审批状态
     *
     * @param code 表单代码
     */
    private void addSysAudit(String code) {
        Map<String, Object> statusData = new HashMap<>();
        for (DataStatusEnum dataStatusEnum : DataStatusEnum.values()) {
            statusData.put("auto_no", IdUtil.getSnowflakeNextIdStr());
            statusData.put("source_code", code);
            statusData.put("source_type", 1);
            statusData.put("status_type", "审批");
            statusData.put("status_name", dataStatusEnum.getValue());
            statusData.put("status_value", dataStatusEnum.getKey());
            genericMapper.create("DATA_STATUS_CONFIG", statusData);
        }
    }

    /**
     * @description 获取表单/流程/视图的所有数据
     * <AUTHOR>
     */
    public List<Map<String, Object>> getDataList() {
        String sql = """
                SELECT
                    data_code AS 'code',
                    data_name AS 'name',
                    data_sname AS 'sname',
                    'data' AS 'type'
                FROM
                    node_ext_data
                WHERE
                    data_state ->> '$.status' = '启用'
                UNION ALL
                SELECT
                    wf_code AS 'code',
                    wf_name AS 'name',
                    wf_sname AS 'sname',
                    'flow' AS 'type'
                FROM
                    node_ext_wf
                WHERE
                    data_state ->> '$.status' = '启用'
                UNION ALL
                SELECT
                    view_code AS 'code',
                    view_name AS 'name',
                    view_sname AS 'sname',
                    'view' AS 'type'
                FROM
                    node_ext_view
                WHERE
                    data_state ->> '$.status' = '启用'
                """;
        return namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
    }

    private String CreateTable(List<TableField> fields, String code) throws Exception {
        BaseTableActuator actor = TableActuatorFactory.CreateActuator(driverClassName, namedParameterJdbcTemplate);
        return actor.Create(fields, code);
    }

    public boolean IsVirtualField(PsoDataDict f) {
        return f.ControlType.equals("attachment") || f.ControlType.equals("seal") || f.ControlType.equals("user") || f.ControlType.equals("tag");
    }

    private void AddTableField(List<TableField> fields, String code) throws Exception {
        BaseTableActuator actor = TableActuatorFactory.CreateActuator(driverClassName, namedParameterJdbcTemplate);
        actor.AddTableField(fields, code);
    }

    public void ModifyTableField(List<TableField> fields, String code) throws Exception {
        BaseTableActuator actor = TableActuatorFactory.CreateActuator(driverClassName, namedParameterJdbcTemplate);
        actor.ModifyTableField(fields, code);
    }

    public void DeleteTableField(List<TableField> fields, String code) throws Exception {
        BaseTableActuator actor = TableActuatorFactory.CreateActuator(driverClassName, namedParameterJdbcTemplate);
        actor.DeleteTableField(fields, code);
    }
}
