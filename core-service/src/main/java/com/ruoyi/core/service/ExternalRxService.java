package com.ruoyi.core.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.constants.PsoConstant;
import com.ruoyi.core.enums.AdministrationFrequency;
import com.ruoyi.core.enums.ExternalRxStatusEnum;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.ruoyi.core.service.LogService.MODULE_EXTERNAL_RX;

@Service
public class ExternalRxService {

    private final GenericMapper genericMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final LogService logService;

    public ExternalRxService(GenericMapper genericMapper, NamedParameterJdbcTemplate namedParameterJdbcTemplate, LogService logService) {
        this.genericMapper = genericMapper;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.logService = logService;
    }


    /**
     * @description 获取老人的外配药库存列表
     * <AUTHOR>
     */
    public Object inventory(Integer pageNum, Integer pageSize, String query) {
        String sql = """
                SELECT
                    IFNULL(leaf_id, '') AS leaf_id,
                    IFNULL(yaopmc, '') AS yaopmc,
                    IFNULL(yaopgg, '') AS yaopgg,
                    IFNULL(menzdw, '') AS menzdw,
                    IFNULL(geiytj, '') AS geiytj,
                    IFNULL(ciyl, '') AS ciyl,
                    IFNULL(ciyldw, '') AS ciyldw
                FROM
                    data_ext_waipyxxb
                WHERE 1=1
                """;

        int offset = (pageNum - 1) * pageSize;
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        if (StrUtil.isNotBlank(query)) {
            params.put("query", "%" + query + "%");
            sql = sql + " AND (yaoppy LIKE :query OR yaopmc LIKE :query)";
        }

        String countSql = "SELECT COUNT(*) FROM (" + sql + ") AS count_table";
        sql = sql + """
                 ORDER BY c_time DESC
                 LIMIT :offset, :pageSize
                """;

        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);
        Map<String, Object> map = createDefaultPageResult();
        if (CollectionUtil.isEmpty(list)) {
            return map;
        }

        Integer count = namedParameterJdbcTemplate.queryForObject(countSql, params, Integer.class);
        map.replace("rows", list);
        map.replace("total", count);
        return map;
    }

    /**
     * @description 新增外配药医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object add(Map<String, Object> params) {
        long id = IdUtil.getSnowflakeNextId();
        String username = SecurityUtils.getUsername();
        Map<String, Object> map = new HashMap<>(params);
        map.put("id", id);
        map.put("create_by", username);
        map.put("status", 0);

        // 新增外配药医嘱记录
        genericMapper.create("external_rx", map);

        // 如果给药途径为口服，则处理waipykc表的数据
        if (map.get("administration_route") != null && "口服".equals(map.get("administration_route").toString())) {
            handleOralMedicationLogic(map);
        }

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_EXTERNAL_RX, "新增外配药医嘱", map, null);
        return AjaxResult.success("添加成功", String.valueOf(id));
    }

    /**
     * @description 修改外配药医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object update(Long id, Map<String, Object> params) {
        String username = SecurityUtils.getUsername();
        Map<String, Object> updateValues = new HashMap<>(params);
        updateValues.put("update_by", username);

        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        condition.put("is_deleted", 0);

        int result = genericMapper.update("external_rx", updateValues, condition);
        if (result > 0) {
            // 如果给药途径为口服，则处理waipykc表的数据
            if (params.get("administration_route") != null && "口服".equals(params.get("administration_route").toString())) {
                // 需要先查询原有记录获取medical_record_id等信息
                Map<String, Object> originalRecord = getExternalRxById(id);
                if (originalRecord != null) {
                    // 合并原有记录和更新参数
                    Map<String, Object> mergedParams = new HashMap<>(originalRecord);
                    mergedParams.putAll(params);
                    handleOralMedicationLogic(mergedParams);
                }
            }
            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_EXTERNAL_RX, "修改外配药医嘱", params, null);
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.error("修改失败，记录不存在或已被删除");
        }
    }

    /**
     * @description 删除外配药医嘱
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object delete(Long id) {
        // 先查询要删除的记录信息，用于判断是否需要处理waipykc表
        Map<String, Object> recordToDelete = getExternalRxById(id);
        if (recordToDelete == null) {
            return AjaxResult.error("删除失败，记录不存在或已被删除");
        }

        String username = SecurityUtils.getUsername();
        Map<String, Object> updateValues = new HashMap<>();
        updateValues.put("is_deleted", 1);
        updateValues.put("update_by", username);

        Map<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        condition.put("is_deleted", 0);

        int result = genericMapper.update("external_rx", updateValues, condition);
        if (result > 0) {
            // 如果给药途径为口服，则清空waipykc表的相关字段
            if (recordToDelete.get("administration_route") != null &&
                "口服".equals(recordToDelete.get("administration_route").toString())) {
                clearOralMedicationFields(recordToDelete);
            }

            logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_EXTERNAL_RX, "删除外配药医嘱", Map.of("id", id), null);
            return AjaxResult.success("删除成功");
        } else {
            return AjaxResult.error("删除失败，记录不存在或已被删除");
        }
    }

    /**
     * @description 查看外配药医嘱详情
     * <AUTHOR>
     */
    public Object info(Long id) {
        String sql = selectAllSql() + " WHERE id = :id AND is_deleted = 0";
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);

        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);

            // 添加 status_label 字段
            Object statusObj = result.get("status");
            if (statusObj != null) {
                int status = Integer.parseInt(statusObj.toString());
                result.put("status_label", ExternalRxStatusEnum.getValueByKey(status));
            } else {
                result.put("status_label", "未知状态");
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("记录不存在");
        }
    }

    /**
     * @description 根据medical_record_id查询外配药医嘱列表
     * <AUTHOR>
     */
    public Object list(String medicalRecordId) {
        String sql = selectAllSql() + " WHERE medical_record_id = :medical_record_id AND is_deleted = 0 ORDER BY create_time DESC";
        Map<String, Object> params = new HashMap<>();
        params.put("medical_record_id", medicalRecordId);

        List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);

        if (CollectionUtil.isNotEmpty(list)) {
            // 为每个记录添加 status_label 字段
            for (Map<String, Object> record : list) {
                Object statusObj = record.get("status");
                if (statusObj != null) {
                    int status = Integer.parseInt(statusObj.toString());
                    record.put("status_label", ExternalRxStatusEnum.getValueByKey(status));
                } else {
                    record.put("status_label", "未知状态");
                }
            }
        }
        return AjaxResult.success(list);
    }

    /**
     * @description 获取外配药处方信息（处方号和处方日期）
     * <AUTHOR>
     */
    public Object getPrescriptionInfo(String patientId) {
        String sql = """
                SELECT
                    prescription_number,
                    prescription_date,
                    status
                FROM
                    `external_rx`
                WHERE
                    medical_record_id = :patientId
                    AND is_deleted = 0
                LIMIT 1
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("patientId", patientId);

        // 没有记录时返回空对象
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("prescription_number", null);
        emptyResult.put("prescription_date", null);
        emptyResult.put("flag", false);
        emptyResult.put("cancelFlag", false);

        try {
            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);
            if (result.get("status").toString().equals("0") || result.get("status").toString().equals("4")) {
                return AjaxResult.success(emptyResult);
            }

            // 处理处方日期格式
            Object prescriptionDateObj = result.get("prescription_date");
            if (prescriptionDateObj != null) {
                String prescriptionDate = prescriptionDateObj.toString();
                DateTime dt = DateUtil.parse(prescriptionDate, "yyyy-MM-dd'T'HH:mm:ss");
                String formatted = DateUtil.format(dt, "yyyy-MM-dd HH:mm:ss");
                result.replace("prescription_date", formatted);
            }

            // true为可以取消开方
            result.put("flag", true);
            if (result.get("status").toString().equals("1")) {
                result.put("cancelFlag", true);
            } else {
                result.put("cancelFlag", false);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.success(emptyResult);
        }
    }

    /**
     * @description 获取外配药上次使用情况
     * <AUTHOR>
     */
    public AjaxResult getLastUsageInfo(String patientId, String drugStockId) {
        // 1. 根据patientId查询患者信息
        String patientInfoSql = """
                SELECT xingming, zhuyuanh
                FROM data_ext_menzbl
                WHERE leaf_id = :patientId
                """;

        Map<String, Object> patientParams = new HashMap<>();
        patientParams.put("patientId", patientId);

        Map<String, Object> patientInfo;
        try {
            patientInfo = namedParameterJdbcTemplate.queryForMap(patientInfoSql, patientParams);
        } catch (Exception e) {
            return AjaxResult.success("success", "");
        }

        String xingming = patientInfo.get("xingming") != null ? patientInfo.get("xingming").toString() : "";
        String zhuyuanh = patientInfo.get("zhuyuanh") != null ? patientInfo.get("zhuyuanh").toString() : "";

        if (StrUtil.isBlank(xingming) || StrUtil.isBlank(zhuyuanh)) {
            return AjaxResult.success("success", "");
        }

        // 2. 根据xingming和zhuyuanh查询所有门诊病历ID
        String allRecordsSql = """
                SELECT leaf_id
                FROM data_ext_menzbl
                WHERE xingming = :xingming AND zhuyuanh = :zhuyuanh
                ORDER BY c_time DESC
                """;

        Map<String, Object> recordParams = new HashMap<>();
        recordParams.put("xingming", xingming);
        recordParams.put("zhuyuanh", zhuyuanh);

        List<Map<String, Object>> allRecords;
        try {
            allRecords = namedParameterJdbcTemplate.queryForList(allRecordsSql, recordParams);
        } catch (Exception e) {
            return AjaxResult.success("success", "");
        }

        if (CollectionUtil.isEmpty(allRecords)) {
            return AjaxResult.success("success", "");
        }

        // 提取所有门诊病历ID列表
        List<String> allMedicalRecordIds = allRecords.stream()
                .map(record -> record.get("leaf_id").toString())
                .toList();

        // 3. 根据门诊病历ID和drug_stock_id查询最近一条外配药使用记录
        String lastUsageSql = """
                SELECT
                    drug_name,
                    drug_spec,
                    drug_unit,
                    prescription_quantity
                FROM `external_rx`
                WHERE medical_record_id IN (:medicalRecordIds)
                AND drug_stock_id = :drugStockId
                AND is_deleted = 0 AND status != 5
                ORDER BY create_time DESC
                LIMIT 1
                """;

        Map<String, Object> usageParams = new HashMap<>();
        usageParams.put("medicalRecordIds", allMedicalRecordIds);
        usageParams.put("drugStockId", drugStockId);

        Map<String, Object> lastUsage;
        try {
            lastUsage = namedParameterJdbcTemplate.queryForMap(lastUsageSql, usageParams);
        } catch (Exception e) {
            // 没有找到记录，返回空字符串
            return AjaxResult.success("success", "");
        }

        // 4. 构建返回字符串（外配药表没有packaging相关字段，直接使用drug_name + drug_spec + prescription_quantity + drug_unit）
        String drugName = lastUsage.get("drug_name") != null ? lastUsage.get("drug_name").toString() : "";
        String drugSpec = lastUsage.get("drug_spec") != null ? lastUsage.get("drug_spec").toString() : "";
        String drugUnit = lastUsage.get("drug_unit") != null ? lastUsage.get("drug_unit").toString() : "";
        Object prescriptionQuantityObj = lastUsage.get("prescription_quantity");
        String prescriptionQuantity = prescriptionQuantityObj != null ? prescriptionQuantityObj.toString() : "";

        // 外配药表格式：drug_name + drug_spec + prescription_quantity + drug_unit
        String result = drugName + drugSpec + prescriptionQuantity + drugUnit;

        return AjaxResult.success("success", result);
    }

    /**
     * @description 外配药开方
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object prescribe(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要开方的医嘱");
        }

        // 生成统一的处方号和处方日期
        long prescriptionNumber = cn.hutool.core.util.RandomUtil.randomLong(100000, 999999);
        String prescriptionDate = DateUtil.now();

        // 查询需要开方的医嘱
        String queryOrdersSql = """
                SELECT id
                FROM `external_rx`
                WHERE id IN (:ids)
                AND is_deleted = 0
                AND (status = 0 OR status = 4)
                """;

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ids", ids);

        List<Map<String, Object>> orders;
        try {
            orders = namedParameterJdbcTemplate.queryForList(queryOrdersSql, queryParams);
        } catch (Exception e) {
            return AjaxResult.error("查询医嘱信息失败");
        }

        if (orders.isEmpty()) {
            return AjaxResult.error("未找到可开方的医嘱记录");
        }

        // 批量更新医嘱状态（外配药不需要扣除库存）
        String updateOrderSql = """
                UPDATE `external_rx`
                SET status = 1,
                    prescription_number = :prescription_number,
                    prescription_date = :prescription_date,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("prescription_number", prescriptionNumber);
        updateParams.put("prescription_date", prescriptionDate);
        updateParams.put("update_by", SecurityUtils.getUsername());
        updateParams.put("ids", ids);

        namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        // 开方成功后，为每个医嘱插入doctor_order记录
        for (Long id : ids) {
            insertDoctorOrderOnPrescribe(id, SecurityUtils.getUsername());
        }

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_EXTERNAL_RX, "外配药医嘱开方", ids, null);
        return AjaxResult.success("开方成功，处方号：" + prescriptionNumber);
    }

    /**
     * @description 外配药取消开方
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object cancelPrescription(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return AjaxResult.error("请选择需要取消开方的医嘱");
        }

        // 查询需要取消开方的医嘱
        String queryOrdersSql = """
                SELECT id
                FROM `external_rx`
                WHERE id IN (:ids)
                AND is_deleted = 0
                AND status = 1
                """;

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ids", ids);

        List<Map<String, Object>> orders;
        try {
            orders = namedParameterJdbcTemplate.queryForList(queryOrdersSql, queryParams);
        } catch (Exception e) {
            return AjaxResult.error("查询医嘱信息失败");
        }

        if (orders.isEmpty()) {
            return AjaxResult.error("未找到已开方的医嘱记录");
        }

        // 批量更新医嘱状态（外配药取消开方状态为4）
        String updateOrderSql = """
                UPDATE `external_rx`
                SET status = 4,
                    prescription_number = NULL,
                    prescription_date = NULL,
                    update_time = NOW(),
                    update_by = :update_by
                WHERE id IN (:ids)
                """;

        Map<String, Object> updateParams = new HashMap<>();
        updateParams.put("update_by", SecurityUtils.getUsername());
        updateParams.put("ids", ids);

        namedParameterJdbcTemplate.update(updateOrderSql, updateParams);

        // 删除doctor_order表中的记录
        for (Long id : ids) {
            deleteDoctorOrder(id);
        }

        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_EXTERNAL_RX, "外配药医嘱取消开方", ids, null);
        return AjaxResult.success("取消开方成功");
    }

    /**
     * @description 查询所有字段的SQL
     * <AUTHOR>
     */
    private String selectAllSql() {
        return """
                SELECT
                    CAST(id AS CHAR) AS id,
                    medical_record_id,
                    prescription_number,
                    prescription_date,
                    drug_stock_id,
                    drug_name,
                    drug_spec,
                    drug_unit,
                    status,
                    part,
                    administration_route,
                    administration_method,
                    prescription_quantity,
                    week_day,
                    morning_quantity,
                    morning_time,
                    noon_quantity,
                    noon_time,
                    evening_quantity,
                    evening_time,
                    night_quantity,
                    night_time,
                    prompt,
                    drug_source,
                    single_dose,
                    single_dose_unit,
                    order_date,
                    CAST(attending_doctor_id AS CHAR) AS attending_doctor_id,
                    order_type,
                    order_time,
                    doctor_name,
                    nurse_name,
                    disease_type,
                    is_deleted,
                    create_time,
                    create_by,
                    update_time,
                    update_by,
                    'exist' as state
                FROM external_rx
                """;
    }

    /**
     * @param externalRxId 外配药医嘱ID
     * @param username     用户名
     * @description 开方时插入医嘱表记录
     */
    private void insertDoctorOrderOnPrescribe(Long externalRxId, String username) {
        try {
            // 查询external_rx的完整数据
            String querySql = """
                    SELECT * FROM `external_rx`
                    WHERE id = :id AND is_deleted = 0 AND order_type != '继续开药'
                    """;
            Map<String, Object> externalRxData;
            try {
                externalRxData = namedParameterJdbcTemplate.queryForMap(
                        querySql, Map.of("id", externalRxId));
            } catch (Exception e) {
                return;
            }

            // 获取患者信息
            String medicalRecordId = externalRxData.get("medical_record_id").toString();
            Map<String, Object> patientInfo = getPatientInfo(medicalRecordId);

            // 生成医嘱详细内容
            String content = generateOrderContent(externalRxData);

            // 构建doctor_order数据
            Map<String, Object> doctorOrderData = new HashMap<>();
            doctorOrderData.put("id", IdUtil.getSnowflakeNextId());
            doctorOrderData.put("medication_order_id", externalRxId);
            doctorOrderData.put("area", patientInfo.get("quy"));
            doctorOrderData.put("bed_number", patientInfo.get("chuangw_no"));
            doctorOrderData.put("hospitalization_id", patientInfo.get("zhuyuanh"));
            doctorOrderData.put("patient_name", patientInfo.get("xingming"));
            doctorOrderData.put("order_time", DateUtil.now());
            doctorOrderData.put("doctor_name", username);
            doctorOrderData.put("order_name", "外配药医嘱");
            doctorOrderData.put("order_type", externalRxData.get("order_type").toString());
            doctorOrderData.put("content", content);
            doctorOrderData.put("created_by", username);
            doctorOrderData.put("is_deleted", 0);
            doctorOrderData.put("status", 0);
            if (externalRxData.get("order_type").toString().equals("长期")) {
                doctorOrderData.put("is_stopped", 0);
            }

            genericMapper.create("doctor_order", doctorOrderData);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("开方时插入医嘱表失败: " + e.getMessage());
        }
    }

    /**
     * @param externalRxId 外配药医嘱ID
     * @description 删除医嘱表记录
     */
    private void deleteDoctorOrder(Long externalRxId) {
        try {
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("is_deleted", 1);

            Map<String, Object> condition = new HashMap<>();
            condition.put("medication_order_id", externalRxId);

            genericMapper.update("doctor_order", updateData, condition);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("删除医嘱表记录失败: " + e.getMessage());
        }
    }

    /**
     * @param medicalRecordId 门诊病历ID
     * @return 患者信息
     * @description 获取患者信息
     */
    private Map<String, Object> getPatientInfo(String medicalRecordId) {
        String sql = """
                SELECT quy, chuangw_no, zhuyuanh, xingming
                FROM data_ext_menzbl
                WHERE leaf_id = :medicalRecordId
                """;
        try {
            return namedParameterJdbcTemplate.queryForMap(sql, Map.of("medicalRecordId", medicalRecordId));
        } catch (Exception e) {
            // 返回默认值
            Map<String, Object> defaultInfo = new HashMap<>();
            defaultInfo.put("quy", "");
            defaultInfo.put("chuangw_no", "");
            defaultInfo.put("zhuyuanh", "");
            defaultInfo.put("xingming", "");
            return defaultInfo;
        }
    }

    /**
     * @param externalRxData 外配药医嘱数据
     * @return 医嘱详细内容
     * @description 生成外配药医嘱详细内容
     */
    private String generateOrderContent(Map<String, Object> externalRxData) {
        String drugName = getStringValue(externalRxData, "drug_name");
        String drugSpec = getStringValue(externalRxData, "drug_spec");
        String singleDose = getStringValue(externalRxData, "single_dose");
        String administrationMethod = getStringValue(externalRxData, "administration_method");
        String administrationRoute = getStringValue(externalRxData, "administration_route");
        String prompt = getStringValue(externalRxData, "prompt");
        String orderType = getStringValue(externalRxData, "order_type");

        Object prescriptionQuantityObj = externalRxData.get("prescription_quantity");
        String prescriptionQuantity = prescriptionQuantityObj != null ? prescriptionQuantityObj.toString() : "";

        // 将"口服"替换为"po"
        if ("口服".equals(administrationRoute)) {
            administrationRoute = "po";
        }

        StringBuilder content = new StringBuilder();

        if ("长期".equals(orderType)) {
            // 长期医嘱：药品名称+次用量+给药方法+给药途径+提示+外配
            content.append(drugName);
            if (StrUtil.isNotBlank(singleDose)) {
                content.append(singleDose);
            }
            if (StrUtil.isNotBlank(administrationMethod)) {
                content.append(administrationMethod);
            }
            if (StrUtil.isNotBlank(administrationRoute)) {
                content.append(administrationRoute);
            }
            if (StrUtil.isNotBlank(prompt)) {
                content.append(prompt);
            }
            content.append("外配");
        } else if ("临时".equals(orderType)) {
            // 临时医嘱：药品名称+规格+数量+sig：+次用量+给药方法+给药途径+提示+外配
            content.append(drugName);
            if (StrUtil.isNotBlank(drugSpec)) {
                content.append(drugSpec);
            }
            if (StrUtil.isNotBlank(prescriptionQuantity)) {
                content.append(prescriptionQuantity);
            }
            content.append("sig：");
            if (StrUtil.isNotBlank(singleDose)) {
                content.append(singleDose);
            }
            if (StrUtil.isNotBlank(administrationMethod)) {
                content.append(administrationMethod);
            }
            if (StrUtil.isNotBlank(administrationRoute)) {
                content.append(administrationRoute);
            }
            if (StrUtil.isNotBlank(prompt)) {
                content.append(prompt);
            }
            content.append("外配");
        } else {
            // 继续开药：暂时不需要内容，返回空字符串
            return "";
        }

        return content.toString();
    }

    /**
     * @param map 数据Map
     * @param key 键
     * @return 字符串值，如果为null则返回空字符串
     * @description 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 创建默认的分页返回结果
     *
     * @return 包含默认值的Map对象
     */
    private Map<String, Object> createDefaultPageResult() {
        Map<String, Object> countMap = new HashMap<>();
        countMap.put("code", 200);
        countMap.put("rows", Collections.EMPTY_LIST);
        countMap.put("total", 0);
        return countMap;
    }

    /**
     * @description 获取医嘱时间和医嘱日期
     * <AUTHOR>
     */
    public AjaxResult getDateAndTime(Long patientId) {
        String sql = """
                select zhendsj,zhendrq from data_ext_menzbl where leaf_id = :patientId
                """;
        return AjaxResult.success(namedParameterJdbcTemplate.queryForMap(sql, Map.of("patientId", patientId)));
    }

    /**
     * @param id 医嘱ID
     * @return 医嘱记录
     * @description 根据ID获取外配药医嘱记录
     */
    private Map<String, Object> getExternalRxById(Long id) {
        try {
            String sql = selectAllSql() + " WHERE id = :id AND is_deleted = 0";
            Map<String, Object> params = new HashMap<>();
            params.put("id", id);
            return namedParameterJdbcTemplate.queryForMap(sql, params);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * @param map 外配药医嘱数据
     * @description 处理口服药品的waipykc表逻辑
     */
    private void handleOralMedicationLogic(Map<String, Object> map) {
        try {
            // 1. 根据medical_record_id查询患者信息
            String medicalRecordId = map.get("medical_record_id").toString();
            String patientInfoSql = """
                    SELECT zhuyuanh, xingming, quy, chuangw_no
                    FROM data_ext_menzbl
                    WHERE leaf_id = :medicalRecordId
                    """;
            Map<String, Object> patientParams = new HashMap<>();
            patientParams.put("medicalRecordId", medicalRecordId);

            Map<String, Object> patientInfo = namedParameterJdbcTemplate.queryForMap(patientInfoSql, patientParams);
            String zhuyuanh = patientInfo.get("zhuyuanh").toString();
            String xingming = patientInfo.get("xingming").toString();
            String quy = patientInfo.get("quy").toString();
            String chuangwNo = patientInfo.get("chuangw_no").toString();

            // 2. 查询waipykc表中是否已存在记录
            String checkExistSql = """
                    SELECT leaf_id, kucsl, ciyl, rics, meiryl
                    FROM data_ext_waipykc
                    WHERE yaopmc = :drugName AND zhuyh = :zhuyuanh AND xingm = :xingming
                    """;
            Map<String, Object> checkParams = new HashMap<>();
            checkParams.put("drugName", map.get("drug_name").toString());
            checkParams.put("zhuyuanh", zhuyuanh);
            checkParams.put("xingming", xingming);
            String drugStockId;
            try {
                // 如果查询到数据，则是编辑
                Map<String, Object> existingRecord = namedParameterJdbcTemplate.queryForMap(checkExistSql, checkParams);
                drugStockId = updateWaipykcRecord(existingRecord, map);
            } catch (Exception e) {
                // 如果查询不到数据，则是新增
                drugStockId = insertWaipykcRecord(map, zhuyuanh, xingming, quy, chuangwNo);
            }
            // 将库存id设置到map中，供后续使用
            map.put("drug_stock_id", drugStockId);

            // 更新external_rx表中的drug_stock_id字段
            updateExternalRxDrugStockId(map, drugStockId);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("处理口服药品waipykc表逻辑时发生错误: " + e.getMessage());
        }
    }

    /**
     * @description 新增waipykc记录
     */
    private String insertWaipykcRecord(Map<String, Object> map, String zhuyuanh, String xingming, String quy, String chuangwNo) {
        String username = SecurityUtils.getUsername();
        Long userId = SecurityUtils.getUserId();

        Map<String, Object> waipykcMap = new HashMap<>();
        String recordId = IdUtil.getSnowflakeNextIdStr();

        // 默认字段
        waipykcMap.put("leaf_id", recordId);
        waipykcMap.put("creator", userId);
        waipykcMap.put("x_creator", username);
        waipykcMap.put("c_time", DateUtil.now());
        waipykcMap.put("app_id", PsoConstant.APP_ID);
        waipykcMap.put("data_code", "waipykc");
        waipykcMap.put("d_status", 0);
        waipykcMap.put("d_stage", 0);
        waipykcMap.put("d_audit", 0);
        waipykcMap.put("sync_status", 0);
        waipykcMap.put("is_chain", 0);
        waipykcMap.put("pub_status", 0);
        waipykcMap.put("share_status", 0);

        // 患者信息字段
        waipykcMap.put("zhuyh", zhuyuanh);
        waipykcMap.put("xingm", xingming);
        waipykcMap.put("quy", quy);
        waipykcMap.put("chuangwh", chuangwNo);

        // 药品信息字段
        String drugName = map.get("drug_name").toString();
        waipykcMap.put("yaopmc", drugName);
        waipykcMap.put("yaopgg", map.get("drug_spec"));
        waipykcMap.put("yaoppy", PinyinUtil.getFirstLetter(drugName, ""));
        waipykcMap.put("yaoply", "外配药");
        waipykcMap.put("tixrq", DateUtil.today());
        waipykcMap.put("kucsl", "0");
        waipykcMap.put("ciyl", map.get("single_dose"));
        waipykcMap.put("yaopdw", map.get("single_dose_unit"));
        waipykcMap.put("rics", map.get("administration_method"));

        // 计算每日用量
        String dailyDosage = calculateDailyDosage(map);
        waipykcMap.put("meiryl", dailyDosage);

        // 插入记录
        genericMapper.create("data_ext_waipykc", waipykcMap);
        return recordId;
    }

    /**
     * @description 更新waipykc记录
     */
    private String updateWaipykcRecord(Map<String, Object> existingRecord, Map<String, Object> map) {
        String leafId = existingRecord.get("leaf_id").toString();
        String kucsl = existingRecord.get("kucsl").toString();

        Map<String, Object> updateValues = new HashMap<>();
        updateValues.put("ciyl", map.get("single_dose"));
        updateValues.put("rics", map.get("administration_method"));

        // 计算每日用量
        String dailyDosage = calculateDailyDosage(map);
        updateValues.put("meiryl", dailyDosage);

        // 计算预计到期日期
        String expectedExpiryDate = calculateExpectedExpiryDate(kucsl, dailyDosage);
        updateValues.put("tixrq", expectedExpiryDate);

        Map<String, Object> condition = new HashMap<>();
        condition.put("leaf_id", leafId);

        genericMapper.update("data_ext_waipykc", updateValues, condition);
        return leafId;
    }

    /**
     * @description 计算每日用量
     */
    private String calculateDailyDosage(Map<String, Object> map) {
        try {
            String singleDose = map.get("single_dose").toString();
            String singleDoseUnit = map.get("single_dose_unit").toString();
            String administrationMethod = map.get("administration_method").toString();

            // 获取给药频次
            double frequency = getFrequencyForCalculation(administrationMethod, map);

            // 判断次用量是否为纯数字
            boolean isPureNumber = singleDose.matches("^\\d+(\\.\\d+)?$");

            // 计算每日计量
            double totalDailyDosage;
            if (isPureNumber) {
                // 如果是纯数字，则 totalDailyDosage = 次用量 * frequency
                totalDailyDosage = Double.parseDouble(singleDose) * frequency;
            } else {
                // 如果不是纯数字，使用正则表达式提取次用量中的数字
                List<Double> dosageNumbers = extractDosageNumbers(singleDose, singleDoseUnit);
                if (dosageNumbers.isEmpty()) {
                    // 如果没有提取到数字，返回默认值
                    return "1" + singleDoseUnit;
                }
                // totalDailyDosage 则是正则匹配后的数字的总和
                totalDailyDosage = dosageNumbers.stream().mapToDouble(Double::doubleValue).sum();
            }

            // 查询次用量因子和主用单位
            String factorSql = """
                    SELECT ciyl, zhuydw
                    FROM data_ext_waipyxxb
                    WHERE yaopmc = :drugName
                    """;
            Map<String, Object> factorParams = new HashMap<>();
            factorParams.put("drugName", map.get("drug_name").toString());

            try {
                Map<String, Object> factorInfo = namedParameterJdbcTemplate.queryForMap(factorSql, factorParams);
                String ciylFactor = factorInfo.get("ciyl").toString();
                String zhuydw = factorInfo.get("zhuydw").toString();

                // 解析次用量因子中的数字
                double factor = extractNumericValue(ciylFactor);
                if (factor > 0) {
                    double finalDailyUsage = totalDailyDosage / factor;
                    return formatNumber(finalDailyUsage) + zhuydw;
                }
            } catch (Exception e) {
                // 如果查询不到因子信息，直接返回计算的每日剂量
            }

            return formatNumber(totalDailyDosage) + singleDoseUnit;
        } catch (Exception e) {
            return "1mg"; // 默认值
        }
    }

    /**
     * @description 使用正则表达式提取次用量中的数字
     */
    private List<Double> extractDosageNumbers(String singleDose, String unit) {
        List<Double> numbers = new ArrayList<>();
        if (StrUtil.isBlank(singleDose) || StrUtil.isBlank(unit)) {
            return numbers;
        }

        // 构建正则表达式：匹配单位前的数字（包括小数）
        String regex = "(\\d+(?:\\.\\d+)?)" + Pattern.quote(unit);
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(singleDose);

        while (matcher.find()) {
            try {
                double number = Double.parseDouble(matcher.group(1));
                numbers.add(number);
            } catch (NumberFormatException e) {
                // 忽略无法解析的数字
            }
        }

        return numbers;
    }

    /**
     * @description 获取用于计算的给药频次
     */
    private double getFrequencyForCalculation(String administrationMethod, Map<String, Object> map) {
        if (StrUtil.isBlank(administrationMethod)) {
            return 1.0;
        }

        String method = administrationMethod.toLowerCase().trim();

        // 对于 q4h, q6h, q8h, q12h，根据早中晚夜数量字段计算频次
        if ("q4h".equals(method) || "q6h".equals(method) || "q8h".equals(method) || "q12h".equals(method)) {
            Double calculatedFrequency = calculateDailyFrequencyFromQuantities(map);
            if (calculatedFrequency != null && calculatedFrequency > 0) {
                return calculatedFrequency;
            }
            // 如果无法从数量字段计算，则使用枚举的默认值
            return AdministrationFrequency.getFrequencyByName(administrationMethod);
        }

        // 其他频次使用枚举的值
        return AdministrationFrequency.getFrequencyByName(administrationMethod);
    }

    /**
     * @description 根据早中晚夜数量计算每日频次
     */
    private Double calculateDailyFrequencyFromQuantities(Map<String, Object> map) {
        try {
            int count = 0;

            Object morningQuantity = map.get("morning_quantity");
            if (morningQuantity != null && Double.parseDouble(morningQuantity.toString()) > 0) {
                count++;
            }

            Object noonQuantity = map.get("noon_quantity");
            if (noonQuantity != null && Double.parseDouble(noonQuantity.toString()) > 0) {
                count++;
            }

            Object eveningQuantity = map.get("evening_quantity");
            if (eveningQuantity != null && Double.parseDouble(eveningQuantity.toString()) > 0) {
                count++;
            }

            Object nightQuantity = map.get("night_quantity");
            if (nightQuantity != null && Double.parseDouble(nightQuantity.toString()) > 0) {
                count++;
            }

            return count > 0 ? (double) count : null;

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * @description 从字符串中提取数字值
     */
    private double extractNumericValue(String str) {
        if (StrUtil.isBlank(str)) {
            return 0;
        }

        Pattern pattern = Pattern.compile("(\\d+(?:\\.\\d+)?)");
        Matcher matcher = pattern.matcher(str);

        if (matcher.find()) {
            try {
                return Double.parseDouble(matcher.group(1));
            } catch (NumberFormatException e) {
                return 0;
            }
        }

        return 0;
    }

    /**
     * @description 格式化数字，去除不必要的小数点
     */
    private String formatNumber(double number) {
        BigDecimal bd = new BigDecimal(number);
        bd = bd.setScale(2, RoundingMode.HALF_UP);

        // 如果是整数，去掉小数点
        if (bd.stripTrailingZeros().scale() <= 0) {
            return String.valueOf(bd.intValue());
        } else {
            return bd.stripTrailingZeros().toPlainString();
        }
    }

    /**
     * @param kucsl      库存数量
     * @param dailyUsage 每日用量
     * @return 预计到期日期
     * @description 计算预计到期日期
     */
    private String calculateExpectedExpiryDate(String kucsl, String dailyUsage) {
        try {
            // 提取库存数量中的数字
            double stockQuantity = extractNumericValue(kucsl);

            // 提取每日用量中的数字
            double dailyUsageQuantity = extractNumericValue(dailyUsage);

            if (stockQuantity <= 0 || dailyUsageQuantity <= 0) {
                return DateUtil.today();
            }

            // 计算可用天数
            int daysAvailable = (int) Math.floor(stockQuantity / dailyUsageQuantity);

            // 计算到期日期
            DateTime currentDate = DateUtil.date();
            DateTime expiryDate = DateUtil.offsetDay(currentDate, daysAvailable);

            return DateUtil.formatDate(expiryDate);
        } catch (Exception e) {
            return DateUtil.today();
        }
    }

    /**
     * @param map 外配药医嘱数据
     * @return 库存id
     * @description 根据药品名称和规格查询外配药库存表获取库存id
     */
    private String getDrugStockId(Map<String, Object> map) {
        try {
            String drugName = map.get("drug_name").toString();
            String drugSpec = map.get("drug_spec") != null ? map.get("drug_spec").toString() : "";

            String sql = """
                    SELECT leaf_id
                    FROM data_ext_waipyxxb
                    WHERE yaopmc = :drugName
                    """;

            Map<String, Object> params = new HashMap<>();
            params.put("drugName", drugName);

            // 如果有规格信息，加入查询条件
            if (StrUtil.isNotBlank(drugSpec)) {
                sql += " AND yaopgg = :drugSpec";
                params.put("drugSpec", drugSpec);
            }

            sql += " LIMIT 1";

            Map<String, Object> result = namedParameterJdbcTemplate.queryForMap(sql, params);
            return result.get("leaf_id").toString();
        } catch (Exception e) {
            // 查询不到对应的库存记录，返回空字符串
            System.err.println("查询外配药库存id失败: " + e.getMessage());
            return "";
        }
    }

    /**
     * @param map 外配药医嘱数据
     * @param drugStockId 库存id
     * @description 更新external_rx表中的drug_stock_id字段
     */
    private void updateExternalRxDrugStockId(Map<String, Object> map, String drugStockId) {
        try {
            Long id = Long.valueOf(map.get("id").toString());

            Map<String, Object> updateValues = new HashMap<>();
            updateValues.put("drug_stock_id", drugStockId);

            Map<String, Object> condition = new HashMap<>();
            condition.put("id", id);

            genericMapper.update("external_rx", updateValues, condition);
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("更新external_rx表drug_stock_id字段失败: " + e.getMessage());
        }
    }

    /**
     * @param recordToDelete 要删除的外配药医嘱记录
     * @description 清空口服药品在waipykc表中的相关字段
     */
    private void clearOralMedicationFields(Map<String, Object> recordToDelete) {
        try {
            // 1. 根据medical_record_id查询患者信息
            String medicalRecordId = recordToDelete.get("medical_record_id").toString();
            String patientInfoSql = """
                    SELECT zhuyuanh, xingming
                    FROM data_ext_menzbl
                    WHERE leaf_id = :medicalRecordId
                    """;
            Map<String, Object> patientParams = new HashMap<>();
            patientParams.put("medicalRecordId", medicalRecordId);

            Map<String, Object> patientInfo = namedParameterJdbcTemplate.queryForMap(patientInfoSql, patientParams);
            String zhuyuanh = patientInfo.get("zhuyuanh").toString();
            String xingming = patientInfo.get("xingming").toString();

            // 2. 查询waipykc表中对应的记录
            String drugName = recordToDelete.get("drug_name").toString();
            String checkExistSql = """
                    SELECT leaf_id
                    FROM data_ext_waipykc
                    WHERE yaopmc = :drugName AND zhuyh = :zhuyuanh AND xingm = :xingming
                    """;
            Map<String, Object> checkParams = new HashMap<>();
            checkParams.put("drugName", drugName);
            checkParams.put("zhuyuanh", zhuyuanh);
            checkParams.put("xingming", xingming);

            try {
                // 如果查询到数据，则清空相关字段
                Map<String, Object> existingRecord = namedParameterJdbcTemplate.queryForMap(checkExistSql, checkParams);
                String leafId = existingRecord.get("leaf_id").toString();

                // 清空ciyl、rics、meiryl、tixrq字段
                Map<String, Object> updateValues = new HashMap<>();
                updateValues.put("ciyl", "");
                updateValues.put("rics", "");
                updateValues.put("meiryl", "");
                updateValues.put("tixrq", "");

                Map<String, Object> condition = new HashMap<>();
                condition.put("leaf_id", leafId);

                genericMapper.update("data_ext_waipykc", updateValues, condition);
            } catch (Exception e) {
                // 如果查询不到数据，说明waipykc表中没有对应记录，无需处理
            }
        } catch (Exception e) {
            // 记录错误日志，但不影响主流程
            System.err.println("清空口服药品waipykc表字段时发生错误: " + e.getMessage());
        }
    }
}
