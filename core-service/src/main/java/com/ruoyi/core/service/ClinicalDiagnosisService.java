package com.ruoyi.core.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.mapper.GenericMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 临床诊断
 */
@Slf4j
@Service
public class ClinicalDiagnosisService {
    public static final String TABLE_NAME = "data_ext_laorzd";
    public static final String MODULE_NAME = "临床诊断";
    private final LogService logService;
    private final GenericMapper genericMapper;

    public ClinicalDiagnosisService(LogService logService, GenericMapper genericMapper) {
        this.logService = logService;
        this.genericMapper = genericMapper;
    }

    public void remove(String id) {
        List<String[]> condition = List.<String[]>of(new String[]{"equal", "leaf_id", id});
        List<Map<String, Object>> list = this.genericMapper.getList(TABLE_NAME, "*", condition, "limit 1");
        if (list == null || list.isEmpty()) {
            throw new RuntimeException("未找到初步诊断记录");
        }
        this.genericMapper.update(TABLE_NAME, Map.of("d_status", 1), Map.of("leaf_id", id));
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> row = list.get(0);
        String dataJson;
        try {
            dataJson = objectMapper.writeValueAsString(row);
            row.put("d_status", "1");
            String dataJson1 = objectMapper.writeValueAsString(row);
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_NAME, "删除初步诊断", dataJson1, dataJson);
        } catch (Exception e) {
            log.error("Error converting data to JSON", e);
        }
    }

    public List<Map<String, Object>> getListByZhuyh(String zhuyh, String zhendlx) {
        List<String[]> condition = new ArrayList<>();
        condition.add(new String[]{"equal", "zhuyh", zhuyh});
        condition.add(new String[]{"equal", "zhendlx", zhendlx});
        condition.add(new String[]{"equal", "d_status", "0"});
        List<Map<String, Object>> rows = this.genericMapper.getList(TABLE_NAME, "*", condition, "limit 10");
        if (rows == null || rows.isEmpty()) {
            return new ArrayList<>();
        }
        return rows;
    }

    @Transactional(rollbackFor = Exception.class)
    public void create(Map<String, Object> data) {
        data.put("app_id", "");
        data.put("leaf_id", IdUtil.getSnowflakeNextIdStr());
        data.put("data_code", "laorzd");
        data.put("d_status", "0");
        data.put("d_stage", "0");
        data.put("d_audit", "0");
        data.put("is_chain", "0");
        data.put("pub_status", "0");
        data.put("sync_status", "0");
        data.put("creator", SecurityUtils.getUsername());
        data.put("c_time", DateUtil.formatDateTime(new Date()));
        this.genericMapper.create(TABLE_NAME, data);
        ObjectMapper objectMapper = new ObjectMapper();
        String dataJson;
        try {
            dataJson = objectMapper.writeValueAsString(data);
            this.logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_NAME, "新增初步诊断", dataJson, "{}");
        } catch (Exception e) {
            log.error("Error converting data to JSON", e);
        }
    }
}
