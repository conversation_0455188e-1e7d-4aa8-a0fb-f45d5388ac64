package com.ruoyi.core.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.core.mapper.GenericMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/*
CREATE TABLE `yzlc`.`Untitled`  (
            `id` bigint(20) NOT NULL,
            `data_state` json NULL COMMENT '{\"created_at\":\"\"}',
            `relation_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '账号id/角色',
            `relation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '占位（账号、角色）',
            `reference` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '列、行所在的表名',
            `reference_detail` json NULL COMMENT '{\"type\":\"数据行/列\",\"column\":\"auto_no\",\"value\":\"1\"}',
            `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '黑名单 白名单',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `relation_id`(`relation_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据访问控制' ROW_FORMAT = Dynamic;
*/

@Slf4j
@Service
public class DataAccessControlService {
    private final String TABLE_DATA_ACCESS_CONTROL = "data_access_control";
    private final GenericMapper genericMapper;
    @Value("${spring.data.schema:yzlc}")
    private String SCHEMA;

    @Autowired
    private RedisCache redisCache;

    // Redis缓存相关常量
    private static final String DATA_ACCESS_CONTROL_CACHE_KEY = "data_access_control:";
    private static final Integer DATA_ACCESS_CONTROL_CACHE_TIMEOUT = 200; // 缓存200分钟

    public DataAccessControlService(GenericMapper genericMapper) {
        this.genericMapper = genericMapper;
    }

    public List<Map<String, Object>> getTableList() {
        List<Map<String, Object>> tableList = genericMapper.getList(
                "information_schema.tables",
                "table_name",
                List.<String[]>of(new String[]{"equal", "table_schema", SCHEMA}),
                "order by table_name"
        );
        if (tableList.isEmpty()) {
            return List.of();
        }
        return tableList;
    }

    public void remove(String id) {
        if (null == id || id.isBlank()) {
            throw new RuntimeException("DataAccessControlService.remove: id is required");
        }
        List<String[]> condition = List.<String[]>of(new String[]{"equal", "id", id});
        genericMapper.delete(TABLE_DATA_ACCESS_CONTROL, condition);
    }

    public void update(Map<String, Object> data) {
        if (!data.containsKey("id")) {
            throw new RuntimeException("DataAccessControlService.update: id is required");
        }
        Map<String, Object> condition = new HashMap<>();
        condition.put("id", data.get("id").toString());
        data.remove("id");
        genericMapper.update(TABLE_DATA_ACCESS_CONTROL, data, condition);
    }

    public Map<String, Object> get(String id) {
        if (null == id || id.isBlank()) {
            throw new RuntimeException("DataAccessControlService.get: id is required");
        }
        List<String[]> condition = List.<String[]>of(new String[]{"equal", "id", id});
        List<Map<String, Object>> list = genericMapper.getList(TABLE_DATA_ACCESS_CONTROL, "*", condition, "limit 1");
        if (list.isEmpty()) {
            return Map.of();
        }
        return list.get(0);
    }

    public Map<String, Object> getList(
            String type,
            String relationID,
            String referenceTable,
            String referenceColumn,
            String pageSize,
            String pageNum
    ) {
        List<String[]> condition = new ArrayList<>();
        if (!type.isBlank()) {
            condition.add(new String[]{"equal", "type", type});
        }
        if (null != relationID && !relationID.isBlank()) {
            condition.add(new String[]{"equal", "relation_id", relationID});
        }
        if (null != referenceTable && !referenceTable.isBlank()) {
            condition.add(new String[]{"equal", "reference", referenceTable});
        }
        if (null != referenceColumn && !referenceColumn.isBlank()) {
            condition.add(new String[]{"equal", "reference_detail->>'$.column'", referenceColumn});
        }
        List<Map<String, Object>> list = genericMapper.getList(
                TABLE_DATA_ACCESS_CONTROL,
                "*",
                condition,
                "limit " + ((Integer.parseInt(pageNum) - 1) * Integer.parseInt(pageSize)) + "," + pageSize
        );
        if (list.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        List<Map<String, Object>> total = genericMapper.getList(
                TABLE_DATA_ACCESS_CONTROL,
                "count(*) as total",
                condition,
                null
        );
        return Map.of(
                "rows", list,
                "total", total.isEmpty() ? 0 : total.get(0).get("total")
        );
    }

    public void create(Map<String, Object> data) {
        data.put("id", IdUtil.getSnowflakeNextId());

        Map<String, Object> dataStateMap = new HashMap<>();
        dataStateMap.put("created_at", DateUtil.formatDateTime(new Date()));
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String dataState;
            dataState = objectMapper.writeValueAsString(dataStateMap);
            data.put("data_state", dataState);
        } catch (Exception e) {
            data.put("data_state", "{}");
        }

        genericMapper.create(TABLE_DATA_ACCESS_CONTROL, data);
    }

    /**
     * 过滤指定用户对指定表中的数据的可访问内容
     *
     * @param userID    用户ID
     * @param data      待过滤的数据列表，方法将直接修改此参数
     * @param tableName 表名
     */
    public void filterAccessibleData(String userID, List<Map<String, Object>> data, String tableName) {
        if (userID == null || data == null || tableName == null) {
            throw new RuntimeException("必须提供用户ID、数据和表名");
        }

        // 构建缓存键
        String cacheKey = DATA_ACCESS_CONTROL_CACHE_KEY + userID + ":" + tableName;

        // 尝试从缓存获取数据访问控制列表
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> dacList = redisCache.getCacheObject(cacheKey);

        // 如果缓存中没有数据，则从数据库获取并存入缓存
        if (dacList == null) {
            log.info("从数据库获取用户[{}]表[{}]的数据访问控制规则", userID, tableName);

            List<String[]> condition = new ArrayList<>();
            if (!userID.isBlank()) {
                condition.add(new String[]{"equal", "relation_id", userID});
            }
            if (!tableName.isBlank()) {
                condition.add(new String[]{"equal", "reference", tableName});
            }

            dacList = genericMapper.getList(
                    TABLE_DATA_ACCESS_CONTROL,
                    "id, type, relation_id, reference, reference_detail->>'$.type' reference_type, reference_detail->>'$.column' reference_column, reference_detail->>'$.value' reference_value",
                    condition,
                    ""
            );

            // 将查询结果存入缓存
            if (dacList != null && !dacList.isEmpty()) {
                redisCache.setCacheObject(cacheKey, dacList, DATA_ACCESS_CONTROL_CACHE_TIMEOUT, TimeUnit.MINUTES);
                log.info("用户[{}]表[{}]的数据访问控制规则已存入缓存，有效期{}分钟", userID, tableName, DATA_ACCESS_CONTROL_CACHE_TIMEOUT);
            }
        } else {
            log.info("从缓存获取用户[{}]表[{}]的数据访问控制规则", userID, tableName);
        }

        if (dacList == null || dacList.isEmpty()) {
            return;
        }

        // 查询用户的行级访问控制规则
        List<Map<String, Object>> rowAccessControls = dacList.stream()
                .filter(dac -> "数据行".equals(dac.get("reference_type")))
                .toList();

        // 查询用户的列级访问控制规则
        List<Map<String, Object>> columnAccessControls = dacList.stream()
                .filter(dac -> "数据列".equals(dac.get("reference_type")))
                .toList();

        // 如果没有访问控制规则，则不需要过滤
        if (rowAccessControls.isEmpty() && columnAccessControls.isEmpty()) {
            return;
        }

        // 处理行级访问控制
        if (!rowAccessControls.isEmpty()) {
            Map<String, Set<String>> whitelistRows = new HashMap<>();
            Map<String, Set<String>> blacklistRows = new HashMap<>();

            for (Map<String, Object> control : rowAccessControls) {
                String referenceColumn = (String) control.get("reference_column");
                String referenceValue = (String) control.get("reference_value");
                String type = (String) control.get("type");

                if ("白名单".equals(type)) {
                    whitelistRows.computeIfAbsent(referenceColumn, k -> new HashSet<>()).add(referenceValue);
                } else if ("黑名单".equals(type)) {
                    blacklistRows.computeIfAbsent(referenceColumn, k -> new HashSet<>()).add(referenceValue);
                }
            }

            // 使用迭代器安全地删除无权限访问的行
            Iterator<Map<String, Object>> iterator = data.iterator();
            while (iterator.hasNext()) {
                Map<String, Object> row = iterator.next();

                if (!whitelistRows.isEmpty()) {
                    boolean inWhitelist = false;
                    for (Map.Entry<String, Set<String>> entry : whitelistRows.entrySet()) {
                        String column = entry.getKey();
                        Set<String> allowedValues = entry.getValue();
                        if (row.containsKey(column) && allowedValues.contains(String.valueOf(row.get(column)))) {
                            inWhitelist = true;
                            break;
                        }
                    }
                    if (!inWhitelist) {
                        iterator.remove();
                        continue;
                    }
                }

                for (Map.Entry<String, Set<String>> entry : blacklistRows.entrySet()) {
                    String column = entry.getKey();
                    Set<String> blockedValues = entry.getValue();
                    if (row.containsKey(column) && blockedValues.contains(String.valueOf(row.get(column)))) {
                        iterator.remove();
                        break;
                    }
                }
            }
        }

        // 处理列级访问控制
        if (!columnAccessControls.isEmpty() && !data.isEmpty()) {
            Set<String> whitelistColumns = new HashSet<>();
            Set<String> blacklistColumns = new HashSet<>();

            for (Map<String, Object> control : columnAccessControls) {
                String column = (String) control.get("reference_column");
                String type = (String) control.get("type");

                if ("白名单".equals(type)) {
                    whitelistColumns.add(column);
                } else if ("黑名单".equals(type)) {
                    blacklistColumns.add(column);
                }
            }

            for (Map<String, Object> row : data) {
                Set<String> keysToRemove = getStrings(row, whitelistColumns, blacklistColumns);

                for (String key : keysToRemove) {
                    row.remove(key);
                }
            }
        }
    }

    private static Set<String> getStrings(Map<String, Object> row, Set<String> whitelistColumns, Set<String> blacklistColumns) {
        Set<String> keysToRemove = new HashSet<>();

        for (String key : row.keySet()) {
            // 如果存在白名单，则移除不在白名单中的列
            if (!whitelistColumns.isEmpty() && !whitelistColumns.contains(key)) {
                keysToRemove.add(key);
                continue;
            }

            // 如果列在黑名单中，则移除
            if (blacklistColumns.contains(key)) {
                keysToRemove.add(key);
            }
        }
        return keysToRemove;
    }
}
