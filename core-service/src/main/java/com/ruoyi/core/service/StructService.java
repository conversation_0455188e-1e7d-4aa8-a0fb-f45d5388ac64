package com.ruoyi.core.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.entity.PsoMainNode;
import com.ruoyi.core.entity.PsoNodeNet;
import com.ruoyi.core.entity.RoleAuthStruct;
import com.ruoyi.core.mapper.GenericMapper;
import com.ruoyi.core.mapper.PsoMainNodeMapper;
import com.ruoyi.core.mapper.PsoNodeNetMapper;
import com.ruoyi.core.sqltemplate.SqlTemplate;
import com.ruoyi.core.strategy.factory.LeafDataSqlStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.ruoyi.core.constants.PsoConstant.*;
import static com.ruoyi.core.service.LogService.MODULE_NODE;

@Slf4j
@Service
public class StructService {

    private final PsoMainNodeMapper psoMainNodeMapper;
    private final PsoNodeNetMapper psoNodeNetMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final LogService logService;
    private final GenericMapper genericMapper;
    private final LeafDataSqlStrategyFactory leafDataSqlStrategyFactory;

    public StructService(PsoMainNodeMapper psoMainNodeMapper, NamedParameterJdbcTemplate namedParameterJdbcTemplate, PsoNodeNetMapper psoNodeNetMapper, LogService logService, GenericMapper genericMapper, LeafDataSqlStrategyFactory leafDataSqlStrategyFactory) {
        this.psoMainNodeMapper = psoMainNodeMapper;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.psoNodeNetMapper = psoNodeNetMapper;
        this.logService = logService;
        this.genericMapper = genericMapper;
        this.leafDataSqlStrategyFactory = leafDataSqlStrategyFactory;
    }

    /**
     * @description 表单左侧树形菜单
     * <AUTHOR>
     */
    public List<Map<String, Object>> treeInfo(String nodeId, String nodeType, Integer leaf, Integer bind) {
        MapSqlParameterSource params = new MapSqlParameterSource()
                .addValue("node_type", nodeType)
                .addValue("app_id", APP_ID);
        // 动态拼接sql查询条件
        StringBuilder condition = new StringBuilder();
        if (StrUtil.isNotBlank(nodeId)) {
            condition.append("""
                    AND (node_path LIKE CONCAT('%', :node_path, '%') OR ua.node_id = :node_id)
                    """);
            params.addValue("node_path", nodeId)  // LIKE 拼接 % 实现模糊查询
                    .addValue("node_id", nodeId);
        }
        // 获取sql语句,动态替换查询参数
        String sql = SqlTemplate.treeInfoSqlTemplate(condition, StrUtil.isBlank(APP_ID));
        // 执行查询语句
        List<Map<String, Object>> result = namedParameterJdbcTemplate.queryForList(sql, params);
        if (CollectionUtil.isNotEmpty(result)) {
            if (Objects.equals(leaf, IS_LEAF)) {
                leafProcess(result, leaf, bind, nodeId, nodeType);
            }
            result.sort(Comparator.comparingInt(this::getNodeOrder));
        }
        return result;
    }

    private void leafProcess(List<Map<String, Object>> result, Integer leaf, Integer bind, String nodeId, String nodeType) {
        if (Objects.equals(leaf, IS_LEAF)) {
            // 根据nodeId划分为Map集合
            Map<String, Object> map = new HashMap<>();
            for (Map<String, Object> objectMap : result) {
                map.put(objectMap.get("node_id").toString(), objectMap);
            }
            // TODO 预留判断用户是否为管理员权限
            List<String> idList = map.keySet().stream().toList();
            if (StrUtil.isNotBlank(nodeId) && !idList.contains(nodeId)) {
                idList.add(nodeId);
            }
            String leafDataSql = leafDataSqlStrategyFactory.getStrategy(nodeType).buildSql(StrUtil.isBlank(APP_ID));
            MapSqlParameterSource leafParams = new MapSqlParameterSource()
                    .addValue("tag", nodeType)
                    .addValue("app_id", APP_ID);
            List<Map<String, Object>> leafList = namedParameterJdbcTemplate.queryForList(leafDataSql, leafParams);
            if (CollectionUtil.isNotEmpty(leafList)) {
                bindProcess(result, leafList, bind);
            }
        }
    }

    private void bindProcess(List<Map<String, Object>> result, List<Map<String, Object>> leafList, Integer bind) {
        // bind为 1表示将未绑定节点的数据移除,为其他则不做判断
        if (Objects.equals(bind, IS_BIND)) {
            //过滤绑定的节点，非绑定的节点移除
            List<String> nodeIds = result.stream()
                    .map(x -> x.get("node_id"))
                    .filter(Objects::nonNull)  // 过滤掉null值
                    .map(Object::toString)    // 确保转换为String
                    .toList();
            nodePathHandle(leafList, nodeIds, result);
        } else {
            result.addAll(leafList);
        }
    }

    private void nodePathHandle(List<Map<String, Object>> leafList, List<String> nodeIds, List<Map<String, Object>> result) {
        // 获取node_path是否包含某个元素节点(原代码逻辑)
        for (Map<String, Object> leafData : leafList) {
            String nodePid = (String) leafData.get("node_pid");
            String nodePath = (String) leafData.get("node_path");
            if (nodeIds.contains(nodePid)) {
                result.add(leafData);
                continue;
            }
            if (StrUtil.isNotBlank(nodePath)) {
                for (String id : nodeIds) {
                    if (nodePath.contains(id)) {
                        result.add(leafData);
                        break;
                    }
                }
            }
        }
    }

    /**
     * @description 对结果集根据node_order进行排序，如果没有值则默认254
     * <AUTHOR>
     */
    private int getNodeOrder(Map<String, Object> map) {
        try {
            return map.get("node_order") != null ?
                    Integer.parseInt(map.get("node_order").toString()) :
                    DEFAULT_ORDER_VALUE;
        } catch (NumberFormatException e) {
            return DEFAULT_ORDER_VALUE;
        }
    }

    /**
     * @description 新增文件夹/目录/节点
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(Map<String, Object> params) {
        CopyOptions options = CopyOptions.create()
                // 转换时，是否忽略 null值
                .setIgnoreNullValue(true)
                // 转换时，是否进行驼峰转换
                .setIgnoreCase(true);
        // 创建节点信息对象
        PsoMainNode nodePO = BeanUtil.toBean(params, PsoMainNode.class, options);
        String username = SecurityUtils.getUsername();
        // 主键id使用雪花算法生成id
        String nodeId = IdUtil.getSnowflakeNextIdStr();
        nodePO.setNodeId(nodeId)
                .setCreator(username)
                .setNodeSname(
                        Objects.nonNull(params.get("node_sname")) &&
                                StrUtil.isBlank(String.valueOf(params.get("node_sname"))) ? params.get("node_name").toString() : params.get("node_sname").toString()
                );
        // 创建节点关系网对象
        PsoNodeNet psoNodeNet = new PsoNodeNet()
                .setNodeId(nodeId)
                .setNodePid(params.get("pid").toString());
        // 处理节点的node_path
        nodePathHandle(params.get("pid").toString(), nodePO, psoNodeNet);
        // 创建节点权限对象
        RoleAuthStruct roleAuthStruct = new RoleAuthStruct()
                .setAutoNo(IdUtil.getSnowflakeNextIdStr())
                .setRoleId("up" + APP_SYS_ROLE)
                .setAuthType(params.get("struct_type").toString())
                .setNodeType(nodePO.getNodeType())
                .setNodeId(nodeId);
        psoMainNodeMapper.insert(nodePO);
        psoNodeNetMapper.insert(psoNodeNet);
        psoMainNodeMapper.insertRoleAuthStruct(roleAuthStruct);
        // 记录操作日志
//        logService.asyncSavePsoLog(new PsoOperLog()
//                .setLogType(INSERT_NODE_LOG_TYPE)
//                .setLogUser(username)
//                .setLogContent(
//                        StrUtil.format("添加树节点：{}-{}", nodePO.getNodeType(), nodeId)
//                )
//                .setDataCode(NODE_LOG_DATA_CODE)
//                .setLeafId(nodeId), params);
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_NODE, "添加树节点", nodePO, null);
        return nodeId;
    }

    /**
     * @description node_path处理
     * <AUTHOR>
     */
    private void nodePathHandle(String pid, PsoMainNode nodePO, PsoNodeNet psoNodeNet) {
        if (StrUtil.isNotBlank(pid) && pid.equals(NODE_PATH_ROOT)) {
            psoNodeNet.setNodePath(NODE_PATH_ROOT);
        } else {
            Map<String, Object> parentNode = psoMainNodeMapper.selectNodeInfo(pid);
            if (MapUtil.isNotEmpty(parentNode)) {
                nodePO.setNodeType(parentNode.get("node_type").toString());
                psoNodeNet.setNodePath(parentNode.get("node_path") + "-" + parentNode.get("node_id"));
            } else {
                psoNodeNet.setNodePath(NODE_PATH_ROOT);
            }
        }
    }

    /**
     * @description 节点详情
     * <AUTHOR>
     */
    public Map<String, Object> nodeInfo(String nodeId) {
        return psoMainNodeMapper.nodeInfo(nodeId);
    }

    /**
     * @description 编辑文件夹/目录/节点
     * <AUTHOR>
     */
    public Integer update(String nodeId, Map<String, Object> params) {
        params.put("node_id", nodeId);
        int update = psoMainNodeMapper.update(params);
        // 记录操作日志
//        logService.asyncSavePsoLog(new PsoOperLog()
//                .setLogType(UPDATE_NODE_LOG_TYPE)
//                .setLogUser(SecurityUtils.getUsername())
//                .setLogContent(
//                        StrUtil.format("修改树节点：{}-{}", params.get("node_type"), nodeId)
//                )
//                .setDataCode(NODE_LOG_DATA_CODE)
//                .setLeafId(nodeId), params);
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_NODE, "修改树节点", params, null);
        return update;
    }

    /**
     * @description 删除文件夹/目录/节点
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseEntity<Object> delete(String nodeId) {
        // 检查是否有子节点
        int childCount = psoMainNodeMapper.countChildrenNodes(nodeId);
        if (childCount > 0) {
            return ResponseEntity.badRequest().body(Map.of(
                    "msg", "该节点存在子节点，无法删除"
            ));
        }
        // 检查是否有节点数据
        int dataCount = psoMainNodeMapper.countNodeData(nodeId);
        if (dataCount > 0) {
            return ResponseEntity.badRequest().body(Map.of(
                    "msg", "该节点存在子节点，无法删除"
            ));
        }
        psoNodeNetMapper.deleteNodeNet(nodeId);
        int delete = psoMainNodeMapper.deleteMainNode(nodeId);
        // 记录操作日志
//        logService.asyncSavePsoLog(new PsoOperLog()
//                .setLogType(DELETE_NODE_LOG_TYPE)
//                .setLogUser(SecurityUtils.getUsername())
//                .setLogContent(
//                        StrUtil.format("删除树节点：{}", nodeId)
//                )
//                .setDataCode(NODE_LOG_DATA_CODE)
//                .setLeafId(nodeId), Map.of("nodeId", nodeId));
        logService.saveLog(SecurityUtils.getUserId().toString(), MODULE_NODE, "删除树节点", nodeId, null);
        return ResponseEntity.ok(delete);
    }

    /**
     * 移动树节点
     *
     * <AUTHOR>
     */
    @Transactional
    public Object moveData(Map<String, Object> params) {
        String nodeId = params.get("node_id").toString();
        String nodePid = params.get("node_pid").toString();
        String nodeType = params.get("node_type").toString();
        String idsStr = params.get("ids").toString();
        String[] idsArr = idsStr.split(",");

        try {
            // 检查是否为叶子节点
            if (params.containsKey("leaf") && "1".equals(params.get("leaf"))) {
                // 叶子节点处理逻辑
                boolean isBind = checkBind(nodeId, nodeType);
                if (isBind) {
                    MapSqlParameterSource updateParams = new MapSqlParameterSource();
                    updateParams.addValue("node_id", nodePid);
                    updateParams.addValue("dataid", nodeId);
                    updateParams.addValue("datatag", nodeType);
                    namedParameterJdbcTemplate.update(
                            """
                                    UPDATE PSO_NODE_DATA SET node_id = :node_id, data_order = 255 
                                    WHERE node_data_id = :dataid AND node_data_tag = :datatag
                                    """,
                            updateParams
                    );
                } else {
                    // 创建新绑定
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("node_id", nodePid);
                    dataMap.put("node_data_tag", nodeType);
                    dataMap.put("node_data_id", nodeId);
                    dataMap.put("data_order", 255);
                    genericMapper.create("PSO_NODE_DATA", dataMap);
                }
                // 更新顺序
                for (int num = 0; num < idsArr.length; num++) {
                    MapSqlParameterSource orderParams = new MapSqlParameterSource();
                    orderParams.addValue("nodeid", nodePid);
                    orderParams.addValue("data_order", num);
                    orderParams.addValue("dataid", idsArr[num]);

                    namedParameterJdbcTemplate.update(
                            "UPDATE PSO_NODE_DATA SET data_order = :data_order " +
                                    "WHERE node_id = :nodeid AND node_data_id = :dataid",
                            orderParams
                    );
                }
                return 1;
            } else {
                // 非叶子节点处理逻辑
                // 获取节点信息
                Map<String, Object> entity = getNodeInfo(nodePid);
                Map<String, Object> nodeNet = getNodeNet(nodeId);
                if (nodeNet != null && !nodeNet.get("node_pid").toString().equals(nodePid)) {
                    // 跨层移动处理
                    MapSqlParameterSource netParams = new MapSqlParameterSource();
                    netParams.addValue("nodeid", nodeId);
                    netParams.addValue("nodepid", nodePid);

                    if (entity == null) {
                        netParams.addValue("nodepath", "root");
                    } else {
                        netParams.addValue("nodepath", entity.get("node_path") + "-" + nodePid);
                    }

                    namedParameterJdbcTemplate.update(
                            "UPDATE PSO_NODE_NET SET node_pid = :nodepid, node_path = :nodepath " +
                                    "WHERE node_id = :nodeid",
                            netParams
                    );
                }
                // 跨层移动处理，更新顺序
                for (int num = 0; num < idsArr.length; num++) {
                    MapSqlParameterSource orderParams = new MapSqlParameterSource();
                    orderParams.addValue("nodeid", idsArr[num]);
                    orderParams.addValue("node_order", num);

                    namedParameterJdbcTemplate.update(
                            "UPDATE PSO_NODE_NET SET node_order = :node_order " +
                                    "WHERE node_id = :nodeid",
                            orderParams
                    );
                }
            }
            return 1;
        } catch (Exception e) {
            log.error("移动节点出错: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取节点网络信息
     */
    private Map<String, Object> getNodeNet(String nodeId) {
        try {
            MapSqlParameterSource parameters = new MapSqlParameterSource();
            parameters.addValue("nodeid", nodeId);

            List<Map<String, Object>> result = namedParameterJdbcTemplate.queryForList(
                    "SELECT ta.*, tb.node_pid, tb.node_path, tc.node_path pid_path " +
                            "FROM pso_main_node ta " +
                            "LEFT JOIN pso_node_net tb ON ta.node_id = tb.node_id " +
                            "LEFT JOIN pso_node_net tc ON tb.node_pid = tc.node_id " +
                            "WHERE ta.node_id = :nodeid",
                    parameters
            );

            return result.isEmpty() ? null : result.get(0);
        } catch (Exception e) {
            log.error("获取节点网络信息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查是否已绑定
     */
    private boolean checkBind(String dataId, String type) {
        try {
            MapSqlParameterSource parameters = new MapSqlParameterSource();
            parameters.addValue("dataid", dataId);
            parameters.addValue("datatag", type);

            Integer count = namedParameterJdbcTemplate.queryForObject(
                    "SELECT COUNT(1) FROM pso_node_data WHERE node_data_id = :dataid AND node_data_tag = :datatag",
                    parameters,
                    Integer.class
            );

            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查绑定关系失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取节点信息
     */
    private Map<String, Object> getNodeInfo(String nodeId) {
        try {
            MapSqlParameterSource parameters = new MapSqlParameterSource();
            parameters.addValue("node_id", nodeId);

            List<Map<String, Object>> result = namedParameterJdbcTemplate.queryForList(
                    "SELECT ta.*, tb.node_pid, node_path, node_order FROM PSO_MAIN_NODE ta " +
                            "LEFT JOIN pso_node_net tb ON ta.node_id = tb.node_id " +
                            "WHERE ta.node_id = :node_id",
                    parameters
            );

            return result.isEmpty() ? null : result.get(0);
        } catch (Exception e) {
            log.error("获取节点信息失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
