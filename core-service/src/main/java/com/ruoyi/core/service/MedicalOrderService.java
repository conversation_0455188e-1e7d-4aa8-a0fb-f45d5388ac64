package com.ruoyi.core.service;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.mapper.GenericMapper;
import com.ruoyi.core.utility.Number2String;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class MedicalOrderService {
    private static final String TABLE_RECORD = "data_ext_menzbl";
    private static final String TABLE_ORDER = "doctor_order";
    private static final String TABLE_ORDER_1 = "medication_order";
    private static final String TABLE_ORDER_2 = "external_rx";
    private static final String TABLE_ORDER_3 = "clinical_order";
    private static final String TABLE_ORDER_4 = "other_medical_order";
    private final GenericMapper genericMapper;

    public MedicalOrderService(GenericMapper genericMapper) {
        this.genericMapper = genericMapper;
    }

    public void remove(String id) {
        genericMapper.update(TABLE_ORDER, Map.of("is_deleted", 1), Map.of("id", id));
    }

    public void switchStopState(String id, String state) {
        if ("1".equals(state)) {
            genericMapper.update(TABLE_ORDER, Map.of(
                    "is_stopped", state,
                    "stopped_by", SecurityUtils.getUsername(),
                    "stopped_time", DateUtil.formatDateTime(new Date())
            ), Map.of("id", id));
        } else if ("0".equals(state)) {
            genericMapper.update(TABLE_ORDER, Map.of(
                    "is_stopped", state,
                    "stopped_by", "",
                    "stopped_time", "0000-01-01 00:00:00"
            ), Map.of("id", id));
        } else {
            throw new IllegalArgumentException("Invalid state: " + state);
        }
    }

    /**
     * 核对医嘱（护理）
     *
     * @param parameter 查询参数，必须包含 id
     * @param data      数据，包含执行医嘱所需的其他信息
     */
    public void update4Check(Map<String, String> parameter, Map<String, Object> data) {
        String id = parameter.get("id");
        if (id == null || id.isBlank()) {
            throw new IllegalArgumentException("ID cannot be null or blank");
        }
        List<Map<String, Object>> orderList = genericMapper.getList(
                TABLE_ORDER,
                "*",
                List.<String[]>of(new String[]{"equal", "id", id}),
                "limit 1"
        );
        if (orderList.isEmpty()) {
            throw new IllegalArgumentException("Order with ID " + id + " not found");
        }

        Map<String, Object> updateData = Map.of(
                "status", "2",
                "enrolled_nurse", SecurityUtils.getUsername(),
                "updated_time", DateUtil.formatDateTime(new Date())
        );

        genericMapper.update(TABLE_ORDER, updateData, Map.of("id", id));
    }

    /**
     * 执行医嘱（护理）
     *
     * @param parameter 查询参数，必须包含 id
     * @param data      数据，包含执行医嘱所需的其他信息
     */
    public void update4CarryOut(Map<String, String> parameter, Map<String, Object> data) {
        String id = parameter.get("id");
        if (id == null || id.isBlank()) {
            throw new IllegalArgumentException("ID cannot be null or blank");
        }
        List<Map<String, Object>> orderList = genericMapper.getList(
                TABLE_ORDER,
                "*",
                List.<String[]>of(new String[]{"equal", "id", id}),
                "limit 1"
        );
        if (orderList.isEmpty()) {
            throw new IllegalArgumentException("Order with ID " + id + " not found");
        }

        Map<String, Object> updateData = Map.of(
                "status", "1",
                "enrolled_nurse", SecurityUtils.getUsername(),
                "updated_time", DateUtil.formatDateTime(new Date())
        );

        genericMapper.update(TABLE_ORDER, updateData, Map.of("id", id));
    }

    public void updateOrderContent(String id, String content) {
        genericMapper.update(TABLE_ORDER, Map.of("content", content), Map.of("id", id));
    }

    /**
     * 按住院号查询长期医嘱核对（护理）数据列表
     *
     * @param p 查询参数，必须包含 sn（住院号）
     * @return 数据 { rows: [], total: 0 }
     */
    public Map<String, Object> getList4CheckStandingBySN(Map<String, String> p) {
        if (null == p.get("sn") || p.get("sn").isBlank()) {
            throw new IllegalArgumentException("住院号不能为空");
        }
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "is_deleted", "0"});
        conditions.add(new String[]{"equal", "order_type", "长期"});
        conditions.add(new String[]{"equal", "hospitalization_id", p.get("sn")});
        List<Map<String, Object>> countResult = genericMapper.getList(
                TABLE_ORDER,
                "count(*) as total",
                conditions,
                ""
        );
        if (countResult.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        String total = countResult.get(0).get("total").toString();
        if ("0".equals(total)) {
            return Map.of("rows", List.of(), "total", 0);
        }

        List<Map<String, Object>> orderList = genericMapper.getList(
                TABLE_ORDER,
                "*",
                conditions,
                "order by order_time limit " + p.getOrDefault("skip", "0") + "," + p.getOrDefault("take", "10")
        );
        if (orderList.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        orderList = Number2String.number2String4ListMap.apply(orderList);

        return Map.of("rows", orderList, "total", total);
    }

    /**
     * 长期医嘱核对（护理） 数据列表
     *
     * @param p 查询参数
     * @return 数据 { rows: [], total: 0 }
     */
    public Map<String, Object> getList4CheckStanding(Map<String, String> p) {
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "is_deleted", "0"});
        conditions.add(new String[]{"equal", "order_type", "长期"});
        if (p.get("area") != null && !p.get("area").isBlank()) {
            conditions.add(new String[]{"equal", "area", p.get("area")});
        }
        List<Map<String, Object>> countResult = genericMapper.getList(
                TABLE_ORDER,
                "count(*) as total",
                conditions,
                ""
        );
        if (countResult.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        String total = countResult.get(0).get("total").toString();
        if ("0".equals(total)) {
            return Map.of("rows", List.of(), "total", 0);
        }

        List<Map<String, Object>> orderList = genericMapper.getList(
                TABLE_ORDER,
                "distinct patient_name,area,bed_number,hospitalization_id",
                conditions,
                "limit " + p.getOrDefault("skip", "0") + "," + p.getOrDefault("take", "10")
        );
        if (orderList.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        orderList = Number2String.number2String4ListMap.apply(orderList);

        return Map.of("rows", orderList, "total", Integer.valueOf(total));
    }

    /**
     * 新开医嘱核对（护理）
     *
     * @param p 查询参数
     * @return 数据 { rows: [], total: 0 }
     */
    public Map<String, Object> getList4Check(Map<String, String> p) {
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "is_deleted", "0"});
        conditions.add(new String[]{"equal", "status", "1"});
        if (p.get("area") != null && !p.get("area").isBlank()) {
            conditions.add(new String[]{"equal", "area", p.get("area")});
        }
        if (p.get("name") != null && !p.get("name").isBlank()) {
            conditions.add(new String[]{"equal", "patient_name", p.get("name")});
        }
        if (p.get("type") != null && !p.get("type").isBlank()) {
            conditions.add(new String[]{"equal", "order_type", p.get("type")});
        }
        if (p.get("dateBegin") != null && !p.get("dateBegin").isBlank()) {
            conditions.add(new String[]{"greater-equal", "created_time", p.get("dateBegin")});
        }
        if (p.get("dateEnd") != null && !p.get("dateEnd").isBlank()) {
            conditions.add(new String[]{"less-equal", "created_time", p.get("dateEnd")});
        }
        List<Map<String, Object>> countResult = genericMapper.getList(
                TABLE_ORDER,
                "count(*) as total",
                conditions,
                ""
        );
        if (countResult.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        String total = countResult.get(0).get("total").toString();
        if ("0".equals(total)) {
            return Map.of("rows", List.of(), "total", 0);
        }

        List<Map<String, Object>> orderList = genericMapper.getList(
                TABLE_ORDER,
                "*",
                conditions,
                "order by created_time limit " + p.getOrDefault("skip", "0") + "," + p.getOrDefault("take", "10")
        );
        if (orderList.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        orderList = Number2String.number2String4ListMap.apply(orderList);

        return Map.of("rows", orderList, "total", Integer.valueOf(total));
    }

    /**
     * 新开医嘱执行（护理）
     *
     * @param p 查询参数
     * @return 数据 { rows: [], total: 0 }
     */
    public Map<String, Object> getList4CarryOut(Map<String, String> p) {
        List<String[]> conditions = new ArrayList<>();
        conditions.add(new String[]{"equal", "is_deleted", "0"});
        conditions.add(new String[]{"equal", "status", "0"});
        if (p.get("area") != null && !p.get("area").isBlank()) {
            conditions.add(new String[]{"equal", "area", p.get("area")});
        }
        if (p.get("name") != null && !p.get("name").isBlank()) {
            conditions.add(new String[]{"equal", "patient_name", p.get("name")});
        }
        if (p.get("type") != null && !p.get("type").isBlank()) {
            conditions.add(new String[]{"equal", "order_type", p.get("type")});
        }
        if (p.get("dateBegin") != null && !p.get("dateBegin").isBlank()) {
            conditions.add(new String[]{"greater-equal", "created_time", p.get("dateBegin")});
        }
        if (p.get("dateEnd") != null && !p.get("dateEnd").isBlank()) {
            conditions.add(new String[]{"less-equal", "created_time", p.get("dateEnd")});
        }
        List<Map<String, Object>> countResult = genericMapper.getList(
                TABLE_ORDER,
                "count(*) as total",
                conditions,
                ""
        );
        if (countResult.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        String total = countResult.get(0).get("total").toString();
        if ("0".equals(total)) {
            return Map.of("rows", List.of(), "total", 0);
        }

        List<Map<String, Object>> orderList = genericMapper.getList(
                TABLE_ORDER,
                "*",
                conditions,
                "order by created_time limit " + p.getOrDefault("skip", "0") + "," + p.getOrDefault("take", "10")
        );
        if (orderList.isEmpty()) {
            return Map.of("rows", List.of(), "total", 0);
        }
        orderList = Number2String.number2String4ListMap.apply(orderList);

        return Map.of("rows", orderList, "total", Integer.valueOf(total));
    }

    public List<Map<String, Object>> getList(Map<String, Object> p) {
        List<String[]> condition = new ArrayList<>();
        if (p.get("is_deleted") != null && !p.get("is_deleted").toString().isBlank()) {
            condition.add(new String[]{"equal", "is_deleted", p.get("is_deleted").toString()});
        } else {
            condition.add(new String[]{"equal", "is_deleted", "0"});
        }
        if (p.get("type") != null && !p.get("type").toString().isBlank()) {
            condition.add(new String[]{"equal", "order_type", (String) p.get("type")});
            if (p.get("type").toString().equals("长期")){
                condition.add(new String[]{"equal", "status", "2"});
            }
        }
        if (p.get("stopped") != null && !p.get("stopped").toString().isBlank()) {
            if (p.get("stopped").toString().equals("true")){
                condition.add(new String[]{"equal", "is_stopped", "1"});
            }
        }
        if (p.get("sn") != null && !p.get("sn").toString().isBlank()) {
            condition.add(new String[]{"equal", "hospitalization_id", (String) p.get("sn")});
        }
        if (p.get("name") != null && !p.get("name").toString().isBlank()) {
            condition.add(new String[]{"equal", "patient_name", (String) p.get("name")});
        }
        if (p.get("bed") != null && !p.get("bed").toString().isBlank()) {
            condition.add(new String[]{"equal", "bed_number", (String) p.get("bed")});
        }
        // todo 停止状态
        if (!p.get("doctor").toString().isBlank()) {
            condition.add(new String[]{"equal", "doctor_name", (String) p.get("doctor")});
        }
        if (!p.get("dateBegin").toString().isBlank()) {
            condition.add(new String[]{"greater-equal", "order_time", (String) p.get("dateBegin")});
        }
        if (!p.get("dateEnd").toString().isBlank()) {
            condition.add(new String[]{"less-equal", "order_time", (String) p.get("dateEnd")});
        }
        if (!p.get("content").toString().isBlank()) {
            condition.add(new String[]{"like", "content", (String) p.get("content")});
        }
        List<Map<String, Object>> orderList = genericMapper.getList(
                TABLE_ORDER,
                "*",
                condition,
                ""
        );
        if (orderList.isEmpty()) {
            return List.of();
        }

        return orderList;
    }
}
