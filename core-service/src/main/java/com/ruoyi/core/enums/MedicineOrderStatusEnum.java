package com.ruoyi.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 用药医嘱状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MedicineOrderStatusEnum {
    NEW_ORDER(0, "已保存"),
    PRESCRIBED(1, "已开方"),
    VERIFIED(2, "已核对"),
    GENERATED(3, "已生成"),
    DISPENSED(4, "已发药"),
    RETURNED(5, "已取消"),
    PHARMACY_RETURNED(6, "药房退药"),
    WITHDRAWN(99,"已撤回");

    private final int key;
    private final String value;

    /**
     * 根据 key 获取枚举
     */
    public static MedicineOrderStatusEnum fromKey(int key) {
        return Arrays.stream(values())
                .filter(e -> e.key == key)
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据 key 获取中文值
     */
    public static String getValueByKey(int key) {
        MedicineOrderStatusEnum status = fromKey(key);
        return status != null ? status.getValue() : "未知状态";
    }
}
