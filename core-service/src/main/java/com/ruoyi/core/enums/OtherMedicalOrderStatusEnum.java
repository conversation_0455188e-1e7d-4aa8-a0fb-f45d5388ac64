package com.ruoyi.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 其他医嘱状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OtherMedicalOrderStatusEnum {
    SAVED(0, "已保存"),
    PRESCRIBED(1, "已开方"),
    VERIFIED(2, "已核对"),
    EXECUTED(3, "已执行"),
    RETURNED(4, "已取消"),
    WITHDRAWN(99,"已撤回");

    private final int key;
    private final String value;

    /**
     * 根据 key 获取枚举
     */
    public static OtherMedicalOrderStatusEnum fromKey(int key) {
        return Arrays.stream(values())
                .filter(e -> e.key == key)
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据 key 获取中文值
     */
    public static String getValueByKey(int key) {
        OtherMedicalOrderStatusEnum status = fromKey(key);
        return status != null ? status.getValue() : "未知状态";
    }
}
