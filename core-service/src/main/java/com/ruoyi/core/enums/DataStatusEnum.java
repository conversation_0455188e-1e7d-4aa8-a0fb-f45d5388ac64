package com.ruoyi.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum DataStatusEnum {
    TO_SUBMIT(0, "待提交"),
    IN_REVIEW(1, "审批中"),
    RETURNED(2, "已退回"),
    REVOKED(7, "已撤回"),
    COMPLETED(8, "已完成"),
    ARCHIVED(9, "已归档");

    private final int key;
    private final String value;

    /**
     * 根据 key 获取枚举
     */
    public static DataStatusEnum fromKey(int key) {
        return Arrays.stream(values())
                .filter(e -> e.key == key)
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据 key 获取中文值
     */
    public static String getValueByKey(int key) {
        DataStatusEnum status = fromKey(key);
        return status != null ? status.getValue() : "未知状态";
    }
}
