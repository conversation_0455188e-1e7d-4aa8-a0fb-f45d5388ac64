package com.ruoyi.core.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 给药方法频次枚举
 * <AUTHOR>
 */
public enum AdministrationFrequency {
    QD(1.0),      // 每日1次
    BID(2.0),     // 每日2次
    TID(3.0),     // 每日3次
    QID(4.0),     // 每日4次
    Q4H(6.0),     // 每4小时1次，每日6次
    Q6H(4.0),     // 每6小时1次，每日4次
    Q8H(3.0),     // 每8小时1次，每日3次
    Q12H(2.0),    // 每12小时1次，每日2次
    QN(1.0),      // 每晚1次
    QOD(3.5/7),   // 隔日1次，每周3.5次
    QW(1.0/7),    // 每周1次
    BIW(2.0/7),   // 每周2次
    TIW(3.0/7),   // 每周3次
    ONCE(1.0),    // 单次
    ST(1.0);      // 立即

    private final double frequency;

    AdministrationFrequency(double frequency) {
        this.frequency = frequency;
    }

    public double getFrequency() {
        return frequency;
    }

    /**
     * 根据字符串获取频次值
     * @param name 给药方法名称
     * @return 频次值，如果找不到则返回1.0
     */
    public static double getFrequencyByName(String name) {
        if (StrUtil.isBlank(name)) {
            return 1.0;
        }
        try {
            return AdministrationFrequency.valueOf(name.toUpperCase()).getFrequency();
        } catch (IllegalArgumentException e) {
            return 1.0; // 默认值
        }
    }

    /**
     * 根据字符串获取枚举实例
     * @param name 给药方法名称
     * @return 枚举实例，如果找不到则返回QD
     */
    public static AdministrationFrequency getByName(String name) {
        if (StrUtil.isBlank(name)) {
            return QD;
        }
        try {
            return AdministrationFrequency.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return QD; // 默认值
        }
    }
}
