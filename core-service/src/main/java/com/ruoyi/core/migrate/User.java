package com.ruoyi.core.migrate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.core.mapper.GenericMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * CREATE TABLE `db_sfy_product_3`.`Untitled`  (
 * `app_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
 * `leaf_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
 * `d_status` tinyint(4) NOT NULL,
 * `d_stage` tinyint(4) NOT NULL,
 * `d_audit` tinyint(4) NOT NULL,
 * `sync_status` tinyint(4) NOT NULL,
 * `creator` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
 * `c_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
 * `input_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `user_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
 * `user_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
 * `user_pwd` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `user_phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `user_email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `user_note` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `expire_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `user_order` int(11) NOT NULL,
 * `is_sys` int(11) NOT NULL DEFAULT 0,
 * `user_img` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `renter_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `data_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `city_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `matter_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `menu_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `tp_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `is_chain` tinyint(4) NULL DEFAULT NULL,
 * `pub_status` tinyint(4) NULL DEFAULT NULL,
 * `x_creator` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `upuser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `x_upuser` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `user_group` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `is_plat` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0',
 * `share_status` int(11) NULL DEFAULT NULL,
 * `user_sign` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `is_fill` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * `user_source` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
 * PRIMARY KEY (`leaf_id`) USING BTREE
 * ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;
 */

@RestController
@RequestMapping("/public-api/migrate")
public class User {
    final String SOURCE_DB = "db_sfy_product_3";
    final String TARGET_DB = "yzlc";
    private static final Logger log = LoggerFactory.getLogger(User.class);
    private final JdbcTemplate jdbcTemplate;
    private final GenericMapper genericMapper;

    final Function<Integer, String> parseUserStatus = value -> {
        if (value == 0) {
            return "0";
        } else if (value == 1 || value == 99) {
            return "1";
        }
        return "";
    };

    public User(JdbcTemplate jdbcTemplate, GenericMapper genericMapper) {
        this.jdbcTemplate = jdbcTemplate;
        this.genericMapper = genericMapper;
    }

    /**
     * app_id 移除
     * d_status 状态 0正常 1冻结 99删除
     * d_stage 移除
     * d_audit 移除
     * c_time 创建时间
     * input_time 修改时间
     *
     * @return result
     */
    @GetMapping("/user")
    public ResponseEntity<Object> user() {
        List<Map<String, Object>> userListSource = jdbcTemplate.queryForList("""
                select * from %s.data_ext_user
                """.formatted(SOURCE_DB));
        ObjectMapper objectMapper = new ObjectMapper();
        for (Map<String, Object> user : userListSource) {
            try {
                // 转换密码 md5 >> bcrypt
                String pwd = user.get("password").toString();
                String bcryptPassword = SecurityUtils.encryptPassword(pwd);
                boolean isPasswordMatch = SecurityUtils.matchesPassword(pwd, bcryptPassword);

                String userSourceJSON = objectMapper.writeValueAsString(user);
                Map<String, Object> newUser = new HashMap<>();
                newUser.put("user_id", user.get("user_id").toString());
                newUser.put("dept_id", 100);
                newUser.put("user_name", user.get("user_name").toString());
                newUser.put("nick_name", user.get("user_name").toString());
                newUser.put("password", isPasswordMatch ? bcryptPassword : user.get("user_pwd").toString());
                newUser.put("user_type", "10");
                newUser.put("email", null == user.get("user_email") ? "" : user.get("user_email").toString());
                newUser.put("phonenumber", null == user.get("user_phone") ? "" : user.get("user_phone").toString());
                newUser.put("status", parseUserStatus.apply(Integer.parseInt(user.get("d_status").toString())));
                newUser.put("del_flag", "99".equals(user.get("d_status").toString()) ? "2" : "0");
                newUser.put("create_by", user.get("creator").toString());
                newUser.put("create_time", user.get("c_time").toString());
                newUser.put("update_time", null == user.get("input_time") || "".equals(user.get("input_time").toString()) ? null : user.get("input_time").toString());
                newUser.put("source_json", userSourceJSON);
                genericMapper.create(TARGET_DB + ".sys_user", newUser);
            } catch (Exception e) {
                log.error("Error processing user data: {}", e.getMessage());
            }
        }
        return ResponseEntity.ok().body(userListSource);
    }
}
