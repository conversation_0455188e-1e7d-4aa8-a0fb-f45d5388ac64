package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
public enum MenuCategoryEnum {
    ADMIN_SYSTEM("admin-system", "后台管理"),
    USER_SYSTEM("user-system", "业务系统");

    private final String key;
    private final String value;

    /**
     * 根据 key 获取对应的 value
     */
    public static String getValueByKey(String key) {
        for (MenuCategoryEnum type : MenuCategoryEnum.values()) {
            if (type.getKey().equals(key)) {
                return type.getValue();
            }
        }
        return null;
    }

    MenuCategoryEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }
}