{"info": {"name": "上海市第四福利院", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "generic", "item": [{"name": "Create Table", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "http://localhost:8090/public-api/generic/table?name=test_table", "protocol": "http", "host": ["localhost"], "port": "8090", "path": ["public-api", "generic", "table"], "query": [{"key": "name", "value": "test_table"}]}, "body": {"mode": "raw", "raw": "[{\"name\": \"id\", \"type\": \"int\", \"len\": 11}, {\"name\": \"name\", \"type\": \"varchar\", \"len\": 255}, {\"name\": \"created_at\", \"type\": \"datetime\"}]"}}, "response": []}]}, {"name": "user", "item": [{"name": "用户数据组件", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8090/public-api/user?option=for-component&name=&dept=&role=&position=", "protocol": "http", "host": ["localhost"], "port": "8090", "path": ["public-api", "user"], "query": [{"key": "option", "value": "for-component"}, {"key": "name", "value": ""}, {"key": "dept", "value": ""}, {"key": "role", "value": ""}, {"key": "position", "value": ""}]}}, "response": []}]}]}