# Use Maven image to build the project
FROM docker.1ms.run/library/maven:3.9-eclipse-temurin-17 AS builder
WORKDIR /app

# 设置阿里云maven镜像
RUN echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">\n  <mirrors>\n    <mirror>\n      <id>aliyunmaven</id>\n      <mirrorOf>*</mirrorOf>\n      <name><PERSON><PERSON> Maven</name>\n      <url>https://maven.aliyun.com/repository/public</url>\n    </mirror>\n  </mirrors>\n</settings>' > /usr/share/maven/conf/settings.xml

# 创建 .m2 目录确保权限正确
RUN mkdir -p /root/.m2

# COPY .m2 /app/.m2
COPY settings.xml /app/
COPY pom.xml /app/

COPY ruoyi-admin/pom.xml ./ruoyi-admin/
COPY ruoyi-common/pom.xml ./ruoyi-common/
COPY ruoyi-framework/pom.xml ./ruoyi-framework/
COPY ruoyi-generator/pom.xml ./ruoyi-generator/
COPY ruoyi-quartz/pom.xml ./ruoyi-quartz/
COPY ruoyi-system/pom.xml ./ruoyi-system/
COPY core-service/pom.xml ./core-service/
COPY ruoyi-admin/src ./ruoyi-admin/src
COPY ruoyi-common/src ./ruoyi-common/src
COPY ruoyi-framework/src ./ruoyi-framework/src
COPY ruoyi-generator/src ./ruoyi-generator/src
COPY ruoyi-quartz/src ./ruoyi-quartz/src
COPY ruoyi-system/src ./ruoyi-system/src
COPY core-service/src ./core-service/src
# Maven 构建时会使用映射的本地仓库
RUN mvn clean package -DskipTests

# Use a minimal JRE image for running the app
FROM docker.1ms.run/library/eclipse-temurin:17-jre-alpine
WORKDIR /app
COPY --from=builder /app/ruoyi-admin/target/ruoyi-admin.jar ./ruoyi-admin.jar
COPY font ./font
COPY template ./template
EXPOSE 8090
ENTRYPOINT ["java", "-jar", "ruoyi-admin.jar"]
